# Bulk Product Edit App
Uses Remix Shopify Typescript template for Embedded Apps

## 📚 Critical Documentation

**🔧 Having Issues?** Check these first:
- **Bull Queue Not Processing?** → `docs/current-status-and-next-steps.md` - "Bull Queue Configuration Guide"
- **Database Tests Failing?** → `docs/current-status-and-next-steps.md` - "Database Testing Strategy"
- **General Status & Next Steps** → `docs/current-status-and-next-steps.md`

**💡 Key Implementation Notes:**
- **Bull Version**: Must use 3.29.3 (not 4.x) - see worker config comments
- **Database**: Uses Proxy pattern for test compatibility - see `app/db.server.ts`
- **Queue Naming**: `bpe-{sanitized}` format - see `getQueueName()` function

## 🚀 Quick Start

### Development (Recommended)
```bash
# Start everything (dependencies + app) - ONE COMMAND!
npm run dev:full
```

### Alternative Commands
```bash
# Start app only (with dependency check)
npm run dev
# → deps:check + shopify app dev

# Start dependencies manually
npm run deps:start
# → docker-compose up -d

# Check if dependencies are running
npm run deps:check
# → Tests Redis connection

# Start worker (for job processing)
npm run worker
# → deps:check + node worker.js

# Start worker with dependencies
npm run worker:dev
# → deps:start + deps:check + node worker.js
```

### Database Management
```bash
npx prisma studio # launches prisma server locally in a browser to browse DB entries.
```

### Redis Monitoring
```bash
# Show Redis Commander URL
npm run redis:commander
# → Output: Redis Commander available at http://localhost:8081 (no login required)

# View Redis logs
npm run redis:logs
# → docker-compose logs -f redis
```

### Testing
```bash
# Run tests with coverage
npm test

# Run tests in watch mode
npm run test:watch

# Manual cleanup of test databases
npm run test:cleanup
```

**Note:** Different test modes use environment variables and file naming conventions to automatically select the right tests without explicit file listing.

# shopify mcp functions:
```
shopify_admin_graphql # Help you write GraphQL operations for the Shopify Admin API
search_dev_docs # Search shopify.dev documentation
introspect_admin_schema # Access and search Shopify Admin GraphQL schema
```

## 📚 Documentation

### 🎯 **PROJECT STATUS & NEXT STEPS**
- **[📊 Current Status & Next Steps](docs/current-status-and-next-steps.md)** - **START HERE** - Complete project status and sequential implementation plan

### ✨ **RECENT FEATURES**
- **Variant Display Enhancement** (August 2025) - Enhanced product selection with expandable variant display for products with >6 variants

### Development Guides
- **[Development Guide](DEVELOPMENT.md)** - Complete development workflow and troubleshooting
- **[Testing Strategy](docs/testing-strategy.md)** - Regression prevention testing setup and best practices
- **[Redis Inspection](REDIS_INSPECTION.md)** - Redis monitoring and job queue inspection
- **[Shop Queue System](docs/shop-queue-system.md)** - Shop-based job queue architecture and monitoring
- **[Development Roadmap](docs/development-roadmap.md)** - Roadmap and phase-wise implementation guide

### Project Documentation
- [DB Doc](docs/db.md)
- [Ablestar Bulk Edit Fields (Free version)](docs/ablestarFields.md) (Only relevant fields)
- [Shopify original README](docs/shopify-README.md)
- [todos](docs/todo.md)
- [PRD](docs/PRD.md)

## 🔧 Dependencies

This app requires:
- **Redis** (for job queues) - Auto-started with `npm run dev:full`
- **Redis Commander** (for monitoring) - Auto-started with `npm run dev:full`

All dependencies are automatically managed through Docker Compose.