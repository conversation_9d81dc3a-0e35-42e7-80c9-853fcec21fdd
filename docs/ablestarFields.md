# Ablestar BPE fields

## Variant Level:
- "Barcode (ISBN, UPC, GTIN, etc.)",
- "Charge tax on this product",
- "Compare-at Price",
- "Continue Selling Inventory when Out of Stock",
- "Cost",
- "Country of Origin",
- "HS Tariff Code",
- "Inventory Level",
- "Is Physical Product",
- "Option 1 Value",
- "Option 2 Value",
- "Option 3 Value",
- "Price",
- "Province Code of Origin",
- "SKU",
- "Tax Code",
- "Track Quantity/Inventory",
- "Weight (in grams)",
- "Weight (in product's unit)",
- "Weight Unit",

## Product Level:
- "Category",
- "Description",
- "Handle (URL)",
- "Images",
- "Manual Collection",
- "Option 1 Name",
- "Option 2 Name",
- "Option 3 Name",
- "Primary Image",
- "Product Type (Custom)",
- "SEO Meta Description",
- "SEO Meta Title",
- "Status",
- "Tags",
- "Theme Template",
- "Title",
- "Vendor",

## Danger Zone:
- "Delete Products"
- "Delete Variants"


## Other fields can be derived from these files:
- [json loaded](json.json)
- [extracted few fields](less.json)