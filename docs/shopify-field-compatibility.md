# Shopify Field Compatibility Analysis

## 📊 Implementation Phases

### ✅ **PHASE 1: FULLY SUPPORTED (IMPLEMENTING NOW)**

#### **Search/Filter Fields**

**Product Level Filters:**
```javascript
// Status Filters
'status:ACTIVE'           // Active products
'status:DRAFT'            // Draft products  
'status:ARCHIVED'         // Archived products

// Text Search Filters
'title:keyword'           // Search in product title
'vendor:nike'            // Filter by vendor
'product_type:clothing'  // Filter by product type
'tag:summer'             // Filter by tags
'handle:my-product'      // Filter by handle

// Date Filters
'created_at:>2024-01-01' // Created after date
'created_at:<2024-12-31' // Created before date
'updated_at:>2024-01-01' // Updated after date
'updated_at:<2024-12-31' // Updated before date
'published_at:*'         // Has published date (is published)
```

**Variant Level Filters:**
```javascript
// SKU and Barcode
'sku:ABC123'             // Filter by SKU
'barcode:123456789'      // Filter by barcode

// Price Filters
'price:>10'              // Price greater than
'price:<50'              // Price less than
'price:10..50'           // Price range
'compare_at_price:>20'   // Compare-at price filters

// Option Filters
'option1:Red'            // Filter by option 1 value
'option2:Large'          // Filter by option 2 value
'option3:Cotton'         // Filter by option 3 value
```

#### **Edit/Update Fields**

**Product Fields (via productSet mutation):**
- ✅ `title` - Product title
- ✅ `descriptionHtml` - Product description
- ✅ `vendor` - Product vendor
- ✅ `productType` - Product type
- ✅ `handle` - URL handle
- ✅ `status` - Product status (ACTIVE/DRAFT/ARCHIVED)
- ✅ `tags` - Product tags array
- ✅ `templateSuffix` - Theme template
- ✅ `category` - Product category
- ✅ `seo.title` - SEO title
- ✅ `seo.description` - SEO description

**Variant Fields (via productSet mutation):**
- ✅ `variants.price` - Variant price
- ✅ `variants.compareAtPrice` - Compare-at price
- ✅ `variants.inventoryItem.sku` - SKU
- ✅ `variants.barcode` - Barcode
- ✅ `variants.inventoryItem.measurement.weight` - Weight
- ✅ `variants.inventoryItem.measurement.weight.unit` - Weight unit
- ✅ `variants.taxable` - Charge tax
- ✅ `variants.inventoryItem.requiresShipping` - Requires shipping
- ✅ `variants.inventoryPolicy` - Inventory policy
- ✅ `variants.inventoryQuantities` - Inventory levels
- ✅ `variants.inventoryItem.cost` - Cost per item
- ✅ `variants.inventoryItem.countryCodeOfOrigin` - Country of origin
- ✅ `variants.inventoryItem.harmonizedSystemCode` - HS tariff code
- ✅ `variants.taxCode` - Tax code
- ✅ `variants.optionValues` - Option values (Size, Color, etc.)

**Metafields (via productSet mutation):**
- ✅ Google Shopping fields (age_group, gender, color, size, material, condition, custom_labels)
- ✅ SEO metafields (title_tag, description_tag)
- ✅ Custom metafields (any namespace/key combination)

---

### ⚠️ **PHASE 2: REQUIRES TRANSLATION (LATER)**

#### **Search/Filter Fields**
```javascript
// Ablestar Syntax → Shopify Translation Needed
'product__total_inventory:>0'     → 'totalInventory:>0'
'product__collections__id:123'   → 'collection_id:123'
'inventory_quantity:>5'           → Use productVariants query
'product__total_images__gt__0'    → Check media.count > 0
```

#### **Complex Filters**
- Inventory-based filtering (translate to totalInventory)
- Collection membership filtering
- Image count filtering (via media connection)
- Variant-specific searches (requires separate productVariants query)

---

### ❌ **PHASE 3: CUSTOM LOGIC REQUIRED (FUTURE)**

#### **Computed Fields (Not in Shopify API)**
```javascript
// These require application-level computation
'barcode__duplicate'              // Duplicate detection logic
'sku__duplicate'                  // Duplicate detection logic
'product__title__duplicate'       // Duplicate detection logic
'price__lt__compare_at_price'     // Field comparison logic
'price__eq__compare_at_price'     // Field comparison logic
'price__gt__compare_at_price'     // Field comparison logic
'product__out_of_stock__true'     // Custom inventory analysis
'profit_margin'                   // (price - cost) calculation
```

#### **Advanced Analytics**
- Profit margin calculations
- Duplicate detection across fields
- Complex inventory analytics
- Cross-field comparisons
- Performance metrics

---

## 🎯 **IMPLEMENTATION PRIORITY**

**Phase 1 (Now):** 
- ✅ 15 core search filters
- ✅ 25+ edit fields
- ✅ All basic product/variant operations

**Phase 2 (Next):**
- ⚠️ 8 translated filters
- ⚠️ Advanced inventory filtering
- ⚠️ Collection-based filtering

**Phase 3 (Future):**
- ❌ 10+ computed filters
- ❌ Advanced analytics
- ❌ Custom business logic

**Coverage:** Phase 1 provides ~70% of Ablestar's core functionality with 100% Shopify API compatibility.

---

## 🚀 **IMPLEMENTED FILTERS (Phase 1)**

### **Currently Available in UI:**

**Product Status (3 filters):**
- ✅ Status: Active (`status:ACTIVE`)
- ✅ Status: Draft (`status:DRAFT`)
- ✅ Status: Archived (`status:ARCHIVED`)

**Product Fields (5 filters):**
- ✅ Has Vendor (`vendor:*`)
- ✅ Has Product Type (`product_type:*`)
- ✅ Has Tags (`tag:*`)
- ✅ Is Published (`published_at:*`)
- ✅ Has Custom Handle (`handle:*`)

**Date Filters (3 filters):**
- ✅ Created This Year (`created_at:>2024-01-01`)
- ✅ Created This Month (`created_at:>2024-12-01`)
- ✅ Updated Recently (`updated_at:>[last 7 days]`)

**Product Type (1 filter):**
- ✅ Gift Cards Only (`gift_card:true`)

**Custom Filter Support:**
- ✅ Free-text input for any Shopify search syntax
- ✅ Examples: `vendor:nike`, `tag:summer`, `title:shirt`

### **Filter Examples for Testing:**

```javascript
// Basic text searches
'title:shirt'              // Products with "shirt" in title
'vendor:nike'             // Products by Nike
'product_type:clothing'   // Clothing products
'tag:summer'              // Products tagged "summer"

// Date searches
'created_at:>2024-01-01'  // Created after Jan 1, 2024
'updated_at:<2024-12-31'  // Updated before Dec 31, 2024

// Boolean searches
'published_at:*'          // Published products
'gift_card:true'          // Gift card products

// Combined searches
'vendor:nike AND tag:summer'           // Nike summer products
'status:ACTIVE AND created_at:>2024-01-01'  // Active products created this year
```

---

## 📋 **NEXT STEPS**

1. **Test Current Implementation:**
   - Try all predefined filters
   - Test custom filter input
   - Verify search combinations work

2. **Add More Phase 1 Filters:**
   - Price range filters (`price:>10`, `price:<50`)
   - SKU/Barcode searches (`sku:ABC123`, `barcode:123456`)
   - Option value filters (`option1:Red`, `option2:Large`)

3. **Prepare for Phase 2:**
   - Inventory-based filtering
   - Collection membership
   - Image count filtering
