# Requirement Docs for Bulk Edit App:

- General Consideration:
  - Built with Typescript, remix, using shopify API compatibility (check shopify MCP for graphQL and documentation).
  - Uses Jan 2025 API. Polaris Components, App Bridge.
  - Shopify APIs (mutation, or query) need to be paginated and be able to gracefully handle rate limits.
  - Folder structre:
    - `/app/routes/` contains route files. Specially app.edit.tsx
    - `/app/components/` contains Components. Prefer Pure function components which can be easily tested and used app.edit.tsx route file.
    - `/app/gql/` contains list of graphQL files.
    - `/app/constants.ts` contains constants reusable across code. If not a component, or route based constant then it goes here.
    - `/app/types.ts` contains typescript definitions. If not component/route specific, then it goes here, and can be reusable.
    - `/prisma/schema.prisma` contains schemas, and `/prisma/migrations/` contains migrations.
    - Follow TDD , write test and code to pass that and check if regression happened.
        
- Pages:
  - **IMPLEMENTATION NOTE**: The original single-page design has been improved into a 3-page workflow for better UX:
    1. `/app/select-products` - Product filtering and selection
    2. `/app/define-modifications` - Define bulk changes
    3. `/app/create-job` - Job creation and scheduling

  - <base-url>/app/edit : **DEPRECATED** - Replaced by 3-page workflow above
    - Original specification (for reference only) comprised 4 sections:

      - Section 1: Add Filter section. 
        - Here list of filters can be specified to narrow down the product / variants (say by optionName and optionValue, like size as small, medium, large or color as red, black). 
        - Multiple filters can be combined using AND or OR. 
        - In AND, all filters must pass for a product/variant.
        - In OR, product/variants passing ANY of the filter should be selected.
        - Multiple filters can be used so filters should have a Add filter button, Remove filter button.
        - There should be a Apply Filter button. Only when its clicked I expect the results to change, so no need for interactively updating filtered products results.
        - If no filter, then show all products/variants in Section 2. If any filter used, then show only filtered results in Section 2.

      - Section 2: Filtered Results section.
        - This section shows filtered results after applying Section 1 filters. If no filters used in Section 1, then show all products and variants.
        - Columns needed here: Product, Variants (if any they can be present in this column). 1 row of Product can nest many rows of Variants (even though Variants is a separate column than product).
        - **VARIANT DISPLAY ENHANCEMENT**: For products with many variants, initially shows 6 variants with "Show X more variants" button to expand and see all. "Show less variants" button collapses back to initial view. Each product's expansion state is independent.
        - Selection/Deselection checkboxes on Product and individual Variants. The finally selected items will be used to apply edit changes in section 3, and we can see their preview changes in section 4.
        - Should be possible to select or deselect products and its variants individually. So user could just select/deselect Product, or just some/all/none of its variants or both.
        - All Products and their variants are Selected by default, user can deselect items.
        - Since Products/Variants combinations can be large in number, we show a paginated Table here, with Next, Previous and indices range of items on current page. So if 11 filtered (or unfiltered total product results), and page has size of 3 products only, then on second page we see text `4-6` and next, prev arrows, and on last page `10-11` and only prev arrow. If filtered (or total unfiltered if no filter used) results less than 1 page size, then no next page button.
        - The final selected items (across the paginated list of Section 2, not just on present paginated list), should be passed for section 3/4 changes. So any select/deselect should somehow pass the complete final list from section 2 to section 3/4.

      - Section 3: Edit Changes
        - Will contain a list of edit changes user can apply on Product or Variants selected finally from Section 2. Even if multiple pages on section 2, this section will receive the exact list of products/variants from section 2. 
        - Each list item, will be a bulk edit which user can choose: Either a product or a variant edit. This is decided by a dropdown list of changes we support. Dropdown has list items nested under Product section (say Title, Description (HTML)), or Variant section (say Price, Compare At Price). User can choose 1 item from dropdown, say Variant Price. Then user can enter the new Value of that field in text box. Then this becomes a list item in our edit list.
        - Dropdown items are maintained under a constant containing exact product / variant fields we allow to edit. Over time we could add more items in list.
        - Similarly, User can add more list items in our bulk edit list, or remove an already added list item. This way user can construct a collection of bulk edit list.
        - Finally, once the user has entered edit list . and clicked on Preview Changes button (at bottom of this section), then we will show a Preview of changes on section 2 (total selected items) in section 4, after applying the edit list changes from section 3. This won't actually apply changes, that will be donce once user confirms in Section 4.

      - Section 4: Preview Changes
        - Shows a Paginated list of Preview Changes due to all the bulk edit list items of Section 3 on Section 2's finally selected items.
        - List can be very big so its controlled by pagination (similar to section 2, with index range of items on page and next, prev buttons).
        - Has 3 columns: Product, Variant, Changes. For product specific changes from edit list of section 3, we can preview a list of changes (showing original and edited value). Similarly for variants inside the product, we can preview changes to its field values.
        - Row wise we need some special rendering. Product is a parent of variants. So Product appears in a row, with no Variant show in that row, and in Changes column we only show product changes preview.
        - But only next row onwards we populate Variant column and show variant field changes preview. Product field should be empty for them. This sort of shows nested structure, between product and variant while still clearly showing changes for each of them.
        - Items on this section are all selected by default, but user can deselect certain product / variant to skip applying changes. Again user could select/deselect Product or any of its variants to finally go through the changes.
        - Also note that this could be a very big list so some pagination while mutation query might be needed. Also mutation needs to be dynamically generated depending upon all the change fields selected in bulk edit list in section 3.
        - On Bottom it also shows a maintains a total selected products, variants, and edits which will be done once the Apply Edits button placed in bottom of this secttion is clicked.
        - Upon clicking here we navigate to jobs page `<base-url>/app/jobs`, with the latest job showing the processing status and progress of above chosen edits.

## ✨ **IMPLEMENTED FEATURES**

### **Variant Display Enhancement (August 2025)**
- **Problem**: Products with many variants (>6) were difficult to manage as only first 6 variants were visible
- **Solution**: Configurable variant display with expand/collapse functionality
- **Implementation**:
  - **Initial Display**: Shows first 6 variants per product (configurable via `VARIANT_DISPLAY_CONFIG`)
  - **Expansion**: "Show X more variants" button reveals all remaining variants
  - **Collapse**: "Show less variants" button returns to initial 6-variant view
  - **State Management**: Each product maintains independent expansion state
  - **Performance**: No additional API calls - uses already loaded variant data
  - **Configuration**: `VARIANT_DISPLAY_CONFIG.initialDisplayCount = 6` (easily adjustable)
- **Benefits**:
  - Improved UX for products with many variants
  - Cleaner interface with progressive disclosure
  - Better performance with large product catalogs
  - Maintains full functionality for variant selection/deselection


  - <base-url>/app/jobs : User can manage bulk edit jobs from here. Shows :
    - jobs history list, with options to revert the jobs
    - Reapply the job (filter specifically). Should direct to the <base-url>/app/edit page with filters of the chosen job, so that user can apply on current list of products/variants.
    - Current Running Job Progress. Each Bulk edit Job could comprise of a series of mutation jobs (to respect Shopify rate limits). So user can see here progress.
    - Each Job can have statuses: COMPLETED, PARTIALLY FAILED, etc.
    - Each Job also shows the filters used, product/variants affected with previous and new values. And if any product/variant mutation was unsuccessful/successful.