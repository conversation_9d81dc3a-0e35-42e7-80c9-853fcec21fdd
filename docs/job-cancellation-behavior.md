# Job Cancellation Behavior

This document explains how job cancellation works in the Shopify Bulk Product Editor, including timing, behavior, and technical implementation details.

## 🎯 **Quick Summary**

- **Cancellation Check Frequency**: Between batches only (every 10 products by default)
- **Mid-Batch Behavior**: Current batch completes before cancellation takes effect
- **Response Time**: 10-30 seconds maximum delay (depending on batch size)
- **Data Integrity**: Final progress numbers are always updated correctly

## 📋 **Cancellation Timing & Behavior**

### **When Cancellation Checks Happen**
```typescript
// Cancellation check occurs here - BEFORE each batch starts
for (let i = 0; i < batches.length; i++) {
  const currentJob = await db.job.findUnique({
    where: { id: jobId },
    select: { status: true }
  })

  if (currentJob?.status === 'CANCELLED') {
    // Job stops here - updates final progress and exits
    return { cancelled: true, processedProducts }
  }

  // Process entire batch (10 products by default)
  for (const productVariant of batch) {
    // NO cancellation checks within batch
    await processProductVariant(productVariant)
  }
}
```

### **Mid-Batch Cancellation Scenario**

**Example**: Job processing 100 products in batches of 10

1. **Batch 1**: Products 1-10 ✅ (completed)
2. **Batch 2**: Products 11-20 ✅ (completed)  
3. **Batch 3**: Products 21-30 🔄 (currently processing)
   - User clicks "Stop Job" while processing product 25
   - Database updated: `job.status = 'CANCELLED'`
   - **Current batch continues**: Products 26, 27, 28, 29, 30 still get processed
4. **Batch 4**: ❌ (cancelled before starting)
   - Cancellation detected before batch 4 starts
   - Job exits with progress: 30 products processed

**Result**: 30 products processed instead of 25 (5 extra products)

## ⏱️ **Response Time Characteristics**

### **Cancellation Delay Factors**
- **Batch Size**: Default 10 products (configurable)
- **Processing Time**: ~1-3 seconds per product
- **Maximum Delay**: 10-30 seconds (time to complete current batch)

### **Typical Response Times**
| Batch Size | Products Remaining in Batch | Expected Delay |
|------------|----------------------------|----------------|
| 10         | 1-5 products               | 3-15 seconds   |
| 10         | 6-10 products              | 18-30 seconds  |
| 20         | 1-10 products              | 3-30 seconds   |
| 20         | 11-20 products             | 33-60 seconds  |

## 🔧 **Technical Implementation**

### **Cancellation Detection Points**

1. **Primary Check**: Before each batch starts
   ```typescript
   // Location: app/services/jobProcessor.server.ts:303
   if (currentJob?.status === 'CANCELLED') {
     // Update final progress numbers
     await db.job.update({
       where: { id: jobId },
       data: { processedProducts, successfulUpdates, failedUpdates }
     })
     return { cancelled: true }
   }
   ```

2. **Final Check**: After all batches complete
   ```typescript
   // Location: app/services/jobProcessor.server.ts:455
   if (finalJob?.status === 'CANCELLED') {
     // Update progress numbers, preserve CANCELLED status
     await db.job.update({
       where: { id: jobId },
       data: { processedProducts, successfulUpdates, failedUpdates }
     })
   }
   ```

### **Progress Update Guarantee**

**Both cancellation paths ensure final progress is updated:**
- ✅ **Early Cancellation**: Progress updated when detected mid-processing
- ✅ **Late Cancellation**: Progress updated after processing completes
- ✅ **Real-time UI**: Shows correct progress immediately (no page refresh needed)

## 🎨 **User Experience**

### **What Users See**

1. **Click "Stop Job"**:
   - Button shows loading state immediately
   - Toast notification: "Job stop requested"

2. **During Cancellation Delay**:
   - Job status remains "IN_PROGRESS"
   - Progress continues updating (current batch completing)
   - Real-time updates show increasing product count

3. **After Cancellation**:
   - Job status changes to "CANCELLED"
   - Progress shows final numbers (includes completed batch)
   - Stop button disappears
   - Toast notification: "Job stopped successfully"

### **Expected User Behavior**
- **10-30 second delay is normal** - current batch must complete
- **Progress may increase slightly** after clicking stop (not a bug)
- **Final numbers are accurate** - includes all actually processed products

## 🏗️ **Design Rationale**

### **Why Batch-Level Cancellation?**

**✅ Advantages:**
- **Batch Integrity**: Avoids partial batch states and complex cleanup
- **Performance**: Minimal database overhead (1 query per 10 products vs 1 per product)
- **Simplicity**: Cleaner error handling and state management
- **Acceptable Delay**: 10-30 seconds is reasonable for bulk operations

**❌ Alternative (Product-Level Checks):**
- **High Database Load**: 1 query per product (10x more database calls)
- **Complex State**: Mid-batch failures require complex cleanup logic
- **Minimal Benefit**: Saves only 5-15 seconds in typical scenarios

### **Batch Size Configuration**

```typescript
// Default configuration
const DEFAULT_BATCH_SIZE = 10  // products per batch
const DEFAULT_DELAY = 1000     // ms between batches

// Cancellation responsiveness vs performance trade-off
// Smaller batches = faster cancellation, more database overhead
// Larger batches = slower cancellation, better performance
```

## 🚨 **Edge Cases & Limitations**

### **Known Limitations**
1. **Mid-Batch Delay**: Up to 1 full batch worth of products may be processed after cancellation
2. **Network Delays**: Shopify API calls may extend batch completion time
3. **Error Handling**: Failed products in current batch still count toward delay

### **Not Supported**
- ❌ Immediate cancellation (mid-product processing)
- ❌ Partial batch rollback
- ❌ Sub-second cancellation response

### **Acceptable Trade-offs**
- ✅ 10-30 second delay is reasonable for bulk operations
- ✅ Extra products processed are still valid updates
- ✅ Batch integrity prevents data inconsistencies

## 📊 **Monitoring & Debugging**

### **Worker Logs to Watch**
```bash
# Normal cancellation flow
[JobProcessor] Job was cancelled, stopping processing {"batchIndex":2,"processedProducts":20}
[JobProcessor] Job was cancelled, updated final progress numbers {"processedProducts":20}

# Or late cancellation detection
[JobProcessor] Job was cancelled, preserving CANCELLED status {"jobId":"..."}
```

### **Database Verification**
```sql
-- Check final job state
SELECT id, status, processedProducts, successfulUpdates, totalProducts 
FROM Job 
WHERE status = 'CANCELLED' 
ORDER BY createdAt DESC;

-- Verify progress accuracy
-- processedProducts should match actual JobProductVariant entries processed
```

---

*Last Updated: August 2, 2025*  
*Related: [Development Roadmap](development-roadmap.md), [Job Queue System](shop-queue-system.md)*
