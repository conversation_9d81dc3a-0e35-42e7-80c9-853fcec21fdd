{"shopify_domain": "ranjan-client.myshopify.com", "display_domain": "ranjan-client.myshopify.com", "shopify_name": "ranjan-client", "shopify_id": 57749536850, "shopify_plan": "partner_test", "shopify_timezone": "America/New_York", "default_weight_unit": "lb", "timezone": "America/New_York", "money_format": "${{amount}}", "multi_location_enabled": true, "primary_location_id": 64730300498, "shop_ab": 1, "preload_search_uuid": "18a9109d-53b8-448c-a5c7-d2763bf29b67", "all_plans": [{"title": "Developer", "price": 0.0, "description": "Free plan for dev stores", "uuid": "f7f92e71-1c55-4f7e-8c3f-1e5f739b9aea", "trial_days": 0, "is_featured": false, "plan_highlights": [], "plan_features": [], "features": []}, {"title": "Starter", "price": 0.0, "description": "10 Products per Task", "uuid": "1ca079ef-9665-4318-91a3-42e10c59ee9b", "trial_days": 0, "is_featured": false, "plan_highlights": [], "plan_features": [], "features": []}, {"title": "Basic", "price": 30.0, "description": "Unlimited Products per Task", "uuid": "418a1ee5-e94c-458f-ba5c-ae48c7f5c1cb", "trial_days": 0, "is_featured": false, "plan_highlights": ["No edit limits", "Schedule edits and sales"], "plan_features": [{"icon": "ArchiveIcon", "text": "60-day edit history"}, {"icon": "CalendarTimeIcon", "text": "Scheduled edits and undos"}], "features": []}, {"title": "Advanced", "price": 60.0, "description": "Schedule and Automate", "uuid": "84dbec72-9f1e-4b1a-bff8-ffbd3bc3d33f", "trial_days": 0, "is_featured": true, "plan_highlights": ["Automate repeated tasks", "Use Liquid code for custom edits"], "plan_features": [{"icon": "ArchiveIcon", "text": "90-day edit history"}, {"icon": "ReplayIcon", "text": "5 repeating edits"}, {"icon": "RefreshIcon", "text": "5 Inventory Sync jobs"}, {"icon": "ContractIcon", "text": "5 automatic product rules"}, {"icon": "CalendarTimeIcon", "text": "5 scheduled exports"}, {"icon": "CodeIcon", "text": "1 code snippet"}], "features": []}, {"title": "Professional", "price": 120.0, "description": "Scale your Store", "uuid": "3a1f659a-d92a-42ea-a7ba-7f110f20449f", "trial_days": 0, "is_featured": false, "plan_highlights": ["Run rules when a product is updated", "More available automations"], "plan_features": [{"icon": "ArchiveIcon", "text": "180-day edit history"}, {"icon": "ReplayIcon", "text": "20 repeating edits"}, {"icon": "RefreshIcon", "text": "10 Inventory Sync jobs"}, {"icon": "ContractIcon", "text": "20 automatic product rules"}, {"icon": "CalendarTimeIcon", "text": "25 scheduled exports"}, {"icon": "CodeIcon", "text": "5 code snippets"}], "features": []}], "discount": null, "total_edit_count": 0, "total_export_count": 0, "total_import_count": 0, "plan_price": null, "search_methods": [{"name": "All Products", "key": "all_products", "data_type": "number", "group": "Custom"}, {"name": "Barcode (ISBN, UPC, GTIN, etc.)", "key": "barcode", "data_type": "string", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Category", "key": "product__category", "data_type": "category", "group": "Product Fields"}, {"name": "Charge tax on this product", "key": "taxable", "data_type": "boolean", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Collection", "key": "product__collections__id", "data_type": "collection", "group": "Product Fields"}, {"name": "Compare-at Price", "key": "compare_at_price", "data_type": "number-float", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Compare-at Price is Blank", "key": "compare_at_price__isnull__true", "data_type": "number", "group": "Custom"}, {"name": "Connected Inventory Location", "key": "inventory_locations", "data_type": "inventory_connection", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Cost", "key": "inventory_item__cost", "data_type": "number", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Country of Origin", "key": "inventory_item__country_code_of_origin", "data_type": "string", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Date Created", "key": "product__product_created_at", "data_type": "datetime", "group": "Product Fields"}, {"name": "Date Published", "key": "product__published_at", "data_type": "datetime", "group": "Product Fields"}, {"name": "Date Updated", "key": "product__product_updated_at", "data_type": "datetime", "group": "Product Fields"}, {"name": "Description", "key": "product__body_html", "data_type": "string", "group": "Product Fields"}, {"name": "Doesn't have images", "key": "product__total_images__eq__0", "data_type": "int", "group": "Custom"}, {"name": "<PERSON><PERSON> (URL)", "key": "product__handle", "data_type": "string", "group": "Product Fields"}, {"name": "Has Duplicate Barcode", "key": "barcode__duplicate", "data_type": "string", "group": "Custom"}, {"name": "Has Duplicate SKU", "key": "sku__duplicate", "data_type": "string", "group": "Custom"}, {"name": "Has Duplicate Title", "key": "product__title__duplicate", "data_type": "string", "group": "Custom"}, {"name": "Has Images", "key": "product__total_images__gt__0", "data_type": "int", "group": "Custom"}, {"name": "HS Tariff Code", "key": "inventory_item__harmonized_system_code", "data_type": "string", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Inventory Out of Stock Policy", "key": "inventory_policy", "data_type": "inventory_policy-edit", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Inventory Quantity", "key": "product__total_inventory", "data_type": "number-int", "group": "Product Fields"}, {"name": "Not in any Collection", "key": "product__collections__none", "data_type": "string", "group": "Custom"}, {"name": "Not in any Manual Collection", "key": "product__custom_collections__none", "data_type": "string", "group": "Custom"}, {"name": "Option 1 Name", "key": "product__options__0__name", "data_type": "string", "group": "Product Fields"}, {"name": "Option 1 Value", "key": "option1", "data_type": "string", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Option 2 Name", "key": "product__options__1__name", "data_type": "string", "group": "Product Fields"}, {"name": "Option 2 Value", "key": "option2", "data_type": "string", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Option 3 Name", "key": "product__options__2__name", "data_type": "string", "group": "Product Fields"}, {"name": "Option 3 Value", "key": "option3", "data_type": "string", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Physical Product", "key": "requires_shipping", "data_type": "boolean", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Price", "key": "price", "data_type": "number-float", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Price < Compare-at Price", "key": "price__lt__compare_at_price", "data_type": "number", "group": "Custom"}, {"name": "Price = Compare-at Price", "key": "price__eq__compare_at_price", "data_type": "number", "group": "Custom"}, {"name": "Price > Compare-at Price", "key": "price__gt__compare_at_price", "data_type": "number", "group": "Custom"}, {"name": "Product ID", "key": "product_id", "data_type": "id", "group": "Product Fields"}, {"name": "Product is completely out of stock", "key": "product__out_of_stock__true", "data_type": "boolean", "group": "Custom"}, {"name": "Product Type (Custom)", "key": "product__product_type", "data_type": "string", "group": "Product Fields"}, {"name": "<PERSON><PERSON>", "key": "profit_margin", "data_type": "number-float", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Search Engine Visibility (SEO)", "key": "product__metafields__seo__hidden", "data_type": "<PERSON><PERSON><PERSON><PERSON>", "group": "Product Fields"}, {"name": "SKU", "key": "sku", "data_type": "string", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Status", "key": "product__status", "data_type": "string", "group": "Product Fields"}, {"name": "Tag", "key": "product__tag", "data_type": "tag", "group": "Product Fields"}, {"name": "Theme Template", "key": "product__template_suffix", "data_type": "template_suffix", "group": "Product Fields"}, {"name": "Title", "key": "product__title", "data_type": "string", "group": "Product Fields"}, {"name": "Track Quantity", "key": "inventory_management_shopify", "data_type": "boolean", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Variant Count", "key": "product__total_variants", "data_type": "number-int", "group": "Product Fields"}, {"name": "Variant Inventory Quantity", "key": "inventory_quantity", "data_type": "number-int", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Variant Title", "key": "title", "data_type": "string", "group": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "key": "product__vendor", "data_type": "string", "group": "Product Fields"}, {"name": "Visible on Online Store (web)", "key": "product__published_at", "data_type": "boolean", "group": "Product Fields"}, {"name": "Visible on Point of Sale (POS)", "key": "product__published_scope", "data_type": "string", "group": "Product Fields"}, {"name": "Weight", "key": "weight", "data_type": "number", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Weight Unit", "key": "weight_unit", "data_type": "weight_unit", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Google Shopping - Age Group", "key": "product__metafields__mm-google-shopping__age_group", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Category", "key": "product__metafields__mm-google-shopping__google_product_category", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Color", "key": "product__metafields__mm-google-shopping__color", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Condition", "key": "product__metafields__mm-google-shopping__condition", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Custom Label 0", "key": "product__metafields__mm-google-shopping__custom_label_0", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Custom Label 1", "key": "product__metafields__mm-google-shopping__custom_label_1", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Custom Label 2", "key": "product__metafields__mm-google-shopping__custom_label_2", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Custom Label 3", "key": "product__metafields__mm-google-shopping__custom_label_3", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Custom Label 4", "key": "product__metafields__mm-google-shopping__custom_label_4", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Custom Product", "key": "product__metafields__mm-google-shopping__custom_product", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Gender", "key": "product__metafields__mm-google-shopping__gender", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Manufacturer Part Number (MPN)", "key": "product__metafields__mm-google-shopping__mpn", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Material", "key": "product__metafields__mm-google-shopping__material", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Size", "key": "product__metafields__mm-google-shopping__size", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Size System", "key": "product__metafields__mm-google-shopping__size_system", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Size Type", "key": "product__metafields__mm-google-shopping__size_type", "data_type": "metafield", "group": "Google Shopping Fields"}], "editable_fields": [{"slug": "barcode", "edit_type": "text-edit", "name": "Barcode (ISBN, UPC, GTIN, etc.)", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__category", "edit_type": "category-edit", "name": "Category", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "taxable", "edit_type": "boolean-edit", "name": "Charge tax on this product", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "compare_at_price", "edit_type": "float-edit", "name": "Compare-at Price", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__shopify--discovery--product_recommendation__complementary_products", "edit_type": "productreferencelist-edit", "name": "Complementary Products", "group": "Search & Discovery App", "is_generate_active": false, "is_active": true, "is_import_active": false}, {"slug": "inventory_policy", "edit_type": "inventory_policy-edit", "name": "Continue Selling Inventory when Out of Stock", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "inventory_item__cost", "edit_type": "float-edit", "name": "Cost", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "inventory_item__country_code_of_origin", "edit_type": "countrycode-edit", "name": "Country of Origin", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__product_created_at", "edit_type": "text-edit", "name": "Date Created", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "variant_created_at", "edit_type": "text-edit", "name": "Date Created", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "product__product_updated_at", "edit_type": "text-edit", "name": "Date Updated", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "variant_updated_at", "edit_type": "text-edit", "name": "Date Updated", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "product__body_html", "edit_type": "text-edit", "name": "Description", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__age_group", "edit_type": "google_age-edit", "name": "Google Shopping - Age Group", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__google_product_category", "edit_type": "google_category-edit", "name": "Google Shopping - Category", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__color", "edit_type": "text-edit", "name": "Google Shopping - Color", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__condition", "edit_type": "google_condition-edit", "name": "Google Shopping - Condition", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__custom_label_0", "edit_type": "text-edit", "name": "Google Shopping - Custom Label 0", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__custom_label_1", "edit_type": "text-edit", "name": "Google Shopping - Custom Label 1", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__custom_label_2", "edit_type": "text-edit", "name": "Google Shopping - Custom Label 2", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__custom_label_3", "edit_type": "text-edit", "name": "Google Shopping - Custom Label 3", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__custom_label_4", "edit_type": "text-edit", "name": "Google Shopping - Custom Label 4", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__custom_product", "edit_type": "google_iscustom-edit", "name": "Google Shopping - Custom Product", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__gender", "edit_type": "google_gender-edit", "name": "Google Shopping - Gender", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__mpn", "edit_type": "text-edit", "name": "Google Shopping - Manufacturer Part Number (MPN)", "group": "Google Shopping Fields", "is_generate_active": false, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__material", "edit_type": "text-edit", "name": "Google Shopping - Material", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__size", "edit_type": "text-edit", "name": "Google Shopping - Size", "group": "Google Shopping Fields", "is_generate_active": false, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__size_system", "edit_type": "google_sizesystem-edit", "name": "Google Shopping - Size System", "group": "Google Shopping Fields", "is_generate_active": false, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__size_type", "edit_type": "google_sizetype-edit", "name": "Google Shopping - Size Type", "group": "Google Shopping Fields", "is_generate_active": false, "is_active": true, "is_import_active": true}, {"slug": "product__handle", "edit_type": "text-edit", "name": "<PERSON><PERSON> (URL)", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "inventory_item__harmonized_system_code", "edit_type": "text-edit", "name": "HS Tariff Code", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__images__alt", "edit_type": "text-edit", "name": "Image Alt Text", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": true}, {"slug": "product__images__id", "edit_type": "image-edit", "name": "Image IDs", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "product__images__position", "edit_type": "image-edit", "name": "Image Position", "group": "Product Fields", "is_generate_active": false, "is_active": false, "is_import_active": true}, {"slug": "product__images", "edit_type": "image-edit", "name": "Images", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "inventory_item_id", "edit_type": "int-edit", "name": "Inventory Item ID", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "inventory_quantity", "edit_type": "integer-edit", "name": "Inventory Level", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "requires_shipping", "edit_type": "boolean-edit", "name": "Is Physical Product", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "custom_collections__title", "edit_type": "collection-edit", "name": "Manual Collection", "group": "Product Fields", "is_generate_active": false, "is_active": true, "is_import_active": true}, {"slug": "custom_collection__handle", "edit_type": "", "name": "Manual Collections", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "product__options__0__name", "edit_type": "text-edit", "name": "Option 1 Name", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "option1", "edit_type": "text-edit", "name": "Option 1 Value", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__options__1__name", "edit_type": "text-edit", "name": "Option 2 Name", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "option2", "edit_type": "text-edit", "name": "Option 2 Value", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__options__2__name", "edit_type": "text-edit", "name": "Option 3 Name", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "option3", "edit_type": "text-edit", "name": "Option 3 Value", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "price", "edit_type": "float-edit", "name": "Price", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__primary_image", "edit_type": "image-edit", "name": "Primary Image", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "product_id", "edit_type": "int", "name": "Product ID", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "product__product_type", "edit_type": "text-edit", "name": "Product Type (Custom)", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "inventory_item__province_code_of_origin", "edit_type": "text-edit", "name": "Province Code of Origin", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": false, "is_active": true, "is_import_active": false}, {"slug": "product__metafields__shopify--discovery--product_recommendation__related_products", "edit_type": "productreferencelist-edit", "name": "Related Products", "group": "Search & Discovery App", "is_generate_active": false, "is_active": true, "is_import_active": false}, {"slug": "product__metafields__shopify--discovery--product_recommendation__related_products_display", "edit_type": "relatedproductsetting-edit", "name": "Related Products Settings", "group": "Search & Discovery App", "is_generate_active": false, "is_active": true, "is_import_active": false}, {"slug": "product__metafields__seo__hidden", "edit_type": "<PERSON><PERSON><PERSON><PERSON>-visibility-edit", "name": "Search Engine Visibility (SEO)", "group": "Product Fields", "is_generate_active": false, "is_active": true, "is_import_active": false}, {"slug": "product__metafields__global__description_tag", "edit_type": "text-edit", "name": "SEO Meta Description", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__global__title_tag", "edit_type": "text-edit", "name": "SEO Meta Title", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "sku", "edit_type": "text-edit", "name": "SKU", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "smart_collection__handle", "edit_type": "", "name": "Smart Collections", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "product__status", "edit_type": "status-edit", "name": "Status", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__tags", "edit_type": "tags-edit", "name": "Tags", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "tax_code", "edit_type": "text-edit", "name": "Tax Code", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__template_suffix", "edit_type": "templatesuffix-edit", "name": "Theme Template", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__title", "edit_type": "text-edit", "name": "Title", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__total_inventory", "edit_type": "int", "name": "Total Inventory", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "inventory_management", "edit_type": "inventory_management-edit", "name": "Track Quantity / Inventory", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": false, "is_import_active": true}, {"slug": "inventory_item__tracked", "edit_type": "track-quantity-edit", "name": "Track Quantity/Inventory", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": false, "is_active": true, "is_import_active": false}, {"slug": "unitPriceMeasurement", "edit_type": "unitprice-edit", "name": "Unit Price", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": false, "is_active": true, "is_import_active": false}, {"slug": "url_admin", "edit_type": "", "name": "URL (Shopify Admin)", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "url_web", "edit_type": "", "name": "URL (Web)", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "id", "edit_type": "integer-edit", "name": "Variant ID", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "image", "edit_type": "image-edit", "name": "Variant Image", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": false, "is_active": false, "is_import_active": true}, {"slug": "title", "edit_type": "text-edit", "name": "Variant Title", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "product__vendor", "edit_type": "text-edit", "name": "<PERSON><PERSON><PERSON>", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__published_at", "edit_type": "published-edit", "name": "Visible on Online Store (web)", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": true}, {"slug": "product__published_scope", "edit_type": "visibility-edit", "name": "Visible on Point of Sale (POS)", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": true}, {"slug": "grams", "edit_type": "integer-edit", "name": "Weight (in grams)", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "weight", "edit_type": "float-edit", "name": "Weight (in product's unit)", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "weight_unit", "edit_type": "weightunit-edit", "name": "Weight Unit", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}], "file_search_methods": [{"id": 5, "name": "Barcode/UPC", "filter_value": "barcode", "comp": "eq"}, {"id": 4, "name": "<PERSON><PERSON>", "filter_value": "product__handle", "comp": "eq"}, {"id": 6, "name": "Product ID", "filter_value": "product_id", "comp": "eq"}, {"id": 9, "name": "SKU contains", "filter_value": "sku__contains", "comp": "eq"}, {"id": 11, "name": "SKU ends with", "filter_value": "sku__iendswith", "comp": "eq"}, {"id": 3, "name": "SKU is", "filter_value": "sku", "comp": "eq"}, {"id": 12, "name": "SKU is (case-insensitive)", "filter_value": "sku__iexact", "comp": "eq"}, {"id": 1013, "name": "SKU starts with", "filter_value": "sku__istartswith", "comp": "eq"}, {"id": 1, "name": "Title contains", "filter_value": "product__title__icontains", "comp": "eq"}, {"id": 2, "name": "Title is exactly", "filter_value": "product__title", "comp": "eq"}, {"id": 7, "name": "Variant ID", "filter_value": "id", "comp": "eq"}, {"id": 10, "name": "Variant Title", "filter_value": "title", "comp": "eq"}], "file_editable_fields": [{"id": 14397, "name": "Add Inventory Level", "slug": "inventory_add", "subtype": "integer_set_value"}, {"id": 12, "name": "Barcode", "slug": "barcode", "subtype": "text_set_value"}, {"id": 28, "name": "Charge tax on this product", "slug": "taxable", "subtype": "boolean_change"}, {"id": 4, "name": "Collection: Add", "slug": "custom_collections__title", "subtype": "collection_add"}, {"id": 5, "name": "Collection: Remove", "slug": "custom_collections__title", "subtype": "collection_remove"}, {"id": 3, "name": "Compare-at Price", "slug": "compare_at_price", "subtype": "float_set_value"}, {"id": 3798, "name": "Connect Inventory to Location", "slug": "inventory_locations", "subtype": "location_connect"}, {"id": 22, "name": "Cost", "slug": "inventory_item__cost", "subtype": "float_set_value"}, {"id": 361, "name": "Cost (decrease by percent)", "slug": "inventory_item__cost", "subtype": "float_decrease_percent"}, {"id": 362, "name": "Cost (increase by percent)", "slug": "inventory_item__cost", "subtype": "float_increase_percent"}, {"id": 83, "name": "Country of Origin", "slug": "inventory_item__country_code_of_origin", "subtype": "countrycode_change"}, {"id": 10, "name": "Description", "slug": "product__body_html", "subtype": "text_set_value"}, {"id": 91, "name": "Description: Add to Beginning", "slug": "product__body_html", "subtype": "text_add_beginning"}, {"id": 19, "name": "Description: Add to End", "slug": "product__body_html", "subtype": "text_add_end"}, {"id": 3797, "name": "Disconnect Inventory from Location", "slug": "inventory_locations", "subtype": "location_disconnect"}, {"id": 138, "name": "Fulfillment Service", "slug": "fulfillment_service", "subtype": "fulfillmentservice_set_value"}, {"id": 186, "name": "Google Shopping - Age Group", "slug": "product__metafields__mm-google-shopping__age_group", "subtype": "google_age_change"}, {"id": 29, "name": "Google Shopping - Category", "slug": "product__metafields__mm-google-shopping__google_product_category", "subtype": "text_set_value"}, {"id": 69, "name": "Google Shopping - Color", "slug": "product__metafields__mm-google-shopping__color", "subtype": "text_set_value"}, {"id": 30, "name": "Google Shopping - Condition", "slug": "product__metafields__mm-google-shopping__condition", "subtype": "google_condition_change"}, {"id": 32, "name": "Google Shopping - Custom Label 0", "slug": "product__metafields__mm-google-shopping__custom_label_0", "subtype": "text_set_value"}, {"id": 33, "name": "Google Shopping - Custom Label 1", "slug": "product__metafields__mm-google-shopping__custom_label_1", "subtype": "text_set_value"}, {"id": 34, "name": "Google Shopping - Custom Label 2", "slug": "product__metafields__mm-google-shopping__custom_label_2", "subtype": "text_set_value"}, {"id": 35, "name": "Google Shopping - Custom Label 3", "slug": "product__metafields__mm-google-shopping__custom_label_3", "subtype": "text_set_value"}, {"id": 36, "name": "Google Shopping - Custom Label 4", "slug": "product__metafields__mm-google-shopping__custom_label_4", "subtype": "text_set_value"}, {"id": 31, "name": "Google Shopping - Gender", "slug": "product__metafields__mm-google-shopping__gender", "subtype": "text_set_value"}, {"id": 71, "name": "Google Shopping - Is Custom Product", "slug": "product__metafields__mm-google-shopping__custom_product", "subtype": "google_iscustom_change"}, {"id": 37, "name": "Google Shopping - Manufacturer Part Number", "slug": "product__metafields__mm-google-shopping__mpn", "subtype": "text_set_value"}, {"id": 70, "name": "Google Shopping - Material", "slug": "product__metafields__mm-google-shopping__material", "subtype": "text_set_value"}, {"id": 187, "name": "Google Shopping - Size", "slug": "product__metafields__mm-google-shopping__size", "subtype": "text_set_value"}, {"id": 189, "name": "Google Shopping - Size System", "slug": "product__metafields__mm-google-shopping__size_system", "subtype": "google_sizesystem_change"}, {"id": 188, "name": "Google Shopping - Size Type", "slug": "product__metafields__mm-google-shopping__size_type", "subtype": "google_sizetype_change"}, {"id": 23, "name": "<PERSON><PERSON>", "slug": "product__handle", "subtype": "text_set_value"}, {"id": 18, "name": "HS Tariff Code", "slug": "inventory_item__harmonized_system_code", "subtype": "text_set_value"}, {"id": 1, "name": "Inventory Level", "slug": "inventory_quantity", "subtype": "integer_set_value"}, {"id": 38, "name": "Inventory Out of Stock Policy", "slug": "inventory_policy", "subtype": "inventorypolicy_change"}, {"id": 27, "name": "Is Physical Product", "slug": "requires_shipping", "subtype": "boolean_change"}, {"id": 16, "name": "Meta Description", "slug": "product__metafields__global__description_tag", "subtype": "text_set_value"}, {"id": 17, "name": "Meta Title", "slug": "product__metafields__global__title_tag", "subtype": "text_set_value"}, {"id": 66, "name": "Option 1 Name", "slug": "product__options__0__name", "subtype": "text_set_value"}, {"id": 24, "name": "Option 1 Value", "slug": "option1", "subtype": "text_set_value"}, {"id": 67, "name": "Option 2 Name", "slug": "product__options__1__name", "subtype": "text_set_value"}, {"id": 25, "name": "Option 2 Value", "slug": "option2", "subtype": "text_set_value"}, {"id": 68, "name": "Option 3 Name", "slug": "product__options__2__name", "subtype": "text_set_value"}, {"id": 26, "name": "Option 3 Value", "slug": "option3", "subtype": "text_set_value"}, {"id": 2, "name": "Price", "slug": "price", "subtype": "float_set_value"}, {"id": 359, "name": "Price (decrease by percent)", "slug": "price", "subtype": "float_decrease_percent"}, {"id": 360, "name": "Price (increase by percent)", "slug": "price", "subtype": "float_increase_percent"}, {"id": 2658, "name": "Product Category (Standard Product Type)", "slug": "product__product_category", "subtype": "product_category_change"}, {"id": 13, "name": "Product Image Alt Text", "slug": "product__images__alt", "subtype": "text_set_value"}, {"id": 266, "name": "Product Images: Add to Beginning", "slug": "product__images", "subtype": "image_add_beginning"}, {"id": 39, "name": "Product Images: Add to End", "slug": "product__images", "subtype": "image_add_end"}, {"id": 101, "name": "Product Images: Set (Overwrite Existing - Can't Undo)", "slug": "product__images", "subtype": "image_replace"}, {"id": 9, "name": "Product Title", "slug": "product__title", "subtype": "text_set_value"}, {"id": 20, "name": "Product Type", "slug": "product__product_type", "subtype": "text_set_value"}, {"id": 103, "name": "Province Code of Origin", "slug": "inventory_item__province_code_of_origin", "subtype": "text_set_value"}, {"id": 77, "name": "Shopify Tracks Inventory", "slug": "inventory_management", "subtype": "inventorymanagement_change"}, {"id": 21, "name": "SKU", "slug": "sku", "subtype": "text_set_value"}, {"id": 179, "name": "Status", "slug": "product__status", "subtype": "status_change"}, {"id": 6, "name": "Tags: Add", "slug": "product__tags", "subtype": "tags_add"}, {"id": 7, "name": "Tags: <PERSON><PERSON><PERSON>", "slug": "product__tags", "subtype": "tags_remove"}, {"id": 40, "name": "Tags: Set (Overwrites Existing)", "slug": "product__tags", "subtype": "tags_set"}, {"id": 54, "name": "Tax Code", "slug": "tax_code", "subtype": "text_set_value"}, {"id": 1547, "name": "Theme Template", "slug": "product__template_suffix", "subtype": "input_value"}, {"id": 14239, "name": "Unit Price", "slug": "unitPriceMeasurement", "subtype": "value"}, {"id": 8, "name": "<PERSON><PERSON><PERSON>", "slug": "product__vendor", "subtype": "text_set_value"}, {"id": 14, "name": "Visible on Online Store (web)", "slug": "product__published_at", "subtype": "published_change"}, {"id": 15, "name": "Visible on Point of Sale (POS)", "slug": "product__published_scope", "subtype": "visibility_change"}, {"id": 11, "name": "Weight (in grams)", "slug": "grams", "subtype": "integer_set_value"}, {"id": 41, "name": "Weight (in product's unit)", "slug": "weight", "subtype": "float_set_value"}, {"id": 198, "name": "Weight Unit", "slug": "weight_unit", "subtype": "weightunit_change"}], "shipperhq_metafields": [{"name": "Shipping Group Name", "namespace": "global", "field_type": {"name": "single_line_text_field", "pretty_name": "Single Line Text Field", "edit_type": "text-edit", "category": "TEXT"}, "key": "SHIPPING_GROUPS", "owner_type": "PRODUCT", "edit_field": "product__metafields__global__SHIPPING_GROUPS"}, {"name": "Origin Name", "namespace": "global", "field_type": {"name": "single_line_text_field", "pretty_name": "Single Line Text Field", "edit_type": "text-edit", "category": "TEXT"}, "key": "ORIGINS", "owner_type": "PRODUCT", "edit_field": "product__metafields__global__ORIGINS"}, {"name": "Dimensional Rules Name", "namespace": "global", "field_type": {"name": "single_line_text_field", "pretty_name": "Single Line Text Field", "edit_type": "text-edit", "category": "TEXT"}, "key": "DIMENSIONAL_GROUPS", "owner_type": "PRODUCT", "edit_field": "product__metafields__global__DIMENSIONAL_GROUPS"}, {"name": "Length", "namespace": "global", "field_type": {"name": "single_line_text_field", "pretty_name": "Single Line Text Field", "edit_type": "text-edit", "category": "TEXT"}, "key": "LENGTH", "owner_type": "PRODUCT", "edit_field": "product__metafields__global__LENGTH"}, {"name": "<PERSON><PERSON><PERSON>", "namespace": "global", "field_type": {"name": "single_line_text_field", "pretty_name": "Single Line Text Field", "edit_type": "text-edit", "category": "TEXT"}, "key": "WIDTH", "owner_type": "PRODUCT", "edit_field": "product__metafields__global__WIDTH"}, {"name": "Height", "namespace": "global", "field_type": {"name": "single_line_text_field", "pretty_name": "Single Line Text Field", "edit_type": "text-edit", "category": "TEXT"}, "key": "HEIGHT", "owner_type": "PRODUCT", "edit_field": "product__metafields__global__HEIGHT"}, {"name": "HS Code", "namespace": "global", "field_type": {"name": "single_line_text_field", "pretty_name": "Single Line Text Field", "edit_type": "text-edit", "category": "TEXT"}, "key": "HS_CODE", "owner_type": "PRODUCT", "edit_field": "product__metafields__global__HS_CODE"}, {"name": "Shipping Group Name", "namespace": "global", "field_type": {"name": "single_line_text_field", "pretty_name": "Single Line Text Field", "edit_type": "text-edit", "category": "TEXT"}, "key": "SHIPPING_GROUPS", "owner_type": "PRODUCTVARIANT", "edit_field": "variant__metafields__global__SHIPPING_GROUPS"}, {"name": "Origin Name", "namespace": "global", "field_type": {"name": "single_line_text_field", "pretty_name": "Single Line Text Field", "edit_type": "text-edit", "category": "TEXT"}, "key": "ORIGINS", "owner_type": "PRODUCTVARIANT", "edit_field": "variant__metafields__global__ORIGINS"}, {"name": "Dimensional Rules Name", "namespace": "global", "field_type": {"name": "single_line_text_field", "pretty_name": "Single Line Text Field", "edit_type": "text-edit", "category": "TEXT"}, "key": "DIMENSIONAL_GROUPS", "owner_type": "PRODUCTVARIANT", "edit_field": "variant__metafields__global__DIMENSIONAL_GROUPS"}, {"name": "Length", "namespace": "global", "field_type": {"name": "single_line_text_field", "pretty_name": "Single Line Text Field", "edit_type": "text-edit", "category": "TEXT"}, "key": "LENGTH", "owner_type": "PRODUCTVARIANT", "edit_field": "variant__metafields__global__LENGTH"}, {"name": "<PERSON><PERSON><PERSON>", "namespace": "global", "field_type": {"name": "single_line_text_field", "pretty_name": "Single Line Text Field", "edit_type": "text-edit", "category": "TEXT"}, "key": "WIDTH", "owner_type": "PRODUCTVARIANT", "edit_field": "variant__metafields__global__WIDTH"}, {"name": "Height", "namespace": "global", "field_type": {"name": "single_line_text_field", "pretty_name": "Single Line Text Field", "edit_type": "text-edit", "category": "TEXT"}, "key": "HEIGHT", "owner_type": "PRODUCTVARIANT", "edit_field": "variant__metafields__global__HEIGHT"}, {"name": "HS Code", "namespace": "global", "field_type": {"name": "single_line_text_field", "pretty_name": "Single Line Text Field", "edit_type": "text-edit", "category": "TEXT"}, "key": "HS_CODE", "owner_type": "PRODUCTVARIANT", "edit_field": "variant__metafields__global__HS_CODE"}], "filter_comps": [{"value": "contains", "data_type": "string", "text": "contains"}, {"value": "ne", "data_type": "string", "text": "does not contain"}, {"value": "eq", "data_type": "string", "text": "equals"}, {"value": "containsany", "data_type": "string", "text": "contains any of the words"}, {"value": "iendswith", "data_type": "string", "text": "ends with"}, {"value": "istartswith", "data_type": "string", "text": "starts with"}, {"value": "nstartswith", "data_type": "string", "text": "does not start with"}, {"value": "icontains", "data_type": "string", "text": "contains (case-insensitive)"}, {"value": "iexact", "data_type": "string", "text": "equals (case-insensitive)"}, {"value": "lt", "data_type": "datetime", "text": "Is before"}, {"value": "gt", "data_type": "datetime", "text": "Is after"}, {"value": "gtdays", "data_type": "datetime", "text": "Is after x days ago"}, {"value": "ltdays", "data_type": "datetime", "text": "Is before x days ago"}, {"value": "lt", "data_type": "number", "text": "<"}, {"value": "gt", "data_type": "number", "text": ">"}, {"value": "eq", "data_type": "number", "text": "="}, {"value": "ne", "data_type": "number", "text": "is not"}, {"value": "eq", "data_type": "number", "text": "is blank"}, {"value": "ne", "data_type": "number", "text": "is not blank"}, {"value": "eq", "data_type": "id", "text": "is"}, {"value": "ne", "data_type": "id", "text": "is not"}, {"value": "containsany", "data_type": "id", "text": "contains any of the IDs"}, {"value": "eq", "data_type": "boolean", "text": "is"}, {"value": "eq", "data_type": "collection", "text": "is"}, {"value": "ne", "data_type": "collection", "text": "is not"}, {"value": "eq", "data_type": "product_category", "text": "is"}, {"value": "ne", "data_type": "product_category", "text": "is not"}, {"value": "eq", "data_type": "category", "text": "is"}, {"value": "ne", "data_type": "category", "text": "is not"}, {"value": "eq", "data_type": "tag", "text": "contains  (case-sensitive)"}, {"value": "ne", "data_type": "tag", "text": "does not contain  (case-sensitive)"}, {"value": "iequals", "data_type": "tag", "text": "contains (case-insensitive)"}, {"value": "inequals", "data_type": "tag", "text": "does not contain  (case-insensitive)"}, {"value": "partial", "data_type": "tag", "text": "contains the text"}, {"value": "npartial", "data_type": "tag", "text": "does not contain the text"}, {"value": "eq", "data_type": "location", "text": "is linked to"}, {"value": "ne", "data_type": "location", "text": "is not linked to"}, {"value": "contains", "data_type": "metafield", "text": "contains"}, {"value": "ne", "data_type": "metafield", "text": "does not equal"}, {"value": "eq", "data_type": "metafield", "text": "equals"}, {"value": "isset", "data_type": "metafield", "text": "is set"}, {"value": "isnotset", "data_type": "metafield", "text": "is not set"}, {"value": "containsany", "data_type": "metafield", "text": "contains any of the words"}, {"value": "eq", "data_type": "template_suffix", "text": "equals"}, {"value": "ne", "data_type": "template_suffix", "text": "does not equal"}, {"value": "eq", "data_type": "weight_unit", "text": "equals"}, {"value": "ne", "data_type": "weight_unit", "text": "does not equal"}, {"value": "ne", "data_type": "inventory_connection", "text": "Inventory is not connected to"}, {"value": "eq", "data_type": "inventory_connection", "text": "Inventory is connected to"}], "generate_fields": [{"slug": "barcode", "name": "Barcode"}, {"slug": "product__handle", "name": "Product Handle"}, {"slug": "product_id", "name": "Product ID"}, {"slug": "product__title", "name": "Product Title"}, {"slug": "sku", "name": "SKU"}, {"slug": "id", "name": "Variant ID"}], "card_order": ["inapp_edit", "spreadsheet_edit", "product_export", "changelog"], "missing_permissions": false}