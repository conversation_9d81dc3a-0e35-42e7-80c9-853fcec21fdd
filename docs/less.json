{"search_methods": [{"name": "All Products", "key": "all_products", "data_type": "number", "group": "Custom"}, {"name": "Barcode (ISBN, UPC, GTIN, etc.)", "key": "barcode", "data_type": "string", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Category", "key": "product__category", "data_type": "category", "group": "Product Fields"}, {"name": "Charge tax on this product", "key": "taxable", "data_type": "boolean", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Collection", "key": "product__collections__id", "data_type": "collection", "group": "Product Fields"}, {"name": "Compare-at Price", "key": "compare_at_price", "data_type": "number-float", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Compare-at Price is Blank", "key": "compare_at_price__isnull__true", "data_type": "number", "group": "Custom"}, {"name": "Connected Inventory Location", "key": "inventory_locations", "data_type": "inventory_connection", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Cost", "key": "inventory_item__cost", "data_type": "number", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Country of Origin Code", "key": "inventory_item__country_code_of_origin", "data_type": "string", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Date Created", "key": "product__product_created_at", "data_type": "datetime", "group": "Product Fields"}, {"name": "Date Published", "key": "product__published_at", "data_type": "datetime", "group": "Product Fields"}, {"name": "Date Updated", "key": "product__product_updated_at", "data_type": "datetime", "group": "Product Fields"}, {"name": "Description", "key": "product__body_html", "data_type": "string", "group": "Product Fields"}, {"name": "Doesn't have images", "key": "product__total_images__eq__0", "data_type": "int", "group": "Custom"}, {"name": "<PERSON><PERSON> (URL)", "key": "product__handle", "data_type": "string", "group": "Product Fields"}, {"name": "Has Duplicate Barcode", "key": "barcode__duplicate", "data_type": "string", "group": "Custom"}, {"name": "Has Duplicate SKU", "key": "sku__duplicate", "data_type": "string", "group": "Custom"}, {"name": "Has Duplicate Title", "key": "product__title__duplicate", "data_type": "string", "group": "Custom"}, {"name": "Has Images", "key": "product__total_images__gt__0", "data_type": "int", "group": "Custom"}, {"name": "HS Tariff Code", "key": "inventory_item__harmonized_system_code", "data_type": "string", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Inventory Out of Stock Policy", "key": "inventory_policy", "data_type": "inventory_policy-edit", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Inventory Quantity", "key": "product__total_inventory", "data_type": "number-int", "group": "Product Fields"}, {"name": "Not in any Collection", "key": "product__collections__none", "data_type": "string", "group": "Custom"}, {"name": "Not in any Manual Collection", "key": "product__custom_collections__none", "data_type": "string", "group": "Custom"}, {"name": "Option 1 Name", "key": "product__options__0__name", "data_type": "string", "group": "Product Fields"}, {"name": "Option 1 Value", "key": "option1", "data_type": "string", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Option 2 Name", "key": "product__options__1__name", "data_type": "string", "group": "Product Fields"}, {"name": "Option 2 Value", "key": "option2", "data_type": "string", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Option 3 Name", "key": "product__options__2__name", "data_type": "string", "group": "Product Fields"}, {"name": "Option 3 Value", "key": "option3", "data_type": "string", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Physical Product", "key": "requires_shipping", "data_type": "boolean", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Price", "key": "price", "data_type": "number-float", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Price < Compare-at Price", "key": "price__lt__compare_at_price", "data_type": "number", "group": "Custom"}, {"name": "Price = Compare-at Price", "key": "price__eq__compare_at_price", "data_type": "number", "group": "Custom"}, {"name": "Price > Compare-at Price", "key": "price__gt__compare_at_price", "data_type": "number", "group": "Custom"}, {"name": "Product ID", "key": "product_id", "data_type": "id", "group": "Product Fields"}, {"name": "Product is completely out of stock", "key": "product__out_of_stock__true", "data_type": "boolean", "group": "Custom"}, {"name": "Product Type (Custom)", "key": "product__product_type", "data_type": "string", "group": "Product Fields"}, {"name": "<PERSON><PERSON>", "key": "profit_margin", "data_type": "number-float", "group": "<PERSON><PERSON><PERSON>"}, {"name": "SKU", "key": "sku", "data_type": "string", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Status", "key": "product__status", "data_type": "string", "group": "Product Fields"}, {"name": "Tag", "key": "product__tag", "data_type": "tag", "group": "Product Fields"}, {"name": "Theme Template", "key": "product__template_suffix", "data_type": "template_suffix", "group": "Product Fields"}, {"name": "Title", "key": "product__title", "data_type": "string", "group": "Product Fields"}, {"name": "Track Quantity", "key": "inventory_management_shopify", "data_type": "boolean", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Variant Count", "key": "product__total_variants", "data_type": "number-int", "group": "Product Fields"}, {"name": "Variant Inventory Quantity", "key": "inventory_quantity", "data_type": "number-int", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Variant Title", "key": "title", "data_type": "string", "group": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "key": "product__vendor", "data_type": "string", "group": "Product Fields"}, {"name": "Visible on Online Store (web)", "key": "product__published_at", "data_type": "boolean", "group": "Product Fields"}, {"name": "Visible on Point of Sale (POS)", "key": "product__published_scope", "data_type": "string", "group": "Product Fields"}, {"name": "Weight", "key": "weight", "data_type": "number", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Weight Unit", "key": "weight_unit", "data_type": "weight_unit", "group": "<PERSON><PERSON><PERSON>"}, {"name": "Google Shopping - Age Group", "key": "product__metafields__mm-google-shopping__age_group", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Category", "key": "product__metafields__mm-google-shopping__google_product_category", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Color", "key": "product__metafields__mm-google-shopping__color", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Condition", "key": "product__metafields__mm-google-shopping__condition", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Custom Label 0", "key": "product__metafields__mm-google-shopping__custom_label_0", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Custom Label 1", "key": "product__metafields__mm-google-shopping__custom_label_1", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Custom Label 2", "key": "product__metafields__mm-google-shopping__custom_label_2", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Custom Label 3", "key": "product__metafields__mm-google-shopping__custom_label_3", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Custom Label 4", "key": "product__metafields__mm-google-shopping__custom_label_4", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Custom Product", "key": "product__metafields__mm-google-shopping__custom_product", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Gender", "key": "product__metafields__mm-google-shopping__gender", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Manufacturer Part Number (MPN)", "key": "product__metafields__mm-google-shopping__mpn", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Material", "key": "product__metafields__mm-google-shopping__material", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Size", "key": "product__metafields__mm-google-shopping__size", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Size System", "key": "product__metafields__mm-google-shopping__size_system", "data_type": "metafield", "group": "Google Shopping Fields"}, {"name": "Google Shopping - Size Type", "key": "product__metafields__mm-google-shopping__size_type", "data_type": "metafield", "group": "Google Shopping Fields"}], "editable_fields": [{"slug": "barcode", "edit_type": "text-edit", "name": "Barcode (ISBN, UPC, GTIN, etc.)", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__category", "edit_type": "category-edit", "name": "Category", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": false}, {"slug": "taxable", "edit_type": "boolean-edit", "name": "Charge tax on this product", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "compare_at_price", "edit_type": "float-edit", "name": "Compare-at Price", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__shopify--discovery--product_recommendation__complementary_products", "edit_type": "productreferencelist-edit", "name": "Complementary Products", "group": "Search & Discovery App", "is_generate_active": false, "is_active": true, "is_import_active": false}, {"slug": "inventory_policy", "edit_type": "inventory_policy-edit", "name": "Continue Selling Inventory when Out of Stock", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "inventory_item__cost", "edit_type": "float-edit", "name": "Cost", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "inventory_item__country_code_of_origin", "edit_type": "countrycode-edit", "name": "Country of Origin", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__product_created_at", "edit_type": "text-edit", "name": "Date Created", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "product__body_html", "edit_type": "text-edit", "name": "Description", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__age_group", "edit_type": "google_age-edit", "name": "Google Shopping - Age Group", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__google_product_category", "edit_type": "google_category-edit", "name": "Google Shopping - Category", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__color", "edit_type": "text-edit", "name": "Google Shopping - Color", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__condition", "edit_type": "google_condition-edit", "name": "Google Shopping - Condition", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__custom_label_0", "edit_type": "text-edit", "name": "Google Shopping - Custom Label 0", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__custom_label_1", "edit_type": "text-edit", "name": "Google Shopping - Custom Label 1", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__custom_label_2", "edit_type": "text-edit", "name": "Google Shopping - Custom Label 2", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__custom_label_3", "edit_type": "text-edit", "name": "Google Shopping - Custom Label 3", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__custom_label_4", "edit_type": "text-edit", "name": "Google Shopping - Custom Label 4", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__custom_product", "edit_type": "google_iscustom-edit", "name": "Google Shopping - Custom Product", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__gender", "edit_type": "google_gender-edit", "name": "Google Shopping - Gender", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__mpn", "edit_type": "text-edit", "name": "Google Shopping - Manufacturer Part Number (MPN)", "group": "Google Shopping Fields", "is_generate_active": false, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__material", "edit_type": "text-edit", "name": "Google Shopping - Material", "group": "Google Shopping Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__size", "edit_type": "text-edit", "name": "Google Shopping - Size", "group": "Google Shopping Fields", "is_generate_active": false, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__size_system", "edit_type": "google_sizesystem-edit", "name": "Google Shopping - Size System", "group": "Google Shopping Fields", "is_generate_active": false, "is_active": true, "is_import_active": true}, {"slug": "product__metafields__mm-google-shopping__size_type", "edit_type": "google_sizetype-edit", "name": "Google Shopping - Size Type", "group": "Google Shopping Fields", "is_generate_active": false, "is_active": true, "is_import_active": true}, {"slug": "product__handle", "edit_type": "text-edit", "name": "<PERSON><PERSON> (URL)", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "inventory_item__harmonized_system_code", "edit_type": "text-edit", "name": "HS Tariff Code", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__images__position", "edit_type": "image-edit", "name": "Image Position", "group": "Product Fields", "is_generate_active": false, "is_active": false, "is_import_active": true}, {"slug": "product__images", "edit_type": "image-edit", "name": "Images", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__images__alt", "edit_type": "text-edit", "name": "Images Alt Text", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "inventory_item_id", "edit_type": "int-edit", "name": "Inventory Item ID", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "inventory_quantity", "edit_type": "integer-edit", "name": "Inventory Level", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "requires_shipping", "edit_type": "boolean-edit", "name": "Is Physical Product", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "custom_collections__title", "edit_type": "collection-edit", "name": "Manual Collection", "group": "Product Fields", "is_generate_active": false, "is_active": true, "is_import_active": true}, {"slug": "custom_collection__handle", "edit_type": "", "name": "Manual Collections", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "product__options__0__name", "edit_type": "text-edit", "name": "Option 1 Name", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "option1", "edit_type": "text-edit", "name": "Option 1 Value", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__options__1__name", "edit_type": "text-edit", "name": "Option 2 Name", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "option2", "edit_type": "text-edit", "name": "Option 2 Value", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__options__2__name", "edit_type": "text-edit", "name": "Option 3 Name", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "option3", "edit_type": "text-edit", "name": "Option 3 Value", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "price", "edit_type": "float-edit", "name": "Price", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__primary_image", "edit_type": "image-edit", "name": "Primary Image", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "product_id", "edit_type": "int", "name": "Product ID", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "product__product_type", "edit_type": "text-edit", "name": "Product Type (Custom)", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "inventory_item__province_code_of_origin", "edit_type": "text-edit", "name": "Province Code of Origin", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": false, "is_active": true, "is_import_active": false}, {"slug": "product__metafields__shopify--discovery--product_recommendation__related_products", "edit_type": "productreferencelist-edit", "name": "Related Products", "group": "Search & Discovery App", "is_generate_active": false, "is_active": true, "is_import_active": false}, {"slug": "product__metafields__shopify--discovery--product_recommendation__related_products_display", "edit_type": "relatedproductsetting-edit", "name": "Related Products Settings", "group": "Search & Discovery App", "is_generate_active": true, "is_active": true, "is_import_active": false}, {"slug": "product__metafields__global__description_tag", "edit_type": "text-edit", "name": "SEO Meta Description", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": false}, {"slug": "product__metafields__global__title_tag", "edit_type": "text-edit", "name": "SEO Meta Title", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": false}, {"slug": "sku", "edit_type": "text-edit", "name": "SKU", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "smart_collection__handle", "edit_type": "", "name": "Smart Collections", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "product__status", "edit_type": "status-edit", "name": "Status", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__tags", "edit_type": "tags-edit", "name": "Tags", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "tax_code", "edit_type": "text-edit", "name": "Tax Code", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__template_suffix", "edit_type": "templatesuffix-edit", "name": "Theme Template", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__title", "edit_type": "text-edit", "name": "Title", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__total_inventory", "edit_type": "int", "name": "Total Inventory", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "inventory_item__tracked", "edit_type": "track-quantity-edit", "name": "Track Quantity", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": false, "is_active": true, "is_import_active": false}, {"slug": "inventory_management", "edit_type": "inventory_management-edit", "name": "Track Quantity/Inventory", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "url_admin", "edit_type": "", "name": "URL (Shopify Admin)", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "url_web", "edit_type": "", "name": "URL (Web)", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "id", "edit_type": "integer-edit", "name": "Variant ID", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "image", "edit_type": "image-edit", "name": "Variant Image", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": false, "is_active": false, "is_import_active": true}, {"slug": "title", "edit_type": "text-edit", "name": "Variant Title", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": false, "is_import_active": false}, {"slug": "product__vendor", "edit_type": "text-edit", "name": "<PERSON><PERSON><PERSON>", "group": "Product Fields", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "product__published_at", "edit_type": "published-edit", "name": "Visible on Online Store (web)", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": true}, {"slug": "product__published_scope", "edit_type": "visibility-edit", "name": "Visible on Point of Sale (POS)", "group": "Product Fields", "is_generate_active": true, "is_active": false, "is_import_active": true}, {"slug": "grams", "edit_type": "integer-edit", "name": "Weight (in grams)", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "weight", "edit_type": "float-edit", "name": "Weight (in product's unit)", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}, {"slug": "weight_unit", "edit_type": "weightunit-edit", "name": "Weight Unit", "group": "<PERSON><PERSON><PERSON>", "is_generate_active": true, "is_active": true, "is_import_active": true}]}