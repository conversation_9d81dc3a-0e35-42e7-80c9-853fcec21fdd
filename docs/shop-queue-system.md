# Shop-Based Job Queue System

## 🤔 **Why Shop-Specific Queues?**

### **1. FIFO Order Guarantee Per Shop**
```
Without shop isolation:
Shop A: Job1 → Job2 → Job3
Shop B: Job4 → Job5 → Job6
Global Queue: [Job1, Job4, Job2, Job5, Job3, <PERSON>6] ❌ Mixed order!

With shop-specific queues:
Shop A Queue: [Job1, Job2, Job3] ✅ Guaranteed order
Shop B Queue: [Job4, Job5, Job6] ✅ Guaranteed order
```

**Why this matters**: If a shop owner creates multiple bulk edit jobs, they expect them to execute in the order they were created. Without shop isolation, Job2 from Shop A might run before Job1 from Shop A if Job1 takes longer.

### **2. Prevent Cross-Shop Interference**
```typescript
// Shop A has a huge job updating 10,000 products
// Shop B has a small job updating 5 products

// Without isolation: Shop B waits for Shop A's massive job
Global Queue: [ShopA-10k-products, ShopB-5-products] ❌

// With isolation: Shop B processes immediately
ShopA Queue: [10k-products-job]
ShopB Queue: [5-products-job] ✅ Runs in parallel
```

### **3. Shopify API Rate Limiting**
Each Shopify shop has **separate API rate limits**:
- GraphQL: 1000 points per shop per minute
- REST: 2 calls per second per shop

```typescript
// Shop-Isolated Worker Architecture
const WORKERS_PER_SHOP_QUEUE = 1; // Serialize jobs per shop
const MAX_CONCURRENT_SHOPS = 10; // Process multiple shops concurrently

// Each shop gets its own rate limiter
const shopRateLimiters = new Map<string, RateLimiter>()
```

### **4. Error Isolation**
```typescript
// If Shop A's job fails due to invalid product data,
// it doesn't affect Shop B's jobs

ShopA Queue: [Job1-FAILED, Job2-PENDING] ❌ Only affects Shop A
ShopB Queue: [Job3-SUCCESS, Job4-RUNNING] ✅ Continues normally
```

### **5. Scalability & Performance**
**Benefits**:
- **Horizontal scaling**: Can add more workers for busy shops
- **Resource allocation**: Prioritize premium customers
- **Monitoring**: Track performance per shop
- **Debugging**: Isolate issues to specific shops

### **6. Business Logic Requirements**
```typescript
// Real-world scenario:
// - Shop A: Premium customer, needs priority
// - Shop B: Free tier, can wait
// - Shop C: Has complex product variants, needs special handling

// With shop queues, we can:
ShopA Queue: { priority: 'high', workers: 3 }
ShopB Queue: { priority: 'low', workers: 1 }
ShopC Queue: { customProcessor: 'variantHandler' }
```

### **7. Data Consistency**
Each shop's data is completely isolated:
- No risk of updating Shop A's products with Shop B's data
- Easier to implement shop-specific business rules
- Simpler error recovery (retry only failed shop's jobs)

## 📋 **Current Implementation Status**

### ✅ **Completed (Task 1A.1)**
- **Job Queue Service** (`app/services/jobQueue.server.ts`)
  - Shop-specific queue creation with caching
  - Job enqueueing with error handling
  - TypeScript interfaces and utility functions
  - Comprehensive test coverage (7 tests passing)

### 🚧 **In Progress**
- **Job Processing Logic** (Task 1A.2) - Next step
- **Worker Process** (Task 1A.3) - Handles actual job execution
- **Integration** (Task 1A.4) - Connect to existing job creation flow

### 📁 **Key Files**
- `app/services/jobQueue.server.ts` - Core queue service
- `tests/services/jobQueue.test.ts` - Test suite
- `docs/development-roadmap.md` - Full implementation plan

## 🏗️ **Architecture**

### **Queue Naming Convention**
```
bull:bulk-update-{shop-domain}
```

**Examples:**
- `bull:bulk-update-myshop-myshopify-com`
- `bull:bulk-update-test-store-myshopify-com`
- `bull:bulk-update-demo-shop-myshopify-com`

### **Queue States Per Shop**
- `waiting` - Jobs queued for processing
- `active` - Currently running jobs
- `completed` - Successfully finished jobs
- `failed` - Jobs that encountered errors

## 🔄 **Processing Flow**

### **1. Job Creation**
```typescript
// Jobs are added to shop-specific queues
const shopQueue = getShopQueue('myshop.myshopify.com');
await shopQueue.add('bulk-update', jobData);
```

### **2. Worker Discovery**
```typescript
// Worker auto-discovers existing shop queues on startup
const existingQueues = await discoverExistingShopQueues();
console.log(`Found ${existingQueues.length} existing shop queues`);
```

### **3. Shop-Isolated Processing**
```typescript
// Each shop gets its own processor
shopQueue.process('bulk-update', 1, async (job) => {
  // Jobs for this shop process sequentially
  // Other shops process concurrently
});
```

### **4. Duplicate Prevention**
```typescript
// Redis locks prevent duplicate processing
const lockKey = `job-lock:${jobId}`;
const acquired = await redis.set(lockKey, 'locked', 'PX', 300000, 'NX');

if (!acquired) {
  console.log('Job already being processed by another worker');
  return;
}
```

## 🎯 **Implementation Benefits**

### **1. Robust Duplicate Prevention**
- Redis distributed locks prevent conflicts
- Safe to run multiple worker instances
- Automatic lock cleanup on completion

### **2. Auto-Discovery**
- Worker automatically finds and processes existing shop queues
- No manual queue setup required
- Seamless scaling as new shops are added

## 🔍 **Monitoring**

### **Redis Commander (http://localhost:8081)**

**Shop Queue Keys:**
```
bull:bulk-update-myshop-myshopify-com:waiting
bull:bulk-update-myshop-myshopify-com:active
bull:bulk-update-myshop-myshopify-com:completed
bull:bulk-update-myshop-myshopify-com:failed
```

**Lock Keys:**
```
job-lock:job-123
job-lock:job-456
```

### **Worker Logs**
```bash
npm run worker:dev

# Output shows:
🔍 Discovered queue: myshop-myshopify-com -> shop: myshop.myshopify.com
🔧 Setting up processing for: myshop.myshopify.com
✅ Processor registered for shop: myshop.myshopify.com
🚀 Starting Job 1 (job-123) for shop: myshop.myshopify.com
✅ Acquired lock for job job-123 (job-lock:job-123)
```

## 🛠️ **Maintenance Commands**

### **Clean Stale Locks**
```bash
# List all current locks
npm run cleanup-locks -- --list

# Clean stale locks (older than 5 minutes)
npm run cleanup-locks

# Force remove all locks (development only)
npm run cleanup-locks -- --force
```

### **Queue Inspection**
```bash
# Connect to Redis CLI
docker exec -it bpe-redis redis-cli

# List all shop queues
KEYS bull:bulk-update-*

# Count jobs for a specific shop
LLEN bull:bulk-update-myshop-myshopify-com:waiting
LLEN bull:bulk-update-myshop-myshopify-com:active

# View job data
HGETALL bull:bulk-update-myshop-myshopify-com:1
```

## 🚀 **Scaling Characteristics**

### **Horizontal Scaling**
- **Worker Instances**: Can run multiple worker instances safely
- **Shop Queues**: Each shop scales independently
- **Lock Coordination**: Redis ensures no duplicate processing

### **Performance**
- **Sequential Per Shop**: Guarantees order within shop
- **Concurrent Across Shops**: Maximizes throughput
- **Bulk Operations**: 50-100x faster than individual updates

### **Resource Usage**
- **Memory**: Minimal per shop queue
- **CPU**: IO-bound operations (Shopify API calls)
- **Network**: Rate-limited by Shopify (not by system)

## 🔧 **Configuration**

### **Queue Settings**
```typescript
const queue = new Bull(queueName, {
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
  },
  defaultJobOptions: {
    removeOnComplete: 100, // Keep last 100 completed jobs
    removeOnFail: 50,      // Keep last 50 failed jobs
    attempts: 3,           // Retry failed jobs up to 3 times
    backoff: {
      type: 'exponential',
      delay: 2000,         // Start with 2 second delay
    },
    delay: 0,              // No delay by default
  },
});
```

### **Worker Configuration**
```typescript
// Process 1 job at a time per shop (ensures order)
shopQueue.process('bulk-update', 1, processBulkUpdateJob);

// Redis lock timeout (5 minutes)
const LOCK_TIMEOUT_MS = 5 * 60 * 1000;

// Auto-discovery on startup
await setupExistingShopQueues();
```

## 🎯 **Best Practices**

### **Development**
1. **Use Redis Commander** to monitor job states in real-time
2. **Check worker logs** for processing details and errors
3. **Clean locks regularly** during development to avoid stale locks
4. **Test with multiple shops** to verify isolation

### **Production**
1. **Monitor queue depths** to detect processing issues
2. **Set up alerts** for failed jobs and long queue times
3. **Scale workers horizontally** as shop count grows
4. **Use managed Redis** for reliability and persistence

### **Debugging**
1. **Check Redis Commander** for job states and data
2. **Review worker logs** for processing details
3. **Verify locks** are being acquired and released properly
4. **Test job order** within individual shops

## 🎯 **Summary: Why Shop-Specific Queues Are Essential**

Shop-specific queues solve the fundamental challenge of **multi-tenant job processing** where:

1. **Each tenant (shop) needs guaranteed job ordering** - Shop owners expect their jobs to run in the order they created them
2. **Tenants shouldn't interfere with each other** - One shop's large job shouldn't block another shop's small job
3. **Each tenant has different rate limits and requirements** - Shopify enforces separate API limits per shop
4. **System needs to scale independently per tenant** - Premium shops can get more resources, free shops can wait

### **Real-World Impact**
```typescript
// Without shop isolation:
// - Shop A creates 3 jobs at 2:00 PM, 2:05 PM, 2:10 PM
// - Shop B creates 1 huge job at 2:07 PM
// - Result: Shop A's 2:10 PM job might run before their 2:05 PM job ❌

// With shop isolation:
// - Shop A's jobs always run in order: 2:00 → 2:05 → 2:10 ✅
// - Shop B's job runs in parallel, doesn't affect Shop A ✅
```

This architecture is **essential for any SaaS product serving multiple Shopify stores simultaneously**! It ensures predictable behavior, fair resource allocation, and optimal performance across all tenants.

This shop-based queue system provides the perfect balance of order guarantees, isolation, and performance for a multi-tenant Shopify app! 🏪⚡
