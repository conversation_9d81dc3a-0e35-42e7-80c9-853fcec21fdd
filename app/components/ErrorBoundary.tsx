import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Banner, Card, Text, BlockStack, Button } from '@shopify/polaris'
import { sanitizeText } from '~/utils/sanitization'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error?: Error
}

/**
 * Error Boundary component to catch and handle React errors gracefully
 * 
 * Features:
 * - Catches JavaScript errors anywhere in the child component tree
 * - Logs error details for debugging
 * - Shows user-friendly error message
 * - Provides retry functionality
 * - Customizable fallback UI
 */
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    // Ensure error is properly typed
    const normalizedError = error instanceof Error ? error : new Error(String(error))
    return { hasError: true, error: normalizedError }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo)
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined })
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default fallback UI
      return (
        <Card>
          <BlockStack gap="400">
            <Banner tone="critical">
              <BlockStack gap="200">
                <Text as="h3" variant="headingMd">
                  Something went wrong
                </Text>
                <Text as="p">
                  An unexpected error occurred. Please try again or contact support if the problem persists.
                </Text>
                {this.state.error && (
                  <Text as="p" tone="subdued" variant="bodySm">
                    Error: {sanitizeText(this.state.error.message || 'Unknown error')}
                  </Text>
                )}
              </BlockStack>
            </Banner>
            <Button onClick={this.handleRetry} variant="primary">
              Try Again
            </Button>
          </BlockStack>
        </Card>
      )
    }

    return this.props.children
  }
}

/**
 * Higher-order component to wrap components with error boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

/**
 * Hook-based error boundary for functional components
 */
export function useErrorHandler() {
  return (error: Error, errorInfo?: ErrorInfo) => {
    console.error('Error caught by useErrorHandler:', error, errorInfo)
    
    // In a real app, you might want to send this to an error reporting service
    // like Sentry, LogRocket, etc.
  }
}

export default ErrorBoundary
