import React, { useState, useCallback } from 'react';
import {
  Card,
  Button,
  Select,
  TextField,
  InlineStack,
  BlockStack,
  Text,
  Popover,
  ActionList,
  Box,
  Tooltip,
} from '@shopify/polaris';
import { PlusIcon, DeleteIcon } from '@shopify/polaris-icons';
import {
  FILTER_GROUPS,
  FILTER_OPERATORS,
  FilterValue,
  getFilterGroupById,
  getFilterFieldById,
} from '~/utils/filterFields';
import { convertAdvancedFiltersToSearchString } from '~/utils/filterConverter';

interface AdvancedFilterBuilderProps {
  filters: FilterValue[];
  onFiltersChange: (filters: FilterValue[]) => void;
  filterLogic?: 'AND' | 'OR';
  onFilterLogicChange?: (logic: 'AND' | 'OR') => void;
}

interface FilterRowProps {
  filter: FilterValue;
  onFilterChange: (filter: FilterValue) => void;
  onFilterRemove: () => void;
}

const FilterRow: React.FC<FilterRowProps> = ({ filter, onFilterChange, onFilterRemove }) => {
  const [groupPopoverActive, setGroupPopoverActive] = useState(false);
  const [fieldPopoverActive, setFieldPopoverActive] = useState(false);

  const selectedGroup = getFilterGroupById(filter.groupId);
  const selectedField = getFilterFieldById(filter.fieldId);

  const handleGroupSelect = useCallback((groupId: string) => {
    const group = getFilterGroupById(groupId);
    if (group && group.fields.length > 0) {
      const firstField = group.fields[0];
      onFilterChange({
        ...filter,
        groupId,
        fieldId: firstField.id,
        operator: firstField.operators[0],
        value: '',
      });
    }
    setGroupPopoverActive(false);
  }, [filter, onFilterChange]);

  const handleFieldSelect = useCallback((fieldId: string) => {
    const field = getFilterFieldById(fieldId);
    if (field) {
      onFilterChange({
        ...filter,
        fieldId,
        operator: field.operators[0],
        value: '',
      });
    }
    setFieldPopoverActive(false);
  }, [filter, onFilterChange]);

  const handleOperatorChange = useCallback((operator: string) => {
    onFilterChange({
      ...filter,
      operator: operator as any,
      value: '',
    });
  }, [filter, onFilterChange]);

  const handleValueChange = useCallback((value: string) => {
    onFilterChange({
      ...filter,
      value,
    });
  }, [filter, onFilterChange]);

  const handleSecondValueChange = useCallback((secondValue: string) => {
    onFilterChange({
      ...filter,
      secondValue,
    });
  }, [filter, onFilterChange]);

  const renderValueInput = () => {
    if (!selectedField) return null;

    // Boolean fields don't need value input
    if (selectedField.dataType === 'boolean') {
      return null;
    }

    // Custom fields with predefined options
    if (selectedField.options) {
      const options = selectedField.options.map(opt => ({
        label: opt.label,
        value: opt.value,
      }));
      
      return (
        <Select
          label=""
          options={[{ label: 'Select...', value: '' }, ...options]}
          value={filter.value as string}
          onChange={handleValueChange}
        />
      );
    }

    // Between operator needs two inputs
    if (filter.operator === 'between') {
      return (
        <InlineStack gap="200">
          <TextField
            label=""
            value={filter.value as string}
            onChange={handleValueChange}
            placeholder="From"
            autoComplete="off"
          />
          <Text as="span" variant="bodyMd">and</Text>
          <TextField
            label=""
            value={filter.secondValue || ''}
            onChange={handleSecondValueChange}
            placeholder="To"
            autoComplete="off"
          />
        </InlineStack>
      );
    }

    // Regular text/number input
    return (
      <TextField
        label=""
        value={filter.value as string}
        onChange={handleValueChange}
        placeholder={selectedField.placeholder}
        type={selectedField.dataType === 'number' ? 'number' : 'text'}
        autoComplete="off"
      />
    );
  };

  const groupActionItems = FILTER_GROUPS.map(group => ({
    content: group.name,
    onAction: () => handleGroupSelect(group.id),
  }));

  const fieldActionItems = selectedGroup ? selectedGroup.fields.map(field => ({
    content: field.name,
    onAction: () => handleFieldSelect(field.id),
  })) : [];

  const operatorOptions = selectedField ? selectedField.operators.map(op => ({
    label: FILTER_OPERATORS[op],
    value: op,
  })) : [];

  return (
    <Card>
      <InlineStack gap="300" align="center">
        {/* Step 1: Field Group */}
        <Box minWidth="150px">
          <Popover
            active={groupPopoverActive}
            activator={
              <Button
                onClick={() => setGroupPopoverActive(!groupPopoverActive)}
                disclosure
                fullWidth
                textAlign="left"
              >
                {selectedGroup ? selectedGroup.name : 'Select field type...'}
              </Button>
            }
            onClose={() => setGroupPopoverActive(false)}
          >
            <ActionList items={groupActionItems} />
          </Popover>
        </Box>

        {/* Step 2: Specific Field */}
        {selectedGroup && (
          <Box minWidth="150px">
            <Popover
              active={fieldPopoverActive}
              activator={
                <Button
                  onClick={() => setFieldPopoverActive(!fieldPopoverActive)}
                  disclosure
                  fullWidth
                  textAlign="left"
                >
                  {selectedField ? selectedField.name : 'Select field...'}
                </Button>
              }
              onClose={() => setFieldPopoverActive(false)}
            >
              <ActionList items={fieldActionItems} />
            </Popover>
          </Box>
        )}

        {/* Step 3: Operator */}
        {selectedField && (
          <Box minWidth="120px">
            <Select
              label=""
              options={operatorOptions}
              value={filter.operator}
              onChange={handleOperatorChange}
            />
          </Box>
        )}

        {/* Step 4: Value Input */}
        {selectedField && filter.operator && (
          <Box minWidth="200px">
            {renderValueInput()}
          </Box>
        )}

        {/* Remove Button */}
        <Button
          icon={DeleteIcon}
          onClick={onFilterRemove}
          accessibilityLabel="Remove filter"
        />
      </InlineStack>
    </Card>
  );
};

export const AdvancedFilterBuilder: React.FC<AdvancedFilterBuilderProps> = ({
  filters,
  onFiltersChange,
  filterLogic = 'AND',
  onFilterLogicChange,
}) => {
  const handleFilterChange = useCallback((index: number, newFilter: FilterValue) => {
    const newFilters = [...filters];
    newFilters[index] = newFilter;
    onFiltersChange(newFilters);
  }, [filters, onFiltersChange]);

  const handleFilterRemove = useCallback((index: number) => {
    const newFilters = filters.filter((_, i) => i !== index);
    onFiltersChange(newFilters);
  }, [filters, onFiltersChange]);

  const handleAddFilter = useCallback(() => {
    const newFilter: FilterValue = {
      groupId: '',
      fieldId: '',
      operator: 'is',
      value: '',
    };
    onFiltersChange([...filters, newFilter]);
  }, [filters, onFiltersChange]);

  return (
    <BlockStack gap="400">
      {filters.length === 0 && (
        <Box padding="400" background="bg-surface-secondary" borderRadius="200">
          <BlockStack gap="200" align="center">
            <Text variant="headingSm" as="h6" alignment="center">
              🎯 Build Advanced Filters
            </Text>
            <Text variant="bodySm" tone="subdued" alignment="center" as="p">
              Create sophisticated filters with multiple conditions and operators.
              Multiple filters use {filterLogic} logic - products must match {filterLogic === 'AND' ? 'ALL' : 'ANY'} conditions.
            </Text>
            <Button
              icon={PlusIcon}
              onClick={handleAddFilter}
              variant="primary"
              size="large"
            >
              Add your first filter
            </Button>
          </BlockStack>
        </Box>
      )}

      {filters.length > 0 && (
        <BlockStack gap="300">
          <InlineStack gap="200" align="space-between">
            <Text variant="headingSm" as="h6">
              Active Filters ({filters.length})
            </Text>
            <InlineStack gap="200" align="center">
              <Button
                icon={PlusIcon}
                onClick={handleAddFilter}
                variant="secondary"
                size="slim"
              >
                Add filter
              </Button>
              {/* Show toggle button after Add Filter when there are multiple filters */}
              {filters.length > 1 && onFilterLogicChange && (
                <Tooltip content={`Currently matching ${filterLogic === 'AND' ? 'ALL' : 'ANY'} conditions. Click to switch to ${filterLogic === 'AND' ? 'ANY' : 'ALL'}.`}>
                  <Button
                    onClick={() => onFilterLogicChange(filterLogic === 'AND' ? 'OR' : 'AND')}
                    size="slim"
                    variant={filterLogic === 'OR' ? 'primary' : 'secondary'}
                    pressed={filterLogic === 'OR'}
                  >
                    {filterLogic === 'AND' ? 'Match All' : 'Match Any'}
                  </Button>
                </Tooltip>
              )}
            </InlineStack>
          </InlineStack>

          <BlockStack gap="200">
            {filters.map((filter, index) => (
              <React.Fragment key={index}>
                <FilterRow
                  filter={filter}
                  onFilterChange={(newFilter) => handleFilterChange(index, newFilter)}
                  onFilterRemove={() => handleFilterRemove(index)}
                />
                {index < filters.length - 1 && (
                  <Box paddingInline="400">
                    <InlineStack align="center">
                      <Text variant="bodySm" tone="subdued" as="span" fontWeight="medium">
                        {filterLogic}
                      </Text>
                    </InlineStack>
                  </Box>
                )}
              </React.Fragment>
            ))}
          </BlockStack>

          {/* Debug: Show generated search query */}
          {filters.length > 0 && (
            <Box padding="200" background="bg-surface-secondary" borderRadius="100">
              <Text variant="bodySm" tone="subdued" as="p">
                <strong>Generated search:</strong> {convertAdvancedFiltersToSearchString(filters) || 'No valid filters'}
              </Text>
            </Box>
          )}
        </BlockStack>
      )}
    </BlockStack>
  );
};

export default AdvancedFilterBuilder;
