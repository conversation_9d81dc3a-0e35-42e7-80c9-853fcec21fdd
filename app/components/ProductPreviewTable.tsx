import { useFetcher } from "@remix-run/react";
import {
    Card,
    IndexTable,
    Text,
    useIndexResourceState,
    useBreakpoints,
    IndexTableProps,
    Thumbnail,
    Badge,
    Pagination,
    Box,
    BlockStack,
} from "@shopify/polaris";
import { Fragment } from "react";
import { ProductType } from "~/types/models";


type Props = {
    headings: IndexTableProps['headings'];
    products: ProductType[];
    pageInfo: {
        hasNextPage: boolean;
        hasPreviousPage: boolean;
        startCursor: string | null;
        endCursor: string | null;
    };
    fetcher: ReturnType<typeof useFetcher>;
    currentQuery?: string;
};

export default function ProductPreviewTable({ headings, products, pageInfo, fetcher, currentQuery }: Props) {
    const rows = (products || []).flatMap((product) => (product.variants?.nodes || []).map((v: any) => v));

    const resourceName = {
        singular: "variant",
        plural: "variants",
    };

    const { selectedResources, allResourcesSelected, handleSelectionChange } =
        useIndexResourceState(
            rows as unknown as { [key: string]: unknown }[],
            {
                resourceFilter: ({ disabled }) => !disabled,
            },
        );

    const rowMarkup = (products || []).map((product, groupIndex) => {
        const variantNodes = product.variants?.nodes || [];
        const allSelected = variantNodes.every((v: any) =>
            selectedResources.includes(v.id),
        );
        const someSelected = variantNodes.some((v: any) =>
            selectedResources.includes(v.id),
        );
        const selectableVariants = rows

        const rowRange: [number, number] = variantNodes.length > 0 ? [
            selectableVariants.findIndex((row) => row.id === variantNodes[0].id),
            selectableVariants.findIndex(
                (row) => row.id === variantNodes[variantNodes.length - 1].id,
            ),
        ] : [0, 0];

        const selected: "indeterminate" | boolean = allSelected
            ? true
            : someSelected
                ? "indeterminate"
                : false;

        return (
            <Fragment key={product.id}>
                <IndexTable.Row
                    id={`group-${product.id}`}
                    position={groupIndex * 100}
                    selected={selected}
                    selectionRange={rowRange}
                >
                    <IndexTable.Cell>
                        <Thumbnail
                            source="https://burst.shopifycdn.com/photos/<EMAIL>"
                            size="small"
                            alt="Black choker necklace"
                        />
                    </IndexTable.Cell>
                    <IndexTable.Cell>
                        <Text as="h6" fontWeight="semibold">{product.title}</Text>
                    </IndexTable.Cell>
                    <IndexTable.Cell>
                        <Badge tone="critical">{product.status}</Badge>
                    </IndexTable.Cell>
                    <IndexTable.Cell>
                        <Text as="p">{product.totalInventory}</Text>
                    </IndexTable.Cell>
                    <IndexTable.Cell>
                        <Text as="p">{`Category: ${product.category?.name || ''}`}<br />{`Vendor: ${product.vendor}`}</Text>
                    </IndexTable.Cell>
                </IndexTable.Row>

                {variantNodes.map((variant: any, i: number) => (
                    <IndexTable.Row
                        rowType="child"
                        key={variant.id}
                        id={variant.id}
                        position={groupIndex * 100 + i + 1}
                        selected={selectedResources.includes(variant.id)}
                    >
                        <IndexTable.Cell />
                        <IndexTable.Cell>
                            <Text as="p">{variant.title}</Text>
                        </IndexTable.Cell>
                        <IndexTable.Cell />
                        <IndexTable.Cell>
                            <Text as="p">{variant.inventoryQuantity}</Text>
                        </IndexTable.Cell>
                        <IndexTable.Cell>
                            <Text as="span">{`Price: ${variant.price}`} {variant.compareAtPrice && <s>{variant.compareAtPrice}</s>}</Text>
                        </IndexTable.Cell>
                    </IndexTable.Row>
                ))}
            </Fragment>
        );
    });

    return (
        <BlockStack>
            <Card padding="0">
                <IndexTable
                    condensed={useBreakpoints().smDown}
                    resourceName={resourceName}
                    itemCount={rows.length}
                    selectedItemsCount={
                        allResourcesSelected ? "All" : selectedResources.length
                    }
                    onSelectionChange={handleSelectionChange}
                    headings={headings}
                    pagination={{
                        onNext: () => {
                            if (pageInfo.hasNextPage && pageInfo.endCursor) {
                                fetcher.submit(
                                    { query: currentQuery ?? "", cursor: pageInfo.endCursor },
                                    { method: "post", encType: "application/json" }
                                );
                            }
                        },
                        hasNext: pageInfo.hasNextPage,
                        hasPrevious: pageInfo.hasPreviousPage,
                        onPrevious: () => {
                            if (pageInfo.hasPreviousPage && pageInfo.startCursor) {
                                fetcher.submit(
                                    { query: currentQuery ?? "", cursor: pageInfo.startCursor },
                                    { method: "post", encType: "application/json" }
                                );
                            }
                        }
                    }}
                >
                    {rowMarkup}
                </IndexTable>
            </Card>
        </BlockStack>
    );
}
