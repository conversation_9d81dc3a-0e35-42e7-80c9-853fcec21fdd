import { Link } from "@remix-run/react";
import { Text, Card, Button, BlockStack, InlineStack } from "@shopify/polaris";

export default function StartNewJob() {
    return (
        <Card>
            <BlockStack gap="400">
                <BlockStack gap="200">
                    <Text as="h2" variant="headingMd">
                        Update products with minimal efforts
                    </Text>
                    <Text variant="bodyMd" as="p">
                        Bulk edit products with 3 simple steps.<br />
                        Step 1: Filter & select products<br />
                        Step 2: Define modifications<br />
                        Step 3: Run/Schedule the job
                    </Text>
                </BlockStack>
                <InlineStack>
                    <Link to="select-products" className="cta">
                        <Button variant="primary">
                            Generate a product
                        </Button>
                    </Link>
                </InlineStack>
            </BlockStack>
        </Card>
    );
}