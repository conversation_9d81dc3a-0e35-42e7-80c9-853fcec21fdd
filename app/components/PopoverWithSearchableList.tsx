import React, { useState } from 'react';
import {
  Listbox,
  TextField,
  Icon,
  Popover,
  AutoSelection,
  Scrollable,
  EmptySearchResult,
  Text,
  Box,
} from '@shopify/polaris';
import { SearchIcon } from '@shopify/polaris-icons';


export enum SegmentType {
  HEADER, ITEM
}

type Segment = {
  id: string;
  value: string;
  label: string;
  type: SegmentType;
}

type PopoverWithSearchableListProps = {
  segments: Segment[];
  selectedSegmentIndex: number;
  onSegmentSelect: (segmentIndex: string) => void;
  activator: (onClick: () => void) => React.ReactElement;
};

export default function PopoverWithSearchableList(props: PopoverWithSearchableListProps) {
  const { segments, activator, onSegmentSelect, selectedSegmentIndex } = props;

  const [pickerOpen, setPickerOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [activeOptionId, setActiveOptionId] = useState(segments[0].id);
  const [filteredSegments, setFilteredSegments] = useState<typeof segments[number][]>([]);

  const handleFilterSegments = (query: string) => {
    const nextFilteredSegments = segments.filter((segment) => {
      if (!query)
        if (segment.type === SegmentType.HEADER) return true;
      return segment.label
        .toLocaleLowerCase()
        .includes(query.toLocaleLowerCase().trim());
    });

    setFilteredSegments(nextFilteredSegments);
  };

  const handleQueryChange = (query: string) => {
    setQuery(query);

    if (query.length >= 2) handleFilterSegments(query);
  };

  const handleQueryClear = () => {
    handleQueryChange('');
  };

  const handleOpenPicker = () => {
    setPickerOpen(true);
  };

  const handleClosePicker = () => {
    setPickerOpen(false);
    handleQueryChange('');
  };

  const handleSegmentSelect = (segmentIndex: string) => {
    onSegmentSelect(segmentIndex);
    handleClosePicker();
  };

  const handleActiveOptionChange = (_: string, domId: string) => {
    setActiveOptionId(domId);
  };

  const listboxId = 'SearchableListboxInPopover';

  const textFieldMarkup = (
    <div style={{ padding: '12px' }}>
      <StopPropagation>
        <TextField
          clearButton
          labelHidden
          label="Customer segments"
          placeholder="Search segments"
          autoComplete="off"
          value={query}
          prefix={<Icon source={SearchIcon} />}
          ariaActiveDescendant={activeOptionId}
          ariaControls={listboxId}
          onChange={handleQueryChange}
          onClearButtonClick={handleQueryClear}
        />
      </StopPropagation>
    </div>
  );

  const segmentOptions = query ? filteredSegments : segments;

  const segmentList =
    segmentOptions.length > 0
      ? segmentOptions
        .map(({ label, id, value, type }) => {
          const selected = segments[selectedSegmentIndex].id === id;
          if (type === SegmentType.HEADER) {
            return (
              <Box key={id} padding='300'>
                <Text as='p' variant='headingMd'>{label}</Text>
              </Box>
            )
          }

          return (
            <Listbox.Option key={id} value={value} selected={selected}>
              <Listbox.TextOption selected={selected}>
                {label}
              </Listbox.TextOption>
            </Listbox.Option>
          );
        })
      : null;

  const noResultsMarkup =
    segmentOptions.length === 0 ? (
      <EmptySearchResult
        title=""
        description={`No field found matching "${query}"`}
      />
    ) : null;

  const listboxMarkup = (
    <Listbox
      enableKeyboardControl
      autoSelection={AutoSelection.FirstSelected}
      accessibilityLabel="Search field to modify"
      customListId={listboxId}
      onSelect={handleSegmentSelect}
      onActiveOptionChange={handleActiveOptionChange}
    >
      {segmentList}
      {noResultsMarkup}
    </Listbox>
  );

  return (
    <Popover
      active={pickerOpen}
      activator={activator(handleOpenPicker)}
      ariaHaspopup="listbox"
      preferredAlignment="left"
      autofocusTarget="first-node"
      onClose={handleClosePicker}
    >
      <Popover.Pane fixed>
        <div
          style={{
            alignItems: 'stretch',
            borderTop: '1px solid #DFE3E8',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'stretch',
            position: 'relative',
            width: '100%',
            height: '100%',
            overflow: 'hidden',
          }}
        >
          {textFieldMarkup}

          <Scrollable
            shadow
            style={{
              position: 'relative',
              width: '310px',
              height: '292px',
              padding: 'var(--p-space-200) 0',
              borderBottomLeftRadius: 'var(--p-border-radius-200)',
              borderBottomRightRadius: 'var(--p-border-radius-200)',
            }}
          >
            {listboxMarkup}
          </Scrollable>
        </div>
      </Popover.Pane>
    </Popover>
  );
}

const StopPropagation = ({ children }: React.PropsWithChildren<any>) => {
  const stopEventPropagation = (event: React.MouseEvent | React.TouchEvent) => {
    event.stopPropagation();
  };

  return (
    <div onClick={stopEventPropagation} onTouchStart={stopEventPropagation}>
      {children}
    </div>
  );
};