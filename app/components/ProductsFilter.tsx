import { <PERSON>ton, BlockStack, InlineStack, Icon, TextField, Box, Divider } from "@shopify/polaris";
import { SearchIcon } from "@shopify/polaris-icons";
import { useCallback } from "react";


type ProductsFilterProps = {
    searchQuery: string;
    setSearchQuery: (value: string) => void;
};

export default function ProductsFilter({ searchQuery, setSearchQuery }: ProductsFilterProps) {
    const handleSearchInput = useCallback((value: string) => setSearchQuery(value), [setSearchQuery]);
    const handleClearButtonClick = useCallback(() => setSearchQuery(''), [setSearchQuery]);

    // Submit search query via URL params (GET)
    const handleSearch = useCallback(() => {
        const searchParams = new URLSearchParams();
        if (searchQuery.trim()) {
            searchParams.set('q', searchQuery.trim());
        }
        // Navigate to the same page with search params
        window.location.search = searchParams.toString();
    }, [searchQuery]);

    // Clear all filters (extend as needed)
    const handleClearAll = useCallback(() => {
        console.log("handleClearAll");
        setSearchQuery('');
        // Clear URL params
        window.location.search = '';
    }, [setSearchQuery]);

    return (
        <BlockStack gap="200">
            <Box paddingInline="200">
                <InlineStack align="center" blockAlign="center" gap="200">
                    <Icon source={SearchIcon} />
                    <div style={{ flexGrow: 1, minWidth: 0 }}>
                        <TextField
                            variant="borderless"
                            label=""
                            labelHidden
                            value={searchQuery}
                            onChange={handleSearchInput}
                            placeholder="Search by title, description, vendor, category, tags, etc."
                            autoComplete="off"
                            clearButton
                            onClearButtonClick={handleClearButtonClick}
                        />
                    </div>
                    <Button onClick={handleSearch}>Search</Button>
                </InlineStack>
            </Box>
            <Divider />
            <Box paddingInline="200">
                <InlineStack>
                    <Button variant="plain" onClick={handleClearAll}>Clear all</Button>
                </InlineStack>
            </Box>
        </BlockStack>
    );
}