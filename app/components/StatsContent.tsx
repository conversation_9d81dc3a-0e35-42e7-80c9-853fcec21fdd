import { BlockStack, IconSource, SkeletonDisplayText, Tooltip } from "@shopify/polaris";
import { Box, Card, Icon, InlineStack, Text } from "@shopify/polaris";
import { StatsCardType } from "~/types/enum";

const ToolTipContent = (tooltipData: { title: string, desc: string }) => {
    return (
        <Box padding="200">
            <BlockStack gap='200'>
                <Text variant="headingSm" as="h6">{tooltipData.title}</Text>
                <Text variant="bodyMd" as="span">{tooltipData.desc}</Text>
            </BlockStack>
        </Box>
    )
}

export const StatsContent = (
    type: StatsCardType, title: string, icon: IconSource, loading: boolean, value?: string,
    tooltipData?: { title: string, desc: string },
) => {
    return (
        <Box key={type} maxWidth="250px" width="250px">
            <Card padding="300">
                <BlockStack gap='100'>
                    <Box>
                        {tooltipData ? (
                            <Tooltip content={ToolTipContent(tooltipData)} preferredPosition='below' width='wide' hasUnderline>
                                <Text variant="headingSm" as="span">{title}</Text>
                            </Tooltip>
                        ) :
                            <Text variant="headingSm" as="span">{title}</Text>
                        }
                    </Box>
                    <InlineStack wrap={false} gap="200" blockAlign="start" align="start">
                        <Box key={`${type}-icon`}>
                            <Icon source={icon} tone="base" />
                        </Box>
                        {loading ? <Box key={`${type}-loading`} width="150px"><SkeletonDisplayText size="small" /></Box> : <Text key={`${type}-value`} as="p" variant="bodyMd">{value}</Text>}
                    </InlineStack>
                </BlockStack>
            </Card>
        </Box>
    );
}