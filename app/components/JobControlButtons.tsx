import { useState, useEffect, useMemo } from 'react'
import { useFetcher } from '@remix-run/react'
import {
  Button,
  InlineStack,
  Modal,
  Text,
  BlockStack,
} from '@shopify/polaris'
import { PlayIcon } from '@shopify/polaris-icons'
import type { JobDetail } from '~/types/models'
import { useJobToast, getErrorMessage, isNetworkError } from '~/contexts/ToastContext'
import { sanitizeText } from '~/utils/sanitization'

/**
 * Props for the JobControlButtons component
 */
export interface JobControlButtonsProps {
  /** The job to control. If null/undefined, no buttons will be rendered */
  job: JobDetail | null | undefined
  /** Visual variant of the buttons. Default renders full-size buttons, compact for headers, inline for tables */
  variant?: 'default' | 'compact' | 'inline'
  /** Callback fired when an action succeeds */
  onSuccess?: (message: string) => void
  /** Callback fired when an action fails */
  onError?: (error: string) => void
}

/**
 * JobControlButtons component provides Run/Stop controls for bulk edit jobs
 *
 * Features:
 * - Shows Run button for SCHEDULED jobs
 * - Shows Stop button for IN_PROGRESS jobs
 * - Loading states during API calls
 * - Confirmation modal for destructive stop action
 * - Toast notifications for success/error feedback
 * - Multiple visual variants for different contexts
 * - Full accessibility support
 *
 * @param props - Component props
 * @returns JSX element or null if job cannot be controlled
 */
export default function JobControlButtons({
  job,
  variant = 'default',
  onSuccess,
  onError,
}: JobControlButtonsProps) {
  const fetcher = useFetcher<{ success: boolean; message?: string; error?: string }>()
  const [showStopConfirmation, setShowStopConfirmation] = useState(false)
  const jobToast = useJobToast()

  // Memoize job title to prevent unnecessary re-renders
  const jobTitle = useMemo(() => sanitizeText(job?.title || 'Job'), [job?.title])

  // Handle fetcher response
  useEffect(() => {
    if (fetcher.data) {
      if (fetcher.data.success && fetcher.data.message) {
        // Determine which action was performed based on current fetcher state
        let action = fetcher.formData?.get('action')

        // If formData doesn't have action (JSON submission), try to get from JSON
        if (!action && fetcher.json && typeof fetcher.json === 'object' && fetcher.json !== null) {
          action = (fetcher.json as any).action
        }

        if (action === 'run') {
          jobToast.showJobStarted(jobTitle)
        } else if (action === 'stop') {
          jobToast.showJobStopped(jobTitle)
        } else {
          // Fallback for unknown actions
          jobToast.showGenericSuccess(fetcher.data.message)
        }

        onSuccess?.(fetcher.data.message)
      } else if (!fetcher.data.success && fetcher.data.error) {
        const errorMessage = getErrorMessage(fetcher.data.error)

        if (isNetworkError(fetcher.data.error)) {
          jobToast.showNetworkError()
        } else {
          jobToast.showJobError('perform action', errorMessage)
        }

        onError?.(errorMessage)
      }
    }
  }, [fetcher.data, onSuccess, onError, jobTitle, jobToast])

  // Don't render anything if job is null/undefined
  if (!job) {
    return null
  }

  // Determine if we should show run or stop button
  const canRun = job.status === 'SCHEDULED'
  const canStop = job.status === 'IN_PROGRESS' || job.status === 'SCHEDULED'

  // Don't render anything if job can't be controlled
  if (!canRun && !canStop) {
    return null
  }

  // Check if current action is loading
  const getSubmittedAction = () => {
    if (fetcher.formData?.get('action')) {
      return fetcher.formData.get('action')
    }

    // For JSON submissions, try to get from JSON
    if (fetcher.json && typeof fetcher.json === 'object' && fetcher.json !== null) {
      return (fetcher.json as any).action
    }

    return null
  }

  const submittedAction = getSubmittedAction()
  const isRunning = fetcher.state === 'submitting' && submittedAction === 'run'
  const isStopping = fetcher.state === 'submitting' && submittedAction === 'stop'

  const handleRunJob = () => {
    fetcher.submit(
      JSON.stringify({ action: 'run' }),
      {
        method: 'POST',
        action: `/api/jobs/${job.id}/actions`,
        encType: 'application/json',
      }
    )
  }

  const handleStopJob = () => {
    setShowStopConfirmation(true)
  }

  const confirmStopJob = () => {
    fetcher.submit(
      JSON.stringify({ action: 'stop' }),
      {
        method: 'POST',
        action: `/api/jobs/${job.id}/actions`,
        encType: 'application/json',
      }
    )
    setShowStopConfirmation(false)
  }

  const cancelStopJob = () => {
    setShowStopConfirmation(false)
  }

  const buttonSize = variant === 'compact' ? 'slim' : 'medium'
  const buttonVariant = variant === 'inline' ? 'tertiary' : 'primary'

  return (
    <>
      <InlineStack gap="200">
        {canRun && (
          <Button
            variant={buttonVariant}
            size={buttonSize}
            icon={PlayIcon}
            loading={isRunning}
            disabled={isRunning}
            onClick={handleRunJob}
            accessibilityLabel={`Run job: ${jobTitle}`}
            aria-busy={isRunning}
            data-variant={variant}
            tone="success"
          >
            Run Job
          </Button>
        )}

        {canStop && (
          <Button
            variant={buttonVariant}
            size={buttonSize}
            loading={isStopping}
            disabled={isStopping}
            onClick={handleStopJob}
            accessibilityLabel={`Stop job: ${jobTitle}`}
            aria-busy={isStopping}
            data-variant={variant}
            tone="critical"
          >
            Stop Job
          </Button>
        )}
      </InlineStack>

      {/* Stop Confirmation Modal */}
      <Modal
        open={showStopConfirmation}
        onClose={cancelStopJob}
        title="Confirm Stop Job"
        primaryAction={{
          content: 'Stop Job',
          onAction: confirmStopJob,
          destructive: true,
        }}
        secondaryActions={[
          {
            content: 'Cancel',
            onAction: cancelStopJob,
          },
        ]}
      >
        <Modal.Section>
          <BlockStack gap="300">
            <Text as="p" id="stop-job-modal-description">
              Are you sure you want to stop the job "{jobTitle}"?
            </Text>
            <Text as="p" tone="subdued">
              This action will cancel the job and any remaining product updates will not be processed.
              This action cannot be undone.
            </Text>
          </BlockStack>
        </Modal.Section>
      </Modal>

    </>
  )
}
