import {
    Text,
    But<PERSON>,
    BlockStack,
    InlineStack,
    Icon,
    TextField,
    Box,
    Divider,
    Tag,
    Popover,
    ActionList,
    Select,
    FormLayout,
    Card
} from "@shopify/polaris";
import { SearchIcon, PlusIcon } from "@shopify/polaris-icons";
import { useCallback, useState } from "react";
import AdvancedFilterBuilder from "./AdvancedFilterBuilder";
import { FilterValue, getFilterFieldById, FILTER_OPERATORS } from "~/utils/filterFields";

interface Filter {
    id: string;
    label: string;
    value: string;
    type: 'status' | 'vendor' | 'product_type' | 'tag' | 'inventory' | 'price' | 'custom';
    displayValue?: string;
    group?: string;
}

type EnhancedProductsFilterProps = {
    searchQuery: string;
    setSearchQuery: (value: string) => void;
    activeFilters?: Filter[];
    onFiltersChange?: (filters: Filter[]) => void;
    onSearch?: (query: string) => void;
    // New advanced filtering props
    advancedFilters?: FilterValue[];
    onAdvancedFiltersChange?: (filters: FilterValue[]) => void;
    filterMode?: 'simple' | 'advanced';
    onFilterModeChange?: (mode: 'simple' | 'advanced') => void;
    // Filter logic props
    filterLogic?: 'AND' | 'OR';
    onFilterLogicChange?: (logic: 'AND' | 'OR') => void;
};

// PHASE 1: Fully Supported Shopify Search Filters
const FILTER_OPTIONS = [
    // ✅ STATUS FILTERS
    {
        id: 'status-active',
        label: 'Show Active Products Only',
        value: 'status:ACTIVE',
        type: 'status' as const,
        group: 'Product Status'
    },
    {
        id: 'status-draft',
        label: 'Show Draft Products Only',
        value: 'status:DRAFT',
        type: 'status' as const,
        group: 'Product Status'
    },
    {
        id: 'status-archived',
        label: 'Show Archived Products Only',
        value: 'status:ARCHIVED',
        type: 'status' as const,
        group: 'Product Status'
    },

    // ✅ BASIC FIELD FILTERS
    {
        id: 'has-vendor',
        label: 'Products with Vendor Set',
        value: 'vendor:*',
        type: 'custom' as const,
        group: 'Product Information'
    },
    {
        id: 'has-product-type',
        label: 'Products with Product Type Set',
        value: 'product_type:*',
        type: 'custom' as const,
        group: 'Product Information'
    },
    {
        id: 'has-tags',
        label: 'Products with Tags',
        value: 'tag:*',
        type: 'custom' as const,
        group: 'Product Information'
    },
    {
        id: 'is-published',
        label: 'Published Products Only',
        value: 'published_at:*',
        type: 'custom' as const,
        group: 'Product Information'
    },

    // ✅ DATE FILTERS
    {
        id: 'created-this-year',
        label: 'Created in 2024',
        value: 'created_at:>2024-01-01',
        type: 'custom' as const,
        group: 'Recently Modified'
    },
    {
        id: 'created-this-month',
        label: 'Created This Month',
        value: 'created_at:>2024-12-01',
        type: 'custom' as const,
        group: 'Recently Modified'
    },
    {
        id: 'updated-recently',
        label: 'Updated in Last 7 Days',
        value: `updated_at:>${new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}`,
        type: 'custom' as const,
        group: 'Recently Modified'
    },

    // ✅ ADDITIONAL USEFUL FILTERS
    {
        id: 'has-handle',
        label: 'Products with Custom URL Handle',
        value: 'handle:*',
        type: 'custom' as const,
        group: 'Product Information'
    },
    {
        id: 'gift-card',
        label: 'Gift Cards Only',
        value: 'gift_card:true',
        type: 'custom' as const,
        group: 'Special Product Types'
    },
];

export default function EnhancedProductsFilter({
    searchQuery,
    setSearchQuery,
    activeFilters = [],
    onFiltersChange,
    onSearch,
    advancedFilters = [],
    onAdvancedFiltersChange,
    filterMode = 'simple',
    onFilterModeChange,
    filterLogic = 'AND',
    onFilterLogicChange
}: EnhancedProductsFilterProps) {
    const [addFilterPopoverActive, setAddFilterPopoverActive] = useState(false);

    // Form-based filter states
    const [filterType, setFilterType] = useState('');
    const [filterValue, setFilterValue] = useState('');

    // Filter type options for the dropdown
    const filterTypeOptions = [
        { label: 'Choose filter type...', value: '' },
        { label: 'Product Status', value: 'status' },
        { label: 'Vendor', value: 'vendor' },
        { label: 'Product Type', value: 'product_type' },
        { label: 'Tags', value: 'tag' },
        { label: 'Price Range', value: 'price' },
        { label: 'Created Date', value: 'created_at' },
        { label: 'Updated Date', value: 'updated_at' },
    ];

    // Helper functions for dynamic labels and placeholders
    const getFilterValueLabel = (type: string): string => {
        switch (type) {
            case 'status': return 'Status';
            case 'vendor': return 'Vendor Name';
            case 'product_type': return 'Product Type';
            case 'tag': return 'Tag';
            case 'price': return 'Price (e.g., >10.00, <50.00, 10.00..50.00)';
            case 'created_at': return 'Created After (YYYY-MM-DD)';
            case 'updated_at': return 'Updated After (YYYY-MM-DD)';
            default: return 'Value';
        }
    };

    const getFilterValuePlaceholder = (type: string): string => {
        switch (type) {
            case 'status': return 'e.g., ACTIVE, DRAFT, ARCHIVED';
            case 'vendor': return 'e.g., Nike, Adidas';
            case 'product_type': return 'e.g., Shoes, T-Shirts';
            case 'tag': return 'e.g., summer, sale, featured';
            case 'price': return 'e.g., >10.00 or 10.00..50.00';
            case 'created_at': return 'e.g., 2024-01-01';
            case 'updated_at': return 'e.g., 2024-01-01';
            default: return 'Enter value...';
        }
    };

    const handleSearchInput = useCallback((value: string) => setSearchQuery(value), [setSearchQuery]);
    const handleClearButtonClick = useCallback(() => setSearchQuery(''), [setSearchQuery]);

    // Submit search query and filters via callback
    const handleSearch = useCallback(() => {
        // Build combined query
        let combinedQuery = '';
        if (searchQuery.trim()) {
            combinedQuery = searchQuery.trim();
        }

        // Add active filters
        if (activeFilters.length > 0) {
            const filterQuery = activeFilters.map(f => f.value).join(' ');
            combinedQuery = combinedQuery
                ? `${combinedQuery} ${filterQuery}`
                : filterQuery;
        }

        // Call the search callback instead of navigation
        onSearch?.(combinedQuery);
    }, [searchQuery, activeFilters, onSearch]);

    // Clear all filters and search
    const handleClearAll = useCallback(() => {
        setSearchQuery('');
        onFiltersChange?.([]);
        onAdvancedFiltersChange?.([]);

        // Clear search via callback
        onSearch?.('');
    }, [setSearchQuery, onFiltersChange, onAdvancedFiltersChange, onSearch]);

    // Add a predefined filter
    const handleAddFilter = useCallback((filterOption: typeof FILTER_OPTIONS[0]) => {
        const newFilter: Filter = {
            id: filterOption.id,
            label: filterOption.label,
            value: filterOption.value,
            type: filterOption.type,
            displayValue: filterOption.label
        };

        const updatedFilters = [...activeFilters, newFilter];
        onFiltersChange?.(updatedFilters);
        setAddFilterPopoverActive(false);

        // Don't auto-search - wait for explicit Search button press
    }, [activeFilters, onFiltersChange]);

    // Add form-based filter
    const handleAddFormFilter = useCallback(() => {
        if (!filterType || !filterValue.trim()) return;

        // Build the filter value based on type
        let searchValue = '';
        let displayLabel = '';

        switch (filterType) {
            case 'status':
                searchValue = `status:${filterValue.toUpperCase()}`;
                displayLabel = `Status: ${filterValue}`;
                break;
            case 'vendor':
                searchValue = `vendor:"${filterValue}"`;
                displayLabel = `Vendor: ${filterValue}`;
                break;
            case 'product_type':
                searchValue = `product_type:"${filterValue}"`;
                displayLabel = `Product Type: ${filterValue}`;
                break;
            case 'tag':
                searchValue = `tag:"${filterValue}"`;
                displayLabel = `Tag: ${filterValue}`;
                break;
            case 'price':
                searchValue = `price:${filterValue}`;
                displayLabel = `Price: ${filterValue}`;
                break;
            case 'created_at':
                searchValue = `created_at:>${filterValue}`;
                displayLabel = `Created after: ${filterValue}`;
                break;
            case 'updated_at':
                searchValue = `updated_at:>${filterValue}`;
                displayLabel = `Updated after: ${filterValue}`;
                break;
            default:
                return;
        }

        const newFilter: Filter = {
            id: `${filterType}-${Date.now()}`,
            label: displayLabel,
            value: searchValue,
            type: filterType as any,
            displayValue: displayLabel
        };

        const updatedFilters = [...activeFilters, newFilter];
        onFiltersChange?.(updatedFilters);
        setFilterType('');
        setFilterValue('');
        setAddFilterPopoverActive(false);

        // Don't auto-search - wait for explicit Search button press
    }, [filterType, filterValue, activeFilters, onFiltersChange]);

    // Remove a filter
    const handleRemoveFilter = useCallback((filterId: string) => {
        const updatedFilters = activeFilters.filter(f => f.id !== filterId);
        onFiltersChange?.(updatedFilters);

        // Don't auto-search - wait for explicit Search button press
    }, [activeFilters, onFiltersChange]);

    const toggleAddFilterPopover = useCallback(
        () => setAddFilterPopoverActive(!addFilterPopoverActive),
        [addFilterPopoverActive]
    );

    // Filter out already active filters from options
    const availableFilterOptions = FILTER_OPTIONS.filter(
        option => !activeFilters.some(filter => filter.id === option.id)
    );

    const addFilterActivator = (
        <Button 
            icon={PlusIcon} 
            onClick={toggleAddFilterPopover}
            variant="tertiary"
            size="slim"
        >
            Add filter
        </Button>
    );

    return (
        <BlockStack gap="300">
            {/* Search Bar */}
            <Box paddingInline="200">
                <InlineStack align="center" blockAlign="center" gap="200">
                    <Icon source={SearchIcon} />
                    <div style={{ flexGrow: 1, minWidth: 0 }}>
                        <TextField
                            variant="borderless"
                            label=""
                            labelHidden
                            value={searchQuery}
                            onChange={handleSearchInput}
                            placeholder="Search by title, description, vendor, category, tags, etc."
                            autoComplete="off"
                            clearButton
                            onClearButtonClick={handleClearButtonClick}
                        />
                    </div>
                    <Button
                        onClick={handleSearch}
                        variant="primary"
                        size="large"
                    >
                        {(activeFilters.length > 0 || advancedFilters.length > 0) ?
                            `Search with ${activeFilters.length + advancedFilters.length} filter${(activeFilters.length + advancedFilters.length) > 1 ? 's' : ''}` :
                            'Search'
                        }
                    </Button>
                </InlineStack>
            </Box>

            {/* Filter Mode Toggle - Make it prominent */}
            <Box paddingInline="200">
                <Card>
                    <Box padding="300">
                        <InlineStack gap="300" align="space-between">
                            <InlineStack gap="300" align="center">
                                <Text variant="headingMd" as="h6">
                                    {filterMode === 'advanced' ? '🎯 Advanced Filters' : '📝 Quick Filters'}
                                </Text>
                                <Button
                                    variant={filterMode === 'advanced' ? 'primary' : 'secondary'}
                                    onClick={() => onFilterModeChange?.(filterMode === 'simple' ? 'advanced' : 'simple')}
                                    size="medium"
                                >
                                    {filterMode === 'simple' ? '⚡ Switch to Advanced' : '📝 Switch to Quick'}
                                </Button>
                                {filterMode === 'advanced' && (
                                    <Text variant="bodySm" tone="subdued" as="span">
                                        Build sophisticated filters with multiple conditions
                                    </Text>
                                )}
                            </InlineStack>

                            {/* Clear All Button */}
                            {(activeFilters.length > 0 || advancedFilters.length > 0 || searchQuery) && (
                                <Button variant="plain" onClick={handleClearAll} size="medium">
                                    Clear all filters
                                </Button>
                            )}
                        </InlineStack>
                    </Box>
                </Card>
            </Box>

            {/* Filter Pills and Add Filter */}
            <Box paddingInline="200">
                <InlineStack gap="200" wrap={true} align="start">
                    {/* Active Filter Pills */}
                    {activeFilters.map((filter) => (
                        <Tag
                            key={filter.id}
                            onRemove={() => handleRemoveFilter(filter.id)}
                        >
                            {filter.displayValue || filter.label}
                        </Tag>
                    ))}

                    {/* Advanced Filter Pills */}
                    {advancedFilters.map((filter, index) => {
                        const field = getFilterFieldById(filter.fieldId);
                        const operator = FILTER_OPERATORS[filter.operator];
                        const displayValue = field ? `${field.name} ${operator} ${filter.value}` : 'Advanced Filter';

                        return (
                            <Tag
                                key={`advanced-${index}`}
                                onRemove={() => {
                                    const newFilters = advancedFilters.filter((_, i) => i !== index);
                                    onAdvancedFiltersChange?.(newFilters);
                                }}
                            >
                                {displayValue}
                            </Tag>
                        );
                    })}

                    {/* Add Filter Button - Only show in simple mode */}
                    {filterMode === 'simple' && (
                        <Popover
                            active={addFilterPopoverActive}
                            activator={addFilterActivator}
                            onClose={toggleAddFilterPopover}
                            preferredAlignment="left"
                        >
                            <Box padding="300" minWidth="400px" maxWidth="500px">
                                <BlockStack gap="300">
                                    {/* Simple Form-based Filter Input */}
                                    <Box>
                                        <Text variant="headingMd" as="h6" fontWeight="semibold">Add Filter</Text>
                                            <Box paddingBlockStart="200">
                                                <Text variant="bodySm" tone="subdued" as="p">
                                                    Choose what you want to filter by and enter the value
                                                </Text>
                                            </Box>
                                            <Box paddingBlockStart="300">
                                                <FormLayout>
                                                    <Select
                                                        label="Filter by"
                                                        options={filterTypeOptions}
                                                        value={filterType}
                                                        onChange={setFilterType}
                                                    />
                                                    {filterType && (
                                                        <TextField
                                                            label={getFilterValueLabel(filterType)}
                                                            value={filterValue}
                                                            onChange={setFilterValue}
                                                            placeholder={getFilterValuePlaceholder(filterType)}
                                                            autoComplete="off"
                                                        />
                                                    )}
                                                    <InlineStack gap="200">
                                                        <Button
                                                            onClick={handleAddFormFilter}
                                                            disabled={!filterType || !filterValue.trim()}
                                                            variant="primary"
                                                            size="medium"
                                                        >
                                                            Add Filter
                                                        </Button>
                                                        <Button
                                                            onClick={() => {
                                                                setFilterType('');
                                                                setFilterValue('');
                                                            }}
                                                            size="medium"
                                                        >
                                                            Clear
                                                </Button>
                                            </InlineStack>
                                        </FormLayout>
                                    </Box>
                                </Box>

                                        <Divider />

                                        {/* Predefined Filters - Grouped */}
                                        <Box>
                                            <Text variant="headingMd" as="h6" fontWeight="semibold">Quick Filters</Text>
                                            <Box paddingBlockStart="200">
                                                <Text variant="bodySm" tone="subdued" as="p">
                                                    Click any option below to instantly filter your products
                                                </Text>
                                            </Box>
                                            {/* Group filters by category */}
                                            {Object.entries(
                                                availableFilterOptions.reduce((groups, option) => {
                                                    const group = option.group || 'Other';
                                                    if (!groups[group]) groups[group] = [];
                                                    groups[group].push(option);
                                                    return groups;
                                                }, {} as Record<string, typeof availableFilterOptions>)
                                            ).map(([groupName, groupOptions]) => (
                                                <Box key={groupName} paddingBlockStart="300">
                                                    <Text variant="bodyMd" as="p" fontWeight="medium" tone="subdued">{groupName}</Text>
                                                    <Box paddingBlockStart="100">
                                                        <ActionList
                                                            items={groupOptions.map(option => ({
                                                                content: option.label,
                                                                onAction: () => handleAddFilter(option)
                                                            }))}
                                                        />
                                                    </Box>
                                                </Box>
                                            ))}
                                        </Box>
                                </BlockStack>
                            </Box>
                        </Popover>
                    )}
                </InlineStack>
            </Box>

            {/* Advanced Filter Builder - Show when in advanced mode */}
            {filterMode === 'advanced' && (
                <Box paddingInline="200">
                    <Card>
                        <Box padding="400">
                            <BlockStack gap="300">
                                <Text variant="headingMd" as="h6" fontWeight="semibold">
                                    Advanced Filter Builder
                                </Text>
                                <Text variant="bodySm" tone="subdued" as="p">
                                    Build sophisticated filters with multiple conditions and operators
                                </Text>
                                <AdvancedFilterBuilder
                                    filters={advancedFilters}
                                    onFiltersChange={onAdvancedFiltersChange || (() => {})}
                                    filterLogic={filterLogic}
                                    onFilterLogicChange={onFilterLogicChange}
                                />
                            </BlockStack>
                        </Box>
                    </Card>
                </Box>
            )}

            {/* Filter Status and Results Summary */}
            {(activeFilters.length > 0 || advancedFilters.length > 0) && (
                <Box paddingInline="200">
                    <Box padding="200" background="bg-surface-caution" borderRadius="200">
                        <InlineStack gap="200" align="center">
                            <Text variant="bodySm" as="p" fontWeight="medium">
                                ⚠️ {activeFilters.length + advancedFilters.length} filter{(activeFilters.length + advancedFilters.length) > 1 ? 's' : ''} set
                            </Text>
                            <Text variant="bodySm" tone="subdued" as="span">
                                Click "Search" to apply filters
                            </Text>
                        </InlineStack>
                    </Box>
                </Box>
            )}

            {/* Search Query Summary */}
            {searchQuery && (
                <Box paddingInline="200">
                    <Text variant="bodySm" tone="subdued" as="p">
                        Searching for "{searchQuery}"
                    </Text>
                </Box>
            )}
        </BlockStack>
    );
}
