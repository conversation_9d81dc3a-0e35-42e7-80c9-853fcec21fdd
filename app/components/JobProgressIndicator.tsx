import {
    <PERSON>,
    BlockStack,
    <PERSON><PERSON><PERSON>,
    Divider,
    InlineStack,
    Spinner,
} from "@shopify/polaris";
import type { JobDetail } from "~/types/models";
import { useJobRealTimeUpdates } from "~/hooks/useShopifyRealTimeUpdates";

export interface JobProgressIndicatorProps {
    /**
     * The job to display progress for
     */
    job: JobDetail;
    
    /**
     * Whether to show the divider above the progress section
     * @default true
     */
    showDivider?: boolean;
    
    /**
     * Size of the progress bar
     * @default "small"
     */
    progressBarSize?: "small" | "medium" | "large";
    
    /**
     * Whether to show live updates indicator
     * @default true
     */
    showLiveIndicator?: boolean;
    
    /**
     * Callback when job status changes
     */
    onStatusChange?: (job: JobDetail, previousStatus: string) => void;
    
    /**
     * Callback when job progress updates
     */
    onProgressUpdate?: (job: JobDetail, progress: number) => void;
    
    /**
     * Callback when job completes
     */
    onJobComplete?: (job: JobDetail) => void;
    
    /**
     * Callback when job fails
     */
    onJobFailed?: (job: JobDetail, error?: string) => void;
}

/**
 * JobProgressIndicator component displays real-time progress for bulk edit jobs
 * 
 * Features:
 * - Real-time progress updates using Shopify-compatible polling
 * - Progress bar with percentage display
 * - Live updates indicator when polling is active
 * - Customizable appearance and callbacks
 * - Only shows for jobs in progress
 * 
 * @param props - Component props
 * @returns JSX element or null if job is not in progress
 */
export default function JobProgressIndicator({
    job,
    showDivider = true,
    progressBarSize = "small",
    showLiveIndicator = true,
    onStatusChange,
    onProgressUpdate,
    onJobComplete,
    onJobFailed,
}: JobProgressIndicatorProps) {
    // Set up real-time updates for the job
    const { isPolling } = useJobRealTimeUpdates(job, {
        onStatusChange,
        onProgressUpdate,
        onJobComplete,
        onJobFailed,
    });

    // Only show progress for jobs that are in progress
    if (job.status !== 'IN_PROGRESS') {
        return null;
    }

    // Calculate progress percentage
    const progressPercentage = job.totalProducts > 0
        ? Math.round((job.processedProducts / job.totalProducts) * 100)
        : 0;

    return (
        <>
            {showDivider && <Divider />}
            <BlockStack gap="200">
                <InlineStack align="space-between" blockAlign="center">
                    <Text variant="bodyMd" as="p">
                        <strong>Progress:</strong> {job.processedProducts} / {job.totalProducts}
                    </Text>
                    {showLiveIndicator && isPolling && (
                        <InlineStack gap="100" blockAlign="center">
                            <Spinner size="small" />
                            <Text as="span" variant="bodyMd" tone="subdued">
                                Live updates
                            </Text>
                        </InlineStack>
                    )}
                </InlineStack>
                
                <ProgressBar 
                    progress={progressPercentage} 
                    size={progressBarSize} 
                />
                
                <Text variant="bodyMd" as="p" tone="subdued">
                    {progressPercentage}% complete
                </Text>
            </BlockStack>
        </>
    );
}
