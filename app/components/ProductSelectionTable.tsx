import {
    Card,
    IndexTable,
    DataTable,
    Text,
    useBreakpoints,
    IndexTableProps,
    Thumbnail,
    Badge,
    Box,
    BlockStack,
    Link,
    Checkbox,
    InlineStack,
    Button,
} from "@shopify/polaris";
import { Fragment, useState } from "react";
import { ProductType } from "~/types/models";
import { VARIANT_DISPLAY_CONFIG } from "~/data/graphql/getProducts";


type Props = {
    headings: IndexTableProps['headings'];
    products: ProductType[];
    pageInfo: {
        hasNextPage: boolean;
        hasPreviousPage: boolean;
        startCursor: string | null;
        endCursor: string | null;
    };
    seeAllVariants: (productId: string) => void;
    unselectedIds: Set<string>;
    onNextPage: () => void;
    onPreviousPage: () => void;
    onSelectionChange: (checked: boolean, productId: string, variantId?: string) => void;
    openProductDetail: (productGid: string) => void;
};

export default function ProductSelectionTable(
    { headings, products, pageInfo, seeAllVariants,
        unselectedIds, onNextPage, onPreviousPage, onSelectionChange,
        openProductDetail }: Props
) {
    // Track which products have expanded variants
    const [expandedProducts, setExpandedProducts] = useState<Set<string>>(new Set());
    // Early return if products is not properly initialized
    if (!products || !Array.isArray(products)) {
        console.warn('ProductSelectionTable: products is not an array:', products);
        return (
            <Card>
                <DataTable
                    columnContentTypes={['text', 'text', 'text', 'text']}
                    headings={headings?.map(h => h.title) || []}
                    rows={[]}
                />
            </Card>
        );
    }

    const allVariants = products.flatMap((product) => (product.variants?.nodes || []).map((v) => v));

    const rowMarkup = products.map((product, groupIndex) => {
        const productUnselected = unselectedIds.has(product.id);
        const variantNodes = product.variants?.nodes || [];
        const allVariantsUnselected = variantNodes.every((v) => unselectedIds.has(v.id));
        const someVariantsUnselected = variantNodes.some((v) => unselectedIds.has(v.id));

        var productSelected: "indeterminate" | boolean = !productUnselected;
        if (!allVariantsUnselected && someVariantsUnselected) {
            productSelected = "indeterminate";
        }

        const firstImage = product.images.nodes?.[0];
        return (
            <Fragment key={product.id}>
                <IndexTable.Row
                    id={`group-${product.id}`}
                    position={groupIndex * 100}
                >
                    <IndexTable.Cell colSpan={1}>
                        <InlineStack align="space-between">
                            <Checkbox
                                label=""
                                checked={productSelected}
                                onChange={(checked) => onSelectionChange(checked, product.id)}
                            />
                            <Box paddingInlineEnd={'200'}>
                                <Thumbnail
                                    source={firstImage?.url || ""}
                                    size="small"
                                    alt={firstImage?.altText || ""}
                                />
                            </Box>
                        </InlineStack>
                    </IndexTable.Cell>
                    <IndexTable.Cell colSpan={6}>
                        <Link
                            dataPrimaryLink
                            onClick={() => openProductDetail(product.id)}
                        >
                            <Text as="h6" fontWeight="semibold">{product.title}</Text>
                        </Link>
                    </IndexTable.Cell>
                    <IndexTable.Cell colSpan={2}>
                        <Badge tone="critical">{product.status}</Badge>
                    </IndexTable.Cell>
                    <IndexTable.Cell colSpan={2}>
                        <Text as="p">{product.totalInventory}</Text>
                    </IndexTable.Cell>
                    <IndexTable.Cell colSpan={6}>
                        <Text as="p">{`Category: ${product.category?.name || ''}`}<br />{`Vendor: ${product.vendor}`}</Text>
                    </IndexTable.Cell>
                </IndexTable.Row>

                {/* Show variants based on configuration and expansion state */}
                {(() => {
                    const allVariants = product.variants?.nodes || [];
                    const isExpanded = expandedProducts.has(product.id);
                    const variantsToShow = isExpanded
                        ? allVariants
                        : allVariants.slice(0, VARIANT_DISPLAY_CONFIG.initialVariantsToShow);

                    return variantsToShow.map((variant, i) => (
                        <IndexTable.Row
                            rowType="child"
                            key={variant.id}
                            id={variant.id}
                            position={groupIndex * 100 + i + 1}
                        >
                            <IndexTable.Cell colSpan={1}>
                                <Checkbox
                                    label=""
                                    checked={!unselectedIds.has(variant.id)}
                                    onChange={(checked) => onSelectionChange(checked, product.id, variant.id)}
                                />
                            </IndexTable.Cell>
                            <IndexTable.Cell colSpan={6}>
                                <Text as="p">{variant.title}</Text>
                            </IndexTable.Cell>
                            <IndexTable.Cell colSpan={2} />
                            <IndexTable.Cell colSpan={2}>
                                <Text as="p">{variant.inventoryQuantity}</Text>
                            </IndexTable.Cell>
                            <IndexTable.Cell colSpan={6}>
                                <Text as="span">{`Price: ${variant.price}`} {variant.compareAtPrice && <s>{variant.compareAtPrice}</s>}</Text>
                            </IndexTable.Cell>
                        </IndexTable.Row>
                    ));
                })()}

                {/* Show more/less variants button */}
                {(() => {
                    const allVariants = product.variants?.nodes || [];
                    const hasMoreVariants = allVariants.length > VARIANT_DISPLAY_CONFIG.initialVariantsToShow;
                    const isExpanded = expandedProducts.has(product.id);
                    const hasIncompleteData = product.variants?.pageInfo?.hasNextPage;

                    if (!hasMoreVariants && !hasIncompleteData) return null;

                    return (
                        <IndexTable.Row
                            rowType="child"
                            key={`${product.id}-show-more`}
                            id={`${product.id}-show-more`}
                            position={groupIndex * 100 + allVariants.length + 1}
                        >
                            <IndexTable.Cell />
                            <IndexTable.Cell colSpan={16}>
                                <InlineStack gap="200" blockAlign="center">
                                    {hasMoreVariants && (
                                        <Button
                                            variant="plain"
                                            onClick={() => {
                                                const newExpanded = new Set(expandedProducts);
                                                if (isExpanded) {
                                                    newExpanded.delete(product.id);
                                                } else {
                                                    newExpanded.add(product.id);
                                                }
                                                setExpandedProducts(newExpanded);
                                            }}
                                        >
                                            {isExpanded
                                                ? `Show less variants`
                                                : `Show ${allVariants.length - VARIANT_DISPLAY_CONFIG.initialVariantsToShow} more variants`
                                            }
                                        </Button>
                                    )}

                                    {hasIncompleteData && (
                                        <Link onClick={() => seeAllVariants(product.id)}>
                                            <Text as="span" variant="bodySm" tone="subdued">
                                                Load all variants from API
                                            </Text>
                                        </Link>
                                    )}
                                </InlineStack>
                            </IndexTable.Cell>
                        </IndexTable.Row>
                    );
                })()}

                {/* Legacy "See all variants" for API pagination - keep for compatibility */}
                {false && product.variants?.pageInfo?.hasNextPage && (
                    <IndexTable.Row
                        rowType="child"
                        key={`${product.id}-load-more`}
                        id={`${product.id}-load-more`}
                        position={groupIndex * 100 + (product.variants?.nodes || []).length + 1}
                        hideSelectable
                    >
                        <IndexTable.Cell />
                        <IndexTable.Cell colSpan={16}>
                            <Link onClick={() => seeAllVariants(product.id)}>
                                <Text as="p">
                                    {`See all variants of ${product.title}`}
                                </Text>
                            </Link>
                        </IndexTable.Cell>
                    </IndexTable.Row>
                )}
            </Fragment>
        );
    });

    return (
        <Card padding="0">
            <BlockStack>
                {/* <Box paddingBlock={'200'} paddingInline={'400'}>
                    <Text as="p" variant="bodyMd">{`${selectedVariants.length} Selected Products`}</Text>
                </Box> */}
                <IndexTable
                    condensed={useBreakpoints().smDown}
                    resourceName={{ singular: "product", plural: "products" }}
                    itemCount={allVariants.length}
                    headings={headings}
                    selectable={false}
                    hasZebraStriping
                    pagination={{
                        onNext: () => {
                            if (pageInfo.hasNextPage && pageInfo.endCursor) {
                                onNextPage();
                            }
                        },
                        hasNext: pageInfo.hasNextPage,
                        hasPrevious: pageInfo.hasPreviousPage,
                        onPrevious: () => {
                            if (pageInfo.hasPreviousPage && pageInfo.startCursor) {
                                onPreviousPage();
                            }
                        }
                    }}
                >
                    {rowMarkup}
                </IndexTable>
            </BlockStack>
        </Card>
    );
}
