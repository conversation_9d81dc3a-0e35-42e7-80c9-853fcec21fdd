import { InlineStack } from "@shopify/polaris";
import { CashDollarIcon, ClockIcon, CollectionListIcon, ProductIcon } from '@shopify/polaris-icons';
import { StatsCardType } from "~/types/enum";
import { StatsCard, StatsResponse } from "~/types/models";
import { useFetcher } from "@remix-run/react";
import { useEffect } from "react";
import { StatsContent } from "./StatsContent";

const statsCards: StatsCard[] = [
    {
        "title": "Bulk edits",
        "type": StatsCardType.TOTAL_JOBS,
        "icon": CollectionListIcon,
    },
    {
        "title": "Time saved",
        "type": StatsCardType.TIME_SAVED,
        "icon": ClockIcon,
        "tooltip": {
            "title": "Operational Time Savings",
            "desc": "Time saved by doing bulk edits compared to manually editing each field."
        }
    },
    {
        "title": "Products updated",
        "type": StatsCardType.PRODUCTS_UPDATED,
        "icon": ProductIcon
    },
    {
        "title": "Plan",
        "type": StatsCardType.PLAN_DETAIL,
        "icon": CashDollarIcon
    }
]

export function StatsComponent() {
    const fetcher = useFetcher<StatsResponse>();

    useEffect(() => {
        // Only load if there's no submission in progress and no data yet
        if (fetcher.state === "idle" && !fetcher.data) {
            fetcher.load("/api/stats");
        }
    }, [fetcher]);

    return (
        <InlineStack gap="400" wrap>
            {statsCards.map((data) => {
                var value: string | undefined;
                switch (data.type) {
                    case StatsCardType.TOTAL_JOBS:
                        value = `${fetcher.data?.totalJobs}`;
                        break;

                    case StatsCardType.TIME_SAVED:
                        value = fetcher.data?.timeSaved;
                        break;

                    case StatsCardType.PRODUCTS_UPDATED:
                        value = `${fetcher.data?.productsUpdated}`;
                        break;

                    case StatsCardType.PLAN_DETAIL:
                        value = fetcher.data?.plan;
                        break;

                    default:
                        break;
                }
                return StatsContent(
                    data.type,
                    data.title,
                    data.icon,
                    fetcher.state === "loading",
                    value,
                    data.tooltip
                );
            })}
        </InlineStack>
    );
}