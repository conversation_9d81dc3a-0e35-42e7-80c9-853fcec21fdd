import { use<PERSON><PERSON><PERSON>, useNavi<PERSON>, <PERSON> } from '@remix-run/react';
import {
    Card,
    Text,
    InlineStack,
    Box,
    Button,
    Badge,
    IndexTable,
    EmptySearchResult,
    Spinner,
} from '@shopify/polaris';
import { useEffect } from 'react';
import { JobDetail } from '~/types/models';
import { useJobsRealTimeUpdates } from '~/hooks/useShopifyRealTimeUpdates';

export default function RecentJobsList() {
    const fetcher = useFetcher<JobDetail[]>();
    const navigate = useNavigate();

    useEffect(() => {
        // Only load if there's no submission in progress and no data yet
        if (fetcher.state === "idle" && !fetcher.data) {
            fetcher.load("/api/jobs"); // Path to your resource route
        }
    }, [fetcher]);

    // Set up real-time updates for recent jobs
    const jobs = fetcher.data || [];
    const { isPolling } = useJobsRealTimeUpdates(jobs, {
        onStatusChange: (_updatedJob: JobDetail, _previousStatus: string) => {
            // Status changes are handled automatically through revalidation
            // Additional status change handling can be added here if needed
        },
        onProgressUpdate: (_updatedJob: JobDetail, _progress: number) => {
            // Progress updates are handled automatically through revalidation
        },
    });

    const resourceName = {
        singular: 'Active and recent bulk edits',
        plural: 'Active and recent bulk edits',
    };

    const emptyStateMarkup = (
        fetcher.state === 'loading' ?
            <InlineStack align='center'>
                <Spinner accessibilityLabel="Loading recent jobs" size="large" />
            </InlineStack>
            : <EmptySearchResult
                title={'No jobs found'}
                description={''}
                withIllustration
            />
    );

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'SCHEDULED':
                return <Badge tone="attention">Scheduled</Badge>;
            case 'IN_PROGRESS':
                return <Badge tone="info">In Progress</Badge>;
            case 'COMPLETED':
                return <Badge tone="success">Completed</Badge>;
            case 'FAILED':
                return <Badge tone="critical">Failed</Badge>;
            case 'CANCELLED':
                return <Badge>Cancelled</Badge>;
            default:
                return <Badge>{status}</Badge>;
        }
    }

    const formatDateTime = (dateString?: string) => {
        if (!dateString) return 'Not set';
        const date = new Date(dateString);
        // Use a consistent format to avoid hydration mismatches
        return date.toLocaleString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
        });
    }

    return (
        <Card padding="0">
            {/* Top Header */}
            <Box paddingBlock="200" paddingInline="300">
                <InlineStack align="space-between" blockAlign="center">
                    <InlineStack gap="200" blockAlign="center">
                        <Text as="h2" variant="headingMd">
                            Active and recent bulk edits
                        </Text>
                        {isPolling && (
                            <InlineStack gap="100" blockAlign="center">
                                <Spinner size="small" />
                                <Text as="span" variant="bodyMd" tone="subdued">
                                    Live updates
                                </Text>
                            </InlineStack>
                        )}
                    </InlineStack>
                    <Button variant="primary" onClick={() => navigate('/app/jobs')}>See all</Button>
                </InlineStack>
            </Box>

            <IndexTable
                resourceName={resourceName}
                itemCount={jobs.length}
                selectable={false}
                emptyState={emptyStateMarkup}
                headings={[
                    { title: 'Job Name' },
                    { title: 'Time' },
                    { title: 'Status' },
                    { title: 'Products Affected' },
                ]}
                hasZebraStriping
            >
                {jobs.map((job, index) => (
                    <IndexTable.Row
                        id={job.id}
                        key={job.id}
                        position={index}
                    >
                        <IndexTable.Cell>
                            <Link to={`/app/jobs/${job.id}`}>
                                <Text as='span' variant="bodyMd" tone="base">
                                    {job.title}
                                </Text>
                            </Link>
                        </IndexTable.Cell>
                        <IndexTable.Cell>
                            <Text as="span" variant="bodyMd">
                                {formatDateTime(job.scheduledAt || job.createdAt)}
                            </Text>
                        </IndexTable.Cell>
                        <IndexTable.Cell>{getStatusBadge(job.status)}</IndexTable.Cell>
                        <IndexTable.Cell>
                            <Text as="span" variant="bodyMd">
                                {job.totalProducts} products
                            </Text>
                        </IndexTable.Cell>
                    </IndexTable.Row>
                ))}
            </IndexTable>
        </Card>
    );
}
