import { BlockStack, Checkbox, IndexTable, Text, useBreakpoints, Button, InlineStack } from "@shopify/polaris";
import { Fragment, useState } from "react";
import { ProductType } from "~/types/models";
import { VARIANT_DISPLAY_CONFIG } from "~/data/graphql/getProducts";

type Props = {
    product: ProductType;
    unselectedIds: Set<string>;
    onSelectionChange: (checked: boolean, productId: string, variantId?: string) => void;
};

export default function ProductVariantTable({ product, unselectedIds, onSelectionChange }: Props) {
    const [showAllVariants, setShowAllVariants] = useState(false);

    const totalVariants = product.variants.nodes.length;
    const hasMoreVariants = totalVariants > VARIANT_DISPLAY_CONFIG.initialVariantsToShow;
    const hasIncompleteData = product.variants.pageInfo?.hasNextPage;



    // Determine which variants to show
    const variantsToShow = showAllVariants
        ? product.variants.nodes
        : product.variants.nodes.slice(0, VARIANT_DISPLAY_CONFIG.initialVariantsToShow);

    const rowMarkup = variantsToShow.map((variant, index) => {
        return (
            <Fragment key={`model-variant-${variant.id}`}>
                <IndexTable.Row
                    key={`model-row-${variant.id}`}
                    id={`model-row-${variant.id}`}
                    position={index}
                >
                    <IndexTable.Cell>
                        <Checkbox
                            label=""
                            checked={!unselectedIds.has(variant.id)}
                            onChange={(checked) => onSelectionChange(checked, product.id, variant.id)}
                        />
                    </IndexTable.Cell>
                    <IndexTable.Cell>
                        <Text as="p">{variant.title}</Text>
                    </IndexTable.Cell>
                    <IndexTable.Cell>
                        <Text as="p">{variant.inventoryQuantity}</Text>
                    </IndexTable.Cell>
                    <IndexTable.Cell>
                        <Text as="span">{`Price: ${variant.price}`} {variant.compareAtPrice && <s>{variant.compareAtPrice}</s>}</Text>
                    </IndexTable.Cell>
                </IndexTable.Row>
            </Fragment>
        );
    });

    return (
        <BlockStack gap="400">
            <IndexTable
                condensed={useBreakpoints().smDown}
                resourceName={{ singular: "variant", plural: "variants" }}
                itemCount={variantsToShow.length}
                headings={[
                    { title: "", id: "checkbox" },
                    { title: "Title", id: "title" },
                    { title: "Inventory", id: "inventory" },
                    { title: "Info", id: "info" }
                ]}
                selectable={false}
                hasZebraStriping
            >
                {rowMarkup}
            </IndexTable>

            {/* Show more/less variants controls */}
            {hasMoreVariants && (
                <InlineStack gap="200" blockAlign="center">
                    <Button
                        variant="plain"
                        onClick={() => setShowAllVariants(!showAllVariants)}
                    >
                        {showAllVariants
                            ? `Show less variants`
                            : `Show ${totalVariants - VARIANT_DISPLAY_CONFIG.initialVariantsToShow} more variants`
                        }
                    </Button>

                    {hasIncompleteData && !showAllVariants && (
                        <Text as="span" variant="bodySm" tone="subdued">
                            (Some variants may not be shown due to API limits)
                        </Text>
                    )}
                </InlineStack>
            )}
        </BlockStack>
    )
}