import Bull from 'bull'

/**
 * Job Queue Service for Shopify Bulk Product Editor
 *
 * This service provides shop-isolated job queues using Bull/Redis.
 * Each shop gets its own queue to ensure FIFO ordering and prevent
 * cross-shop interference.
 */

// Types for job data
export interface BulkUpdateJobData {
  jobId: string
  shopDomain: string
  modifications: Array<{
    fieldType: string
    fieldName: string
    fieldValue: string
  }>
  productIds: string[]
  options: {
    batchSize: number
    delayBetweenBatches: number
  }
}

export interface JobResult {
  bullJobId: string
  jobId: string
  queueName: string
}

// Cache for shop queues to avoid creating multiple instances
const shopQueues = new Map<string, Bull.Queue>()

/**
 * Clear queue cache (useful for testing)
 */
export function clearQueueCache(): void {
  shopQueues.clear()
}

/**
 * Get or create a shop-specific queue
 */
export function getShopQueue(shopDomain: string): Bull.Queue {
  // Use consistent naming with getQueueName function
  const queueName = getQueueName(shopDomain)

  // Return existing queue if already created
  if (shopQueues.has(queueName)) {
    return shopQueues.get(queueName)!
  }

  // Create new queue with Redis configuration
  const queue = new Bull(queueName, {
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
    },
    defaultJobOptions: {
      removeOnComplete: 100,
      removeOnFail: 50,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
    },
  })

  // Cache the queue
  shopQueues.set(queueName, queue)

  return queue
}

/**
 * Enqueue a bulk update job for a specific shop
 */
export async function enqueueBulkUpdateJob(jobData: BulkUpdateJobData): Promise<JobResult> {
  try {
    const queue = getShopQueue(jobData.shopDomain)

    // Add job to queue with specific options
    const bullJob = await queue.add('bulk-update', jobData, {
      jobId: jobData.jobId,
      removeOnComplete: true,
      removeOnFail: false,
    })

    return {
      bullJobId: bullJob.id as string,
      jobId: jobData.jobId,
      queueName: getQueueName(jobData.shopDomain),
    }
  } catch (error) {
    console.error(`Failed to enqueue job ${jobData.jobId} for shop ${jobData.shopDomain}:`, error)
    throw error
  }
}

/**
 * Get the normalized queue name for a shop domain
 *
 * QUEUE NAMING STRATEGY:
 * - Format: `bpe-{sanitizedDomain}` (bpe = Bulk Product Editor)
 * - Removes dots and hyphens for Bull/Redis compatibility
 * - Examples:
 *   - "test-shop.myshopify.com" → "bpe-testshopmyshopifycom"
 *   - "my.test.shop.myshopify.com" → "bpe-mytestshopmyshopifycom"
 *
 * WHY THIS FORMAT:
 * 1. **Bull Compatibility**: Bull 3.x works better with simple queue names
 * 2. **Redis Keys**: Avoids issues with special characters in Redis key names
 * 3. **Uniqueness**: Each shop gets its own isolated queue
 * 4. **Consistency**: Centralized naming logic used throughout the app
 *
 * 📚 **See**: docs/current-status-and-next-steps.md - "Bull Queue Configuration Guide"
 */
export function getQueueName(shopDomain: string): string {
  // Simplify queue name - remove dots and hyphens for Bull compatibility
  const sanitized = shopDomain.replace(/[.-]/g, '')
  return `bpe-${sanitized}`
}
