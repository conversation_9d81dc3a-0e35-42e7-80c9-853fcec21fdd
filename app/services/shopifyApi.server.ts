import { RateLimiter } from 'limiter'
import { apiVersion } from '~/shopify.server'

export interface ShopifyApiError extends Error {
  status?: number
  statusText?: string
}

export interface GraphQLResponse {
  data?: any
  errors?: Array<{
    message: string
    locations?: Array<{ line: number; column: number }>
    path?: string[]
  }>
}

export interface ProductInput {
  id: string
  title?: string
  vendor?: string
  productType?: string
  tags?: string[]
  status?: 'ACTIVE' | 'ARCHIVED' | 'DRAFT'
}

export interface ProductVariantInput {
  id: string
  price?: string
  compareAtPrice?: string
  sku?: string
  inventoryQuantity?: number
  weight?: number
  weightUnit?: 'GRAMS' | 'KILOGRAMS' | 'OUNCES' | 'POUNDS'
}

// Interface for Shopify's ProductVariantsBulkInput (matches official GraphQL schema)
export interface ProductVariantsBulkInput {
  id?: string  // Optional for create, required for update
  price?: string  // Money scalar - we'll pass as string and let Shopify convert
  compareAtPrice?: string  // Money scalar - we'll pass as string and let Shopify convert
  barcode?: string
  sku?: string
  taxable?: boolean
  taxCode?: string
  inventoryPolicy?: 'DENY' | 'CONTINUE'
  inventoryQuantities?: Array<{
    locationId: string
    quantity: number
  }>
  mediaId?: string
  mediaSrc?: string[]
  metafields?: Array<{
    namespace: string
    key: string
    value: string
    type: string
  }>
  optionValues?: Array<{
    name?: string
    optionName?: string
    optionId?: string
    id?: string
  }>
  requiresComponents?: boolean
  showUnitPrice?: boolean
  unitPriceMeasurement?: {
    measuredType?: 'VOLUME' | 'WEIGHT' | 'DIMENSION'
    quantityUnit?: string
    quantityValue?: number
    referenceUnit?: string
    referenceValue?: number
  }
  inventoryItem?: {
    cost?: string
    tracked?: boolean
  }
}

export interface BulkUpdateResult {
  success: boolean
  updatedCount: number
  errors: string[]
}

export class ShopifyApiService {
  public readonly shopDomain: string
  public readonly accessToken: string
  private rateLimiter: RateLimiter

  constructor(shopDomain: string, accessToken: string) {
    if (!this.validateShopDomain(shopDomain)) {
      throw new Error(`Invalid shop domain format: ${shopDomain}`)
    }
    if (!this.validateAccessToken(accessToken)) {
      throw new Error('Invalid access token format')
    }

    this.shopDomain = shopDomain
    this.accessToken = accessToken

    // Conservative rate limiting: 10 requests per second for GraphQL API
    // Shopify allows 100-1000 points/second, each mutation ~10 points
    this.rateLimiter = new RateLimiter({ tokensPerInterval: 10, interval: 'second' })
  }

  /**
   * Make a GraphQL request to Shopify Admin API
   */
  async graphqlRequest(query: string, variables: Record<string, any> = {}): Promise<any> {
    // Wait for rate limiter
    await this.rateLimiter.removeTokens(1)

    const url = this.getGraphQLEndpoint()
    
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': this.accessToken,
        },
        body: JSON.stringify({
          query,
          variables,
        }),
      })

      if (!response.ok) {
        const errorText = await response.text()
        const error = new Error(
          `HTTP Error ${response.status}: ${response.statusText}${errorText ? ` - ${errorText}` : ''}`
        ) as ShopifyApiError
        error.status = response.status
        error.statusText = response.statusText
        throw error
      }

      const result: GraphQLResponse = await response.json()

      // Handle GraphQL errors
      if (result.errors && result.errors.length > 0) {
        const errorMessages = result.errors.map(err => err.message).join(', ')
        throw new Error(`GraphQL Error: ${errorMessages}`)
      }

      return result
    } catch (error) {
      // Re-throw with context
      if (error instanceof Error) {
        throw error
      }
      throw new Error(`Network error: ${String(error)}`)
    }
  }

  /**
   * Update a product using GraphQL mutation
   */
  async updateProduct(productData: ProductInput): Promise<any> {
    if (!productData.id) {
      throw new Error('Product ID is required')
    }

    const mutation = `
      mutation productUpdate($input: ProductInput!) {
        productUpdate(input: $input) {
          product {
            id
            title
            vendor
            status
          }
          userErrors {
            field
            message
          }
        }
      }
    `

    const result = await this.graphqlRequest(mutation, { input: productData })
    
    // Handle user errors from Shopify
    if (result.data?.productUpdate?.userErrors?.length > 0) {
      const errorMessages = result.data.productUpdate.userErrors.map((err: any) => err.message).join(', ')
      throw new Error(`Shopify API Error: ${errorMessages}`)
    }

    return result.data?.productUpdate
  }

  /**
   * Update a product variant using GraphQL mutation (uses bulk API)
   */
  async updateProductVariant(variantData: ProductVariantsBulkInput): Promise<any> {
    if (!variantData.id) {
      throw new Error('Product variant ID is required')
    }

    // Extract product ID from variant ID (assuming format: gid://shopify/ProductVariant/123)
    // We need the product ID for the bulk update API
    const productId = await this.getProductIdFromVariant(variantData.id)

    const mutation = `
      mutation productVariantsBulkUpdate($productId: ID!, $variants: [ProductVariantsBulkInput!]!) {
        productVariantsBulkUpdate(productId: $productId, variants: $variants) {
          productVariants {
            id
            price
            sku
            inventoryQuantity
          }
          userErrors {
            field
            message
          }
        }
      }
    `

    const result = await this.graphqlRequest(mutation, {
      productId,
      variants: [variantData]
    })

    // Handle user errors from Shopify
    if (result.data?.productVariantsBulkUpdate?.userErrors?.length > 0) {
      const errorMessages = result.data.productVariantsBulkUpdate.userErrors.map((err: any) => err.message).join(', ')
      throw new Error(`Shopify API Error: ${errorMessages}`)
    }

    return result.data?.productVariantsBulkUpdate?.productVariants?.[0]
  }

  /**
   * Get product ID from variant ID by querying the variant
   */
  private async getProductIdFromVariant(variantId: string): Promise<string> {
    const query = `
      query getVariantProduct($id: ID!) {
        productVariant(id: $id) {
          product {
            id
          }
        }
      }
    `

    const result = await this.graphqlRequest(query, { id: variantId })

    if (!result.data?.productVariant?.product?.id) {
      throw new Error(`Could not find product for variant ${variantId}`)
    }

    return result.data.productVariant.product.id
  }

  /**
   * Update multiple product variants efficiently using bulk mutation
   * This is the primary method mentioned in the roadmap for bulk operations
   */
  async updateProductVariants(productId: string, variants: ProductVariantsBulkInput[]): Promise<any> {
    if (!productId) {
      throw new Error('Product ID is required')
    }

    if (!variants || variants.length === 0) {
      throw new Error('At least one variant is required')
    }

    // Shopify allows up to 100 variants per productVariantsBulkUpdate mutation
    if (variants.length > 100) {
      throw new Error('Maximum 100 variants allowed per bulk update')
    }

    const mutation = `
      mutation productVariantsBulkUpdate($productId: ID!, $variants: [ProductVariantsBulkInput!]!) {
        productVariantsBulkUpdate(productId: $productId, variants: $variants) {
          productVariants {
            id
            price
            sku
            inventoryQuantity
          }
          userErrors {
            field
            message
          }
        }
      }
    `

    const result = await this.graphqlRequest(mutation, { productId, variants })

    // Handle user errors from Shopify
    if (result.data?.productVariantsBulkUpdate?.userErrors?.length > 0) {
      const errorMessages = result.data.productVariantsBulkUpdate.userErrors.map((err: any) => err.message).join(', ')
      throw new Error(`Shopify API Error: ${errorMessages}`)
    }

    return result.data?.productVariantsBulkUpdate
  }

  /**
   * Update multiple product variants efficiently using bulk mutation (alias for backward compatibility)
   */
  async updateProductVariantsBulk(productId: string, variants: ProductVariantInput[]): Promise<any> {
    return this.updateProductVariants(productId, variants)
  }

  /**
   * Process multiple products with optimized batching and error handling
   */
  async batchUpdateProducts(products: ProductInput[]): Promise<BulkUpdateResult> {
    const results: BulkUpdateResult = {
      success: true,
      updatedCount: 0,
      errors: []
    }

    for (const product of products) {
      try {
        await this.updateProduct(product)
        results.updatedCount++
      } catch (error) {
        results.success = false
        const errorMessage = error instanceof Error ? error.message : String(error)
        results.errors.push(`Product ${product.id}: ${errorMessage}`)
      }
    }

    return results
  }

  /**
   * Process multiple variants with optimized batching and error handling
   */
  async batchUpdateProductVariants(variants: ProductVariantInput[]): Promise<BulkUpdateResult> {
    const results: BulkUpdateResult = {
      success: true,
      updatedCount: 0,
      errors: []
    }

    for (const variant of variants) {
      try {
        await this.updateProductVariant(variant)
        results.updatedCount++
      } catch (error) {
        results.success = false
        const errorMessage = error instanceof Error ? error.message : String(error)
        results.errors.push(`Variant ${variant.id}: ${errorMessage}`)
      }
    }

    return results
  }

  /**
   * Get the GraphQL API endpoint URL
   */
  private getGraphQLEndpoint(): string {
    return `https://${this.shopDomain}/admin/api/${apiVersion}/graphql.json`
  }

  /**
   * Validate shop domain format
   */
  private validateShopDomain(domain: string): boolean {
    return /^[a-zA-Z0-9][a-zA-Z0-9-]*\.myshopify\.com$/.test(domain)
  }

  /**
   * Validate access token format
   */
  private validateAccessToken(token: string): boolean {
    return token.length > 0 && /^[a-zA-Z0-9_-]+$/.test(token)
  }
}
