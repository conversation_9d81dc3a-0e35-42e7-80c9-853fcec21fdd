import db from '~/db.server'
import { sessionStorage } from '~/shopify.server'
import type { BulkUpdateJobData } from './jobQueue.server'
import { getProductsFromShopify } from '~/data/graphql/getProducts'
import { ShopifyApiService } from './shopifyApi.server'

// Import delay utility (can be mocked in tests)
const delayUtil = {
  delay: async (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

/**
 * Job Processor Service for Shopify Bulk Product Editor
 *
 * This service handles the actual processing of bulk update jobs,
 * including status updates, error handling, and Shopify API integration.
 *
 * Features:
 * - Batch processing with configurable batch sizes and delays
 * - Comprehensive error handling and recovery
 * - Real-time job status updates
 * - Detailed logging for debugging and monitoring
 * - Support for both product-level and variant-level modifications
 */

// Logger utility for structured logging
const logger = {
  info: (message: string, meta?: Record<string, any>) => {
    console.log(`[JobProcessor] ${message}`, meta ? JSON.stringify(meta) : '')
  },
  error: (message: string, error?: Error, meta?: Record<string, any>) => {
    console.error(`[JobProcessor] ${message}`, error?.message || '', meta ? JSON.stringify(meta) : '')
  },
  warn: (message: string, meta?: Record<string, any>) => {
    console.warn(`[JobProcessor] ${message}`, meta ? JSON.stringify(meta) : '')
  },
}

export interface JobProcessResult {
  success: boolean
  jobId: string
  processedProducts: number
  successfulUpdates: number
  failedUpdates: number
  retryableFailures: number
  permanentFailures: number
  cancelled?: boolean
}

export interface ErrorClassification {
  isRetryable: boolean
  category: 'NETWORK' | 'RATE_LIMIT' | 'VALIDATION' | 'PERMISSION' | 'SYSTEM' | 'UNKNOWN'
  retryAfter?: number // seconds to wait before retry
}

export interface ProductVariantProcessResult {
  success: boolean
  errorMessage: string | null
  errorClassification?: ErrorClassification
  retryAttempt?: number
}

export interface JobProgressUpdate {
  processedProducts?: number
  successfulUpdates?: number
  failedUpdates?: number
  totalProducts?: number
}

/**
 * Classify errors to determine if they are retryable and what category they belong to
 */
export function classifyError(error: Error): ErrorClassification {
  const message = error.message.toLowerCase()

  // Rate limiting errors
  if (message.includes('rate limit') || message.includes('throttle') || message.includes('429')) {
    return {
      isRetryable: true,
      category: 'RATE_LIMIT',
      retryAfter: 60 // Default 60 seconds for rate limits
    }
  }

  // Network errors (temporary)
  if (message.includes('network') || message.includes('timeout') || message.includes('connection') ||
      message.includes('econnreset') || message.includes('enotfound') || message.includes('502') ||
      message.includes('503') || message.includes('504')) {
    return {
      isRetryable: true,
      category: 'NETWORK',
      retryAfter: 30
    }
  }

  // Validation errors (permanent)
  if (message.includes('validation') || message.includes('invalid') || message.includes('required') ||
      message.includes('400') || message.includes('422')) {
    return {
      isRetryable: false,
      category: 'VALIDATION'
    }
  }

  // Permission errors (permanent)
  if (message.includes('unauthorized') || message.includes('forbidden') || message.includes('401') ||
      message.includes('403') || message.includes('permission')) {
    return {
      isRetryable: false,
      category: 'PERMISSION'
    }
  }

  // System errors (potentially retryable)
  if (message.includes('internal server error') || message.includes('500')) {
    return {
      isRetryable: true,
      category: 'SYSTEM',
      retryAfter: 120
    }
  }

  // Default to unknown, not retryable to be safe
  return {
    isRetryable: false,
    category: 'UNKNOWN'
  }
}

/**
 * Retry a function with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  errorClassifier?: (error: Error) => ErrorClassification
): Promise<T> {
  let lastError: Error

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error

      // Classify error if classifier provided
      if (errorClassifier) {
        const classification = errorClassifier(lastError)

        // Don't retry if error is not retryable
        if (!classification.isRetryable) {
          throw lastError
        }

        // Use custom retry delay if provided
        if (classification.retryAfter && attempt < maxRetries) {
          logger.warn(`Retryable error detected, waiting ${classification.retryAfter}s before retry`, {
            attempt,
            maxRetries,
            errorCategory: classification.category,
            errorMessage: lastError.message
          })
          await delayUtil.delay(classification.retryAfter * 1000)
          continue
        }
      }

      // Don't retry on last attempt
      if (attempt === maxRetries) {
        throw lastError
      }

      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt - 1)
      logger.warn(`Attempt ${attempt} failed, retrying in ${delay}ms`, {
        attempt,
        maxRetries,
        errorMessage: lastError.message
      })
      await delayUtil.delay(delay)
    }
  }

  throw lastError!
}

/**
 * Process a bulk update job
 *
 * This is the main entry point for job processing. It handles the complete
 * lifecycle of a job from start to completion, including error recovery.
 */
export async function processJob(jobData: BulkUpdateJobData): Promise<JobProcessResult> {
  const { jobId, shopDomain, options } = jobData
  const startTime = Date.now()

  logger.info('Starting job processing', {
    jobId,
    shopDomain,
    productCount: jobData.productIds.length,
    batchSize: options.batchSize,
    delayBetweenBatches: options.delayBetweenBatches
  })

  try {
    // Find the job in database
    const job = await db.job.findUnique({
      where: { id: jobId },
      include: {
        modifications: true,
      },
    })

    if (!job) {
      const error = new Error(`Job not found: ${jobId}`)
      logger.error('Job not found in database', error, { jobId })
      throw error
    }

    logger.info('Job found in database', {
      jobId,
      status: job.status,
      modificationsCount: job.modifications?.length || 0
    })

    // Update job status to IN_PROGRESS
    await updateJobStatus(jobId, 'IN_PROGRESS')
    logger.info('Job status updated to IN_PROGRESS', { jobId })

    // Get shop session for Shopify API access
    const sessions = await sessionStorage.findSessionsByShop(shopDomain)
    if (!sessions || !Array.isArray(sessions) || sessions.length === 0) {
      const error = new Error(`No active session found for shop: ${shopDomain}`)
      logger.error('No active session found', error, { shopDomain })
      throw error
    }

    const session = sessions[0]
    logger.info('Shop session found', { shopDomain, sessionId: session?.id })

    // Check if JobProductVariant entries exist, if not create them
    let productVariants = await db.jobProductVariant.findMany({
      where: { jobId },
    })

    if (productVariants.length === 0) {
      logger.info('No JobProductVariant entries found, creating them from job data', { jobId })

      // Create JobProductVariant entries based on job modifications and product selection
      const productVariantEntries = await createJobProductVariantEntries(job, session, logger)

      if (productVariantEntries.length > 0) {
        await db.jobProductVariant.createMany({
          data: productVariantEntries
        })
        logger.info('Created JobProductVariant entries', { jobId, count: productVariantEntries.length })
      } else {
        logger.warn('No products found matching job criteria', { jobId })
      }

      // Reload the created entries
      productVariants = await db.jobProductVariant.findMany({
        where: { jobId },
      })

      logger.info('Created JobProductVariant entries', {
        jobId,
        createdCount: productVariants.length
      })
    }

    logger.info('Product variants loaded for processing', {
      jobId,
      variantCount: productVariants.length
    })

    // Initialize counters
    let processedProducts = 0
    let successfulUpdates = 0
    let failedUpdates = 0

    // Process products in batches
    const { batchSize, delayBetweenBatches } = options
    const batches = chunkArray(productVariants, batchSize)

    logger.info('Starting batch processing', {
      jobId,
      totalBatches: batches.length,
      batchSize,
      delayBetweenBatches
    })

    for (let i = 0; i < batches.length; i++) {
      // Check if job has been cancelled before processing each batch
      const currentJob = await db.job.findUnique({
        where: { id: jobId },
        select: { status: true }
      })

      if (currentJob?.status === 'CANCELLED') {
        logger.info('Job was cancelled, stopping processing', {
          jobId,
          batchIndex: i,
          processedProducts,
          remainingBatches: batches.length - i
        })

        // Update final progress numbers for cancelled job
        await db.job.update({
          where: { id: jobId },
          data: {
            processedProducts,
            successfulUpdates,
            failedUpdates,
          },
        })
        logger.info('Job was cancelled, updated final progress numbers', {
          jobId,
          processedProducts,
          successfulUpdates,
          failedUpdates
        })

        // Return current progress without updating status to COMPLETED
        return {
          success: false,
          jobId,
          processedProducts,
          successfulUpdates,
          failedUpdates,
          retryableFailures: 0,
          permanentFailures: 0,
          cancelled: true
        }
      }

      const batch = batches[i]
      const batchStartTime = Date.now()

      logger.info(`Processing batch ${i + 1}/${batches.length}`, {
        jobId,
        batchIndex: i,
        batchSize: batch.length
      })

      // Process each product variant in the batch
      for (const productVariant of batch) {

        try {
          // Create real Shopify API service using session data
          if (!session.shop || !session.accessToken) {
            throw new Error('Invalid session: missing shop domain or access token')
          }
          const shopifyApi = new ShopifyApiService(session.shop, session.accessToken)

          const result = await processProductVariant(
            productVariant,
            jobData.modifications,
            shopifyApi
          )

          processedProducts++
          if (result.success) {
            successfulUpdates++
            logger.info('Product variant processed successfully', {
              jobId,
              productVariantId: productVariant.id,
              productId: productVariant.productId
            })
          } else {
            failedUpdates++
            logger.warn('Product variant processing failed', {
              jobId,
              productVariantId: productVariant.id,
              productId: productVariant.productId,
              errorMessage: result.errorMessage
            })
          }
        } catch (error) {
          logger.error('Unexpected error processing product variant', error as Error, {
            jobId,
            productVariantId: productVariant.id,
            productId: productVariant.productId
          })
          failedUpdates++
          processedProducts++
        }
      }

      const batchDuration = Date.now() - batchStartTime
      logger.info(`Batch ${i + 1} completed`, {
        jobId,
        batchIndex: i,
        duration: batchDuration,
        processedInBatch: batch.length
      })

      // Update progress after each batch (except the last one, which will be handled in final update)
      if (i < batches.length - 1) {
        try {
          await updateJobProgress(jobId, {
            processedProducts,
            successfulUpdates,
            failedUpdates,
            totalProducts: productVariants.length
          })
          logger.info('Progress updated after batch', {
            jobId,
            batchIndex: i,
            processedProducts,
            successfulUpdates,
            failedUpdates
          })
        } catch (progressError) {
          logger.warn('Failed to update progress after batch - continuing processing', {
            jobId,
            batchIndex: i,
            error: progressError instanceof Error ? progressError.message : 'Unknown error'
          })
          // Don't throw error - continue processing even if progress update fails
        }
      }

      // Add delay between batches (except for the last batch)
      if (i < batches.length - 1 && delayBetweenBatches > 0) {
        logger.info(`Waiting ${delayBetweenBatches}ms before next batch`, { jobId })
        await delayUtil.delay(delayBetweenBatches)
      }
    }

    const totalDuration = Date.now() - startTime

    // Check final job status before updating to COMPLETED
    const finalJob = await db.job.findUnique({
      where: { id: jobId },
      select: { status: true }
    })

    // Only update to COMPLETED if job hasn't been cancelled
    if (finalJob?.status !== 'CANCELLED') {
      await db.job.update({
        where: { id: jobId },
        data: {
          status: 'COMPLETED',
          completedAt: new Date(),
          processedProducts,
          successfulUpdates,
          failedUpdates,
        },
      })
    } else {
      // Job was cancelled, just update the progress numbers
      await db.job.update({
        where: { id: jobId },
        data: {
          processedProducts,
          successfulUpdates,
          failedUpdates,
        },
      })
      logger.info('Job was cancelled, updated final progress numbers', {
        jobId,
        processedProducts,
        successfulUpdates,
        failedUpdates
      })
      logger.info('Job was cancelled, preserving CANCELLED status', { jobId })
    }

    logger.info('Job processing completed successfully', {
      jobId,
      duration: totalDuration,
      processedProducts,
      successfulUpdates,
      failedUpdates,
      successRate: processedProducts > 0 ? (successfulUpdates / processedProducts * 100).toFixed(2) + '%' : '0%'
    })

    return {
      success: true,
      jobId,
      processedProducts,
      successfulUpdates,
      failedUpdates,
      retryableFailures: 0, // Will be calculated from failed items
      permanentFailures: 0, // Will be calculated from failed items
    }
  } catch (error) {
    const totalDuration = Date.now() - startTime
    logger.error('Job processing failed', error as Error, {
      jobId,
      duration: totalDuration,
      shopDomain
    })

    // Update job status to FAILED with error handling
    try {
      await updateJobStatus(jobId, 'FAILED')
      logger.info('Job status updated to FAILED', { jobId })
    } catch (statusError) {
      logger.error('Failed to update job status to FAILED', statusError as Error, { jobId })
    }

    throw error
  }
}

/**
 * Update job status in database with comprehensive error handling
 */
export async function updateJobStatus(jobId: string, status: string): Promise<void> {
  try {
    const updateData: any = { status }

    // Set timestamps based on status
    if (status === 'IN_PROGRESS') {
      updateData.startedAt = new Date()
    } else if (status === 'COMPLETED' || status === 'FAILED') {
      updateData.completedAt = new Date()
    }

    logger.info('Updating job status', { jobId, status, updateData })

    await db.job.update({
      where: { id: jobId },
      data: updateData,
    })

    logger.info('Job status updated successfully', { jobId, status })
  } catch (error) {
    logger.error('Failed to update job status', error as Error, { jobId, status })
    throw error
  }
}

/**
 * Update job progress in database with comprehensive error handling
 */
export async function updateJobProgress(jobId: string, progress: JobProgressUpdate): Promise<void> {
  try {
    logger.info('Updating job progress', { jobId, progress })

    await db.job.update({
      where: { id: jobId },
      data: progress,
    })

    logger.info('Job progress updated successfully', { jobId, progress })
  } catch (error) {
    logger.error('Failed to update job progress', error as Error, { jobId, progress })
    throw error
  }
}

/**
 * Process a single product variant with comprehensive error handling and logging
 */
export async function processProductVariant(
  productVariant: any,
  modifications: any[],
  shopifyApi: any,
  retryAttempt: number = 0
): Promise<ProductVariantProcessResult> {
  const { id, productId, variantId } = productVariant

  logger.info('Processing product variant', {
    productVariantId: id,
    productId,
    variantId,
    modificationsCount: modifications.length,
    retryAttempt
  })

  try {
    // Separate modifications by type
    const productMods = modifications.filter(mod => mod.fieldType === 'product')
    const variantMods = modifications.filter(mod => mod.fieldType === 'variant')

    logger.info('Modifications separated by type', {
      productVariantId: id,
      productModifications: productMods.length,
      variantModifications: variantMods.length
    })

    let updatedFields: Record<string, any> = {}

    // Process product-level modifications (apply to the product regardless of whether we're processing product or variant)
    if (productMods.length > 0) {
      const productUpdates = productMods.reduce((acc, mod) => {
        acc[mod.fieldName] = mod.fieldValue
        return acc
      }, {} as Record<string, any>)

      logger.info('Applying product-level modifications', {
        productVariantId: id,
        productId,
        updates: productUpdates
      })

      await shopifyApi.updateProduct({ id: productId, ...productUpdates })
      updatedFields = { ...updatedFields, ...productUpdates }
    }

    // Process variant-level modifications (only if we have a specific variant to update)
    if (variantId && variantMods.length > 0) {
      const variantUpdates = variantMods.reduce((acc, mod) => {
        acc[mod.fieldName] = mod.fieldValue
        return acc
      }, {} as Record<string, any>)

      logger.info('Applying variant-level modifications', {
        productVariantId: id,
        variantId,
        updates: variantUpdates
      })

      // Convert to ProductVariantsBulkInput format
      const variantBulkInput = { id: variantId, ...variantUpdates }
      await shopifyApi.updateProductVariant(variantBulkInput)
      updatedFields = { ...updatedFields, ...variantUpdates }
    }

    // Update product variant status in database
    await db.jobProductVariant.update({
      where: { id },
      data: {
        status: 'SUCCESS',
        processedAt: new Date(),
        newValues: JSON.stringify(updatedFields),
      },
    })

    logger.info('Product variant processed successfully', {
      productVariantId: id,
      updatedFields
    })

    return { success: true, errorMessage: null }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    const errorClassification = classifyError(error as Error)

    logger.error('Error processing product variant', error as Error, {
      productVariantId: id,
      productId,
      variantId,
      retryAttempt,
      errorCategory: errorClassification.category,
      isRetryable: errorClassification.isRetryable
    })

    // Update product variant status to FAILED with error handling
    try {
      await db.jobProductVariant.update({
        where: { id: productVariant.id },
        data: {
          status: 'FAILED',
          processedAt: new Date(),
          errorMessage,
        },
      })

      logger.info('Product variant status updated to FAILED', {
        productVariantId: id,
        errorMessage,
        errorCategory: errorClassification.category
      })
    } catch (dbError) {
      logger.error('Failed to update product variant status to FAILED', dbError as Error, {
        productVariantId: id
      })
    }

    return {
      success: false,
      errorMessage,
      errorClassification,
      retryAttempt
    }
  }
}

/**
 * Utility function to chunk array into batches
 *
 * @param array - The array to chunk
 * @param chunkSize - The size of each chunk
 * @returns Array of chunks
 */
function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  if (chunkSize <= 0) {
    throw new Error('Chunk size must be greater than 0')
  }

  const chunks: T[][] = []
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize))
  }

  logger.info('Array chunked for batch processing', {
    totalItems: array.length,
    chunkSize,
    totalChunks: chunks.length
  })

  return chunks
}

/**
 * Handle partial job recovery - retry failed items that are retryable
 */
export async function handlePartialJobRecovery(
  jobId: string,
  maxRetryAttempts: number = 3
): Promise<{ retriedCount: number; recoveredCount: number }> {
  logger.info('Starting partial job recovery', { jobId, maxRetryAttempts })

  // Find failed items that might be retryable
  const failedItems = await db.jobProductVariant.findMany({
    where: {
      jobId,
      status: 'FAILED'
    }
  })

  let retriedCount = 0
  let recoveredCount = 0

  for (const item of failedItems) {
    if (!item.errorMessage) continue

    // Create a mock error to classify
    const mockError = new Error(item.errorMessage)
    const classification = classifyError(mockError)

    // Only retry if error is retryable and we haven't exceeded max attempts
    if (classification.isRetryable) {
      retriedCount++

      try {
        // Reset status to PENDING for retry
        await db.jobProductVariant.update({
          where: { id: item.id },
          data: {
            status: 'PENDING',
            errorMessage: null,
            processedAt: null
          }
        })

        logger.info('Reset failed item for retry', {
          jobId,
          productVariantId: item.id,
          errorCategory: classification.category
        })
      } catch (error) {
        logger.error('Failed to reset item for retry', error as Error, {
          jobId,
          productVariantId: item.id
        })
      }
    }
  }

  logger.info('Partial job recovery completed', {
    jobId,
    retriedCount,
    recoveredCount,
    totalFailedItems: failedItems.length
  })

  return { retriedCount, recoveredCount }
}

/**
 * Create JobProductVariant entries based on job data and Shopify products
 *
 * @param job - The job containing filterCriteria and unselectedIds
 * @param session - Shopify session for API access
 * @param logger - Logger instance
 * @returns Array of JobProductVariant entries to create
 */
async function createJobProductVariantEntries(
  job: any,
  session: any,
  logger: any
): Promise<Array<{
  jobId: string;
  productId: string;
  variantId: string | null;
  status: string;
}>> {
  try {
    // Parse job data
    const filterCriteria = job.filterCriteria ? JSON.parse(job.filterCriteria) : {}
    const unselectedIds = JSON.parse(job.unselectedIds || '[]')

    logger.info('Creating JobProductVariant entries', {
      jobId: job.id,
      filterCriteria,
      unselectedIdsCount: unselectedIds.length
    })

    // Get job modifications to determine scope
    const modifications = await db.jobModification.findMany({
      where: { jobId: job.id }
    })

    const hasProductModifications = modifications.some(mod => mod.fieldType === 'product')
    const hasVariantModifications = modifications.some(mod => mod.fieldType === 'variant')

    logger.info('Modification scope analysis', {
      jobId: job.id,
      hasProductModifications,
      hasVariantModifications,
      totalModifications: modifications.length
    })

    // Create admin object for GraphQL calls
    const admin = {
      graphql: async (query: string, options?: any) => {
        // Use session to make GraphQL calls
        const response = await fetch(`https://${session.shop}/admin/api/2023-10/graphql.json`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': session.accessToken,
          },
          body: JSON.stringify({
            query,
            variables: options?.variables || {}
          })
        })
        return response
      }
    }

    // Fetch products from Shopify using existing logic
    let allProducts: any[] = []
    let cursor: string | null = null
    let hasNextPage = true

    // Fetch all products matching the criteria
    while (hasNextPage) {
      const productsData = await getProductsFromShopify({
        admin,
        searchQuery: filterCriteria.searchQuery || '',
        optionName: filterCriteria.optionName,
        optionValue: filterCriteria.optionValue,
        cursor,
        pageSize: 50 // Larger page size for job processing
      })

      allProducts.push(...productsData.nodes)
      hasNextPage = productsData.pageInfo.hasNextPage
      cursor = productsData.pageInfo.endCursor
    }

    logger.info('Fetched products from Shopify', {
      jobId: job.id,
      totalProducts: allProducts.length
    })

    // Filter out unselected products and variants
    const productVariantEntries: Array<{
      jobId: string;
      productId: string;
      variantId: string | null;
      status: string;
    }> = []

    for (const product of allProducts) {
      const productId = product.id

      // Skip if product is unselected
      if (unselectedIds.includes(productId)) {
        continue
      }

      // Create product-level entries if needed
      if (hasProductModifications) {
        productVariantEntries.push({
          jobId: job.id,
          productId,
          variantId: null,
          status: 'PENDING'
        })
      }

      // Create variant-level entries if needed
      if (hasVariantModifications && product.variants?.nodes) {
        for (const variant of product.variants.nodes) {
          const variantId = variant.id

          // Skip if variant is unselected
          if (unselectedIds.includes(variantId)) {
            continue
          }

          productVariantEntries.push({
            jobId: job.id,
            productId,
            variantId,
            status: 'PENDING'
          })
        }
      }
    }

    logger.info('Created JobProductVariant entries', {
      jobId: job.id,
      totalEntries: productVariantEntries.length,
      productEntries: productVariantEntries.filter(e => e.variantId === null).length,
      variantEntries: productVariantEntries.filter(e => e.variantId !== null).length
    })

    return productVariantEntries

  } catch (error) {
    logger.error('Error creating JobProductVariant entries', {
      jobId: job.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    throw error
  }
}

/**
 * Handle dead letter queue - jobs that have failed multiple times
 */
export async function handleDeadLetterJob(
  jobId: string,
  finalError: Error,
  attemptCount: number
): Promise<void> {
  logger.error('Job moved to dead letter queue', finalError, {
    jobId,
    attemptCount,
    finalErrorMessage: finalError.message
  })

  try {
    // Update job with final failure status and detailed error info
    await db.job.update({
      where: { id: jobId },
      data: {
        status: 'FAILED',
        completedAt: new Date(),
        // Store error details in description for debugging
        description: `DEAD LETTER: Failed after ${attemptCount} attempts. Final error: ${finalError.message}`
      }
    })

    // Mark all remaining PENDING items as FAILED
    await db.jobProductVariant.updateMany({
      where: {
        jobId,
        status: 'PENDING'
      },
      data: {
        status: 'FAILED',
        errorMessage: `Job failed after ${attemptCount} attempts: ${finalError.message}`,
        processedAt: new Date()
      }
    })

    logger.info('Dead letter job processing completed', { jobId })
  } catch (error) {
    logger.error('Failed to handle dead letter job', error as Error, { jobId })
  }
}

/**
 * Check if a job should be moved to dead letter queue
 */
export function shouldMoveToDeadLetter(
  error: Error,
  attemptCount: number,
  maxAttempts: number = 3
): boolean {
  const classification = classifyError(error)

  // Move to dead letter if:
  // 1. Error is not retryable, OR
  // 2. We've exceeded max attempts
  return !classification.isRetryable || attemptCount >= maxAttempts
}

/**
 * Get job recovery statistics
 */
export async function getJobRecoveryStats(jobId: string): Promise<{
  totalItems: number
  pendingItems: number
  successfulItems: number
  failedItems: number
  retryableFailures: number
  permanentFailures: number
}> {
  const items = await db.jobProductVariant.findMany({
    where: { jobId }
  })

  const stats = {
    totalItems: items.length,
    pendingItems: 0,
    successfulItems: 0,
    failedItems: 0,
    retryableFailures: 0,
    permanentFailures: 0
  }

  for (const item of items) {
    switch (item.status) {
      case 'PENDING':
        stats.pendingItems++
        break
      case 'SUCCESS':
        stats.successfulItems++
        break
      case 'FAILED':
        stats.failedItems++
        // Classify the failure
        if (item.errorMessage) {
          const mockError = new Error(item.errorMessage)
          const classification = classifyError(mockError)
          if (classification.isRetryable) {
            stats.retryableFailures++
          } else {
            stats.permanentFailures++
          }
        } else {
          stats.permanentFailures++
        }
        break
    }
  }

  return stats
}

/**
 * Utility function to add delay with logging
 *
 * @param ms - Milliseconds to delay
 */
export async function delay(ms: number): Promise<void> {
  if (ms <= 0) return

  logger.info(`Delaying execution for ${ms}ms`)
  return new Promise(resolve => setTimeout(resolve, ms))
}
