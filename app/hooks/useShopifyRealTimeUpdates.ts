import { useEffect, useRef, useState, useCallback } from 'react'
import { useRevalidator } from '@remix-run/react'
import type { JobDetail } from '~/types/models'
import type { JobStatusResponse } from '~/routes/api.jobs.$jobId.status'

export interface UseShopifyRealTimeUpdatesOptions {
  /**
   * Job or jobs to monitor for real-time updates
   */
  jobs?: JobDetail | JobDetail[]
  
  /**
   * Whether to enable real-time updates
   * @default true
   */
  enabled?: boolean
  
  /**
   * Custom polling intervals in milliseconds
   */
  intervals?: {
    /** Interval for jobs in progress */
    active?: number
    /** Interval for scheduled jobs */
    scheduled?: number
    /** Interval when no active jobs */
    idle?: number
  }
  
  /**
   * Callback when job status changes
   */
  onStatusChange?: (job: JobDetail, previousStatus: string) => void
  
  /**
   * Callback when job progress updates
   */
  onProgressUpdate?: (job: JobDetail, progress: number) => void
  
  /**
   * Callback when job completes
   */
  onJobComplete?: (job: JobDetail) => void
  
  /**
   * Callback when job fails
   */
  onJobFailed?: (job: JobDetail, error?: string) => void
}

/**
 * Shopify-compatible real-time updates hook
 * 
 * Uses polling with session token authentication instead of SSE
 * Optimized for Shopify's iframe environment and App Bridge
 */
export function useShopifyRealTimeUpdates(options: UseShopifyRealTimeUpdatesOptions = {}) {
  const {
    jobs,
    enabled = true,
    intervals = {},
    onStatusChange,
    onProgressUpdate,
    onJobComplete,
    onJobFailed,
  } = options



  // Default intervals optimized for Shopify apps
  const defaultIntervals = {
    active: 2000,    // 2 seconds for active jobs
    scheduled: 5000, // 5 seconds for scheduled jobs
    idle: 30000,     // 30 seconds when idle
  }

  const effectiveIntervals = { ...defaultIntervals, ...intervals }

  const revalidator = useRevalidator()
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const previousStatusRef = useRef<Map<string, string>>(new Map())
  const [isPolling, setIsPolling] = useState(false)
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date())

  // Convert jobs to array for consistent handling
  const jobArray = jobs ? (Array.isArray(jobs) ? jobs : [jobs]) : []

  // Determine current polling state
  const hasActiveJobs = jobArray.some(job => 
    job.status === 'IN_PROGRESS'
  )
  const hasScheduledJobs = jobArray.some(job => 
    job.status === 'SCHEDULED'
  )
  const hasAnyRelevantJobs = hasActiveJobs || hasScheduledJobs

  // Calculate optimal polling interval
  const getPollingInterval = useCallback(() => {
    if (hasActiveJobs) return effectiveIntervals.active
    if (hasScheduledJobs) return effectiveIntervals.scheduled
    return effectiveIntervals.idle
  }, [hasActiveJobs, hasScheduledJobs, effectiveIntervals])

  // Poll individual job status using App Bridge authenticated fetch
  const pollJobStatus = useCallback(async (jobId: string): Promise<JobStatusResponse | null> => {
    try {
      // In App Bridge React 4.x.x, fetch is automatically authenticated
      const response = await fetch(`/api/jobs/${jobId}/status`)

      if (!response.ok) {
        // Handle authentication errors specifically
        if (response.status === 401) {
          console.warn(`[Real-time Updates] Authentication failed for job ${jobId} - session may have expired`)
          // Stop polling on auth failure to prevent iframe issues and notification cascade
          return null
        }
        console.error(`[Real-time Updates] Failed to fetch job ${jobId} status:`, response.status)
        return null
      }

      const data: JobStatusResponse = await response.json()
      return data
    } catch (error) {
      console.error(`[Real-time Updates] Error polling job ${jobId}:`, error)
      // On any error, return null to prevent further issues
      return null
    }
  }, [])

  // Poll all active jobs and trigger revalidation if any changes detected
  const pollAllJobs = useCallback(async () => {
    if (!hasAnyRelevantJobs) return

    let hasChanges = false
    let shouldStopPolling = false

    for (const job of jobArray) {
      if (job.status === 'IN_PROGRESS' || job.status === 'SCHEDULED') {
        const updatedStatus = await pollJobStatus(job.id)

        if (updatedStatus) {
          const previousStatus = previousStatusRef.current.get(job.id)

          // Check if status or progress changed
          if (previousStatus !== updatedStatus.status ||
              job.processedProducts !== updatedStatus.processedProducts) {
            hasChanges = true

            // Store new status
            previousStatusRef.current.set(job.id, updatedStatus.status)

            // Trigger callbacks for status changes
            if (previousStatus && previousStatus !== updatedStatus.status) {
              onStatusChange?.(job, previousStatus)

              if (updatedStatus.status === 'COMPLETED') {
                onJobComplete?.(job)
                shouldStopPolling = true
              } else if (updatedStatus.status === 'FAILED') {
                onJobFailed?.(job, updatedStatus.errorMessage)
                shouldStopPolling = true
              }
            }

            // Trigger progress callback
            if (updatedStatus.status === 'IN_PROGRESS' && updatedStatus.totalProducts > 0) {
              const progress = (updatedStatus.processedProducts / updatedStatus.totalProducts) * 100
              onProgressUpdate?.(job, progress)
            }
          }
        } else {
          // If polling failed (e.g., auth error), stop polling to prevent iframe issues
          console.warn(`[Real-time Updates] Polling failed for job ${job.id}, stopping polling`)
          shouldStopPolling = true
          // Break out of loop immediately to prevent cascade
          break
        }
      }
    }

    // Stop polling immediately if any job completed or polling failed
    if (shouldStopPolling && intervalRef.current) {
      console.log('[Real-time Updates] Stopping polling due to job completion or error')
      clearInterval(intervalRef.current)
      intervalRef.current = null
      setIsPolling(false)
    }

    // Only revalidate if we detected changes
    if (hasChanges && revalidator.state === 'idle') {
      revalidator.revalidate()
      setLastUpdateTime(new Date())
    }
  }, [hasAnyRelevantJobs, jobArray, pollJobStatus, revalidator, onStatusChange, onJobComplete, onJobFailed, onProgressUpdate, intervalRef, setIsPolling])

  // Handle status changes and trigger callbacks
  const handleStatusChanges = useCallback(() => {
    jobArray.forEach(job => {
      const previousStatus = previousStatusRef.current.get(job.id)
      
      if (previousStatus && previousStatus !== job.status) {
        // Status changed - trigger callback
        onStatusChange?.(job, previousStatus)
        
        // Trigger specific callbacks
        if (job.status === 'COMPLETED') {
          onJobComplete?.(job)
        } else if (job.status === 'FAILED') {
          onJobFailed?.(job, job.errorMessage)
        }
      }
      
      // Update progress if job is active
      if (job.status === 'IN_PROGRESS' && job.totalProducts > 0) {
        const progress = (job.processedProducts / job.totalProducts) * 100
        onProgressUpdate?.(job, progress)
      }
      
      // Store current status for next comparison
      previousStatusRef.current.set(job.id, job.status)
    })
  }, [jobArray, onStatusChange, onProgressUpdate, onJobComplete, onJobFailed])

  // Start/stop polling based on conditions
  useEffect(() => {
    if (!enabled) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
        setIsPolling(false)
      }
      return
    }

    const shouldPoll = hasAnyRelevantJobs
    const currentInterval = getPollingInterval()

    if (shouldPoll) {
      // Clear existing interval if interval changed
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }

      // Start new polling interval using App Bridge authenticated fetch
      intervalRef.current = setInterval(() => {
        pollAllJobs()
      }, currentInterval)
      
      setIsPolling(true)
    } else {
      // Stop polling when no relevant jobs
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
        setIsPolling(false)
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
        setIsPolling(false)
      }
    }
  }, [enabled, hasAnyRelevantJobs, getPollingInterval, pollAllJobs])

  // Handle status changes when jobs update
  useEffect(() => {
    handleStatusChanges()
  }, [handleStatusChanges])

  // Manual refresh function
  const refresh = useCallback(async () => {
    await pollAllJobs()
  }, [pollAllJobs])

  // Force immediate update
  const forceUpdate = useCallback(async () => {
    await pollAllJobs()
  }, [pollAllJobs])

  return {
    /** Whether polling is currently active */
    isPolling,
    
    /** Whether any jobs are currently active */
    hasActiveJobs,
    
    /** Whether any jobs are scheduled */
    hasScheduledJobs,
    
    /** Whether revalidation is in progress */
    isUpdating: revalidator.state !== 'idle',
    
    /** Current polling interval in milliseconds */
    currentInterval: getPollingInterval(),

    /** Last update timestamp */
    lastUpdateTime,

    /** Manually trigger a refresh */
    refresh,

    /** Force immediate update (bypasses idle check) */
    forceUpdate,
    
    /** Stop polling temporarily */
    pause: () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
        setIsPolling(false)
      }
    },
    
    /** Resume polling */
    resume: () => {
      if (enabled && hasAnyRelevantJobs && !intervalRef.current) {
        intervalRef.current = setInterval(() => {
          pollAllJobs()
        }, getPollingInterval())
        setIsPolling(true)
      }
    },
  }
}

/**
 * Hook specifically for single job real-time updates
 */
export function useJobRealTimeUpdates(
  job: JobDetail, 
  options: Omit<UseShopifyRealTimeUpdatesOptions, 'jobs'> = {}
) {
  return useShopifyRealTimeUpdates({
    ...options,
    jobs: job,
  })
}

/**
 * Hook specifically for multiple jobs real-time updates
 */
export function useJobsRealTimeUpdates(
  jobs: JobDetail[], 
  options: Omit<UseShopifyRealTimeUpdatesOptions, 'jobs'> = {}
) {
  return useShopifyRealTimeUpdates({
    ...options,
    jobs,
  })
}
