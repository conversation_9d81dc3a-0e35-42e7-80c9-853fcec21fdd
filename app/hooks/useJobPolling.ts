import { useEffect, useRef, useMemo } from 'react'
import { useRevalidator } from '@remix-run/react'
import type { JobDetail } from '~/types/models'

export interface UseJobPollingOptions {
  /**
   * Polling interval in milliseconds
   * @default 5000 (5 seconds)
   */
  interval?: number
  
  /**
   * Whether to enable polling
   * @default true
   */
  enabled?: boolean
  
  /**
   * Job or jobs to monitor for status changes
   */
  jobs?: JobDetail | JobDetail[]
  
  /**
   * Callback when job status changes
   */
  onStatusChange?: (job: JobDetail, previousStatus: string) => void
}

/**
 * Hook for polling job status updates
 * Automatically revalidates data when jobs are in progress
 */
export function useJobPolling(options: UseJobPollingOptions = {}) {
  const {
    interval,
    enabled = true,
    jobs,
    onStatusChange,
  } = options

  // Use dynamic interval based on job status if not explicitly provided
  const effectiveInterval = useMemo(() => {
    return interval ?? (jobs ? getPollingInterval(jobs) : 5000)
  }, [interval, jobs])
  
  const revalidator = useRevalidator()
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const previousStatusRef = useRef<Map<string, string>>(new Map())
  
  // Determine if any jobs are active (in progress or scheduled)
  const hasActiveJobs = useMemo(() => {
    if (!jobs) return false

    const jobArray = Array.isArray(jobs) ? jobs : [jobs]
    return jobArray.some(job => job.status === 'IN_PROGRESS' || job.status === 'SCHEDULED')
  }, [jobs])
  
  // Track status changes
  useEffect(() => {
    if (!jobs || !onStatusChange) return
    
    const jobArray = Array.isArray(jobs) ? jobs : [jobs]
    
    jobArray.forEach(job => {
      const previousStatus = previousStatusRef.current.get(job.id)
      if (previousStatus && previousStatus !== job.status) {
        onStatusChange(job, previousStatus)
      }
      previousStatusRef.current.set(job.id, job.status)
    })
  }, [jobs, onStatusChange])
  
  // Start/stop polling based on conditions
  useEffect(() => {
    if (!enabled) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
      return
    }
    
    const shouldPoll = hasActiveJobs
    
    if (shouldPoll && !intervalRef.current) {
      // Start polling
      intervalRef.current = setInterval(() => {
        if (revalidator.state === 'idle') {
          revalidator.revalidate()
        }
      }, effectiveInterval)
    } else if (!shouldPoll && intervalRef.current) {
      // Stop polling
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [enabled, jobs, effectiveInterval, revalidator, hasActiveJobs])

  return {
    isPolling: !!intervalRef.current,
    hasActiveJobs,
    revalidate: () => revalidator.revalidate(),
    isRevalidating: revalidator.state !== 'idle',
  }
}

/**
 * Hook specifically for single job polling (job detail page)
 */
export function useJobDetailPolling(job: JobDetail, options: Omit<UseJobPollingOptions, 'jobs'> = {}) {
  return useJobPolling({
    ...options,
    jobs: job,
  })
}

/**
 * Hook specifically for multiple jobs polling (jobs list page)
 */
export function useJobsListPolling(jobs: JobDetail[], options: Omit<UseJobPollingOptions, 'jobs'> = {}) {
  return useJobPolling({
    ...options,
    jobs,
  })
}

/**
 * Utility function to determine if a job status change is significant
 */
export function isSignificantStatusChange(from: string, to: string): boolean {
  const significantTransitions = [
    ['SCHEDULED', 'IN_PROGRESS'],
    ['IN_PROGRESS', 'COMPLETED'],
    ['IN_PROGRESS', 'FAILED'],
    ['IN_PROGRESS', 'CANCELLED'],
    ['SCHEDULED', 'CANCELLED'],
  ]
  
  return significantTransitions.some(([fromStatus, toStatus]) => 
    from === fromStatus && to === toStatus
  )
}

/**
 * Get appropriate polling interval based on job status
 */
export function getPollingInterval(jobs: JobDetail | JobDetail[]): number {
  const jobArray = Array.isArray(jobs) ? jobs : [jobs]
  const hasInProgress = jobArray.some(job => job.status === 'IN_PROGRESS')
  
  // More frequent polling for active jobs
  return hasInProgress ? 3000 : 10000
}
