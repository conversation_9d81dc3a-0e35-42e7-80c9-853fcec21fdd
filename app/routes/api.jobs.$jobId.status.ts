import { LoaderFunctionArgs, json } from "@remix-run/node";
import { authenticate } from "~/shopify.server";
import { getJobById } from "~/utils/jobManager.server";

/**
 * Optimized job status endpoint for real-time polling
 * 
 * This endpoint provides efficient job status updates for Shopify iframe apps
 * using session token authentication and optimized response format.
 * 
 * SHOPIFY COMPATIBILITY:
 * - Uses session token authentication (required for iframe)
 * - Compatible with App Bridge authenticatedFetch
 * - Optimized for frequent polling requests
 * - Minimal response payload for performance
 */

export async function loader({ request, params }: LoaderFunctionArgs) {
  try {
    // Authenticate using session tokens (Shopify iframe requirement)
    const { session } = await authenticate.admin(request);
    const shopDomain = session.shop;

    const { jobId } = params;
    if (!jobId) {
      return json(
        { error: 'Job ID is required' },
        { status: 400 }
      );
    }

    // Get job with minimal data for polling efficiency
    const job = await getJobById(jobId, shopDomain);
    if (!job) {
      return json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }

    // Return optimized response for polling
    const response = {
      id: job.id,
      status: job.status,
      processedProducts: job.processedProducts,
      totalProducts: job.totalProducts,
      successfulUpdates: job.successfulUpdates,
      failedUpdates: job.failedUpdates,
      updatedAt: job.updatedAt,
      // Include completion/error details if relevant
      ...(job.status === 'COMPLETED' && {
        completedAt: job.completedAt,
      }),
      ...(job.status === 'FAILED' && {
        failedAt: job.failedAt,
        errorMessage: job.errorMessage,
      }),
    };

    // Add cache headers for polling optimization
    const headers = new Headers({
      'Content-Type': 'application/json',
      // Short cache for active jobs, longer for completed
      'Cache-Control': job.status === 'IN_PROGRESS' || job.status === 'SCHEDULED' 
        ? 'no-cache, must-revalidate' 
        : 'public, max-age=60',
      // Add ETag for conditional requests
      'ETag': `"${job.id}-${job.updatedAt}"`,
    });

    // Handle conditional requests for efficiency
    const ifNoneMatch = request.headers.get('If-None-Match');
    if (ifNoneMatch === `"${job.id}-${job.updatedAt}"`) {
      return new Response(null, { status: 304, headers });
    }

    return json(response, { headers });

  } catch (error) {
    console.error('[Job Status API] Error:', error);

    // Handle authentication errors specifically
    if (error instanceof Error && error.message.includes('Authentication')) {
      return json(
        { 
          error: 'Authentication required',
          code: 'AUTH_REQUIRED',
          timestamp: new Date().toISOString()
        },
        { status: 401 }
      );
    }

    // Handle other errors
    return json(
      { 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// Export types for client-side usage
export interface JobStatusResponse {
  id: string;
  status: string;
  processedProducts: number;
  totalProducts: number;
  successfulUpdates: number;
  failedUpdates: number;
  updatedAt: string;
  completedAt?: string;
  failedAt?: string;
  errorMessage?: string;
}

export interface JobStatusError {
  error: string;
  code: string;
  timestamp: string;
}
