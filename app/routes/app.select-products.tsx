import {
  Box,
  Card,
  Layout,
  Page,
  Text,
  BlockStack,
  InlineStack,
  Button,
  Banner,
  Tooltip,
  Icon
} from "@shopify/polaris";
import { Modal, TitleBar, useAppBridge } from "@shopify/app-bridge-react";
import { ArrowLeftIcon, InfoIcon } from "@shopify/polaris-icons";
import { useFetcher, useNavigate, useLoaderData } from "@remix-run/react";
import { useState } from "react";
import EnhancedProductsFilter from "~/components/EnhancedProductsFilter";
import { FilterValue } from "~/utils/filterFields";
import { buildCompleteSearchQuery } from "~/utils/filterConverter";
import ProductSelectionTable from "~/components/ProductSelectionTable";
import { authenticate } from "~/shopify.server";
import { ActionFunctionArgs, LoaderFunctionArgs, redirect } from "@remix-run/node";
import { PageInfo, ProductType } from "~/types/models";
import { getProductsFromShopify, getProductsCountFromShopify } from "~/data/graphql/getProducts";
import ProductVariantTable from "~/components/ProductVariantsTable";
import { extractIdFromGid } from "~/utils/constants";
import { storeUnselectedIds } from "~/utils/tempSelection.server";


export type LoaderData = {
  products: ProductType[];
  pageInfo: PageInfo;
  product?: ProductType;
  error?: string;
  searchQuery?: string;
  storeHandle?: string;
  productsCount?: {
    count: number;
    precision: 'EXACT' | 'AT_LEAST';
  };

};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { admin } = await authenticate.admin(request);

  // Handle both JSON and form data
  let body: any;
  const contentType = request.headers.get('content-type');

  if (contentType?.includes('application/json')) {
    body = await request.json();
  } else {
    // Handle form data
    const formData = await request.formData();
    body = {};

    for (const [key, value] of formData.entries()) {
      body[key] = value;
    }

    // Parse JSON strings if they exist
    if (body.unselectedIds && typeof body.unselectedIds === 'string') {
      try {
        body.unselectedIds = JSON.parse(body.unselectedIds);
      } catch (e) {
        // If it's not JSON, treat as single value
        body.unselectedIds = [body.unselectedIds];
      }
    }
  }



  const { cursor, searchQuery, optionName, optionValue, type, productId, unselectedIds } = body;

  // Handle search requests
  if (type === "search") {
    try {
      // console.log('🔍 Search request received:', { searchQuery });

      // Fetch products and products count in parallel
      const [products, productsCount] = await Promise.all([
        getProductsFromShopify({
          admin,
          searchQuery,
          optionName: '',
          optionValue: '',
          cursor: '',
        }),
        getProductsCountFromShopify({
          admin,
          searchQuery,
          optionName: '',
          optionValue: '',
        })
      ]);

      return {
        products: products.nodes,
        pageInfo: products.pageInfo,
        searchQuery,
        productsCount
      };
    } catch (error) {
      console.error('Error searching products:', error);
      throw error;
    }
  }

  // Handle storing unselected IDs and redirecting to modifications page
  if (type === "store-unselected-ids" && unselectedIds) {
    try {
      const sessionKey = await storeUnselectedIds(request, unselectedIds);
      return redirect(`/app/define-modifications?sessionKey=${sessionKey}`);
    } catch (error) {
      console.error('Error storing unselected IDs:', error);
      throw error;
    }
  }

  if (type == "get-variants" && productId) {
    try {
      const response = await admin.graphql(`
        query getProductVariants($id: ID!) {
          product(id: $id) {
            id
            title
            variants(first: 100) {
              nodes {
                id
                title
                sku
                price
                inventoryQuantity
                selectedOptions {
                  name
                  value
                }
              }
              pageInfo {
                hasNextPage
                hasPreviousPage
                startCursor
                endCursor
              }
            }
          }
        }
      `, {
        variables: {
          id: productId
        }
      });

      const responseData = await response.json();
      const { data, errors } = responseData as { data: any; errors?: any[] };

      if (errors) {
        console.error('GraphQL errors:', errors);
        throw new Error('Failed to fetch product variants');
      }

      return { product: data.product };
    } catch (error) {
      console.error('Error fetching product variants:', error);
      throw error;
    }
  }

  let query = searchQuery || '';
  if (optionName && optionValue) {
    query = `${query} option:${optionName}:${optionValue}`.trim();
  }

  try {
    const products = await getProductsFromShopify({
      admin,
      searchQuery,
      optionName,
      optionValue,
      cursor,
    });


    return {
      products: products.nodes,
      pageInfo: products.pageInfo,
    };
  } catch (error) {
    console.error('Error fetching products:', error);
    throw error;
  }
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const url = new URL(request.url);
  const searchQuery = url.searchParams.get("q") ?? '';
  const optionName = url.searchParams.get("optionName") ?? '';
  const optionValue = url.searchParams.get("optionValue") ?? '';
  const cursor = url.searchParams.get("after") ?? '';

  try {
    // Fetch products and products count in parallel
    const [products, productsCount] = await Promise.all([
      getProductsFromShopify({
        admin,
        searchQuery,
        optionName,
        optionValue,
        cursor,
      }),
      getProductsCountFromShopify({
        admin,
        searchQuery,
        optionName,
        optionValue,
      })
    ]);



    // Extract store handle from shop domain using Shopify best practice
    // e.g., "cool-shop" from "cool-shop.myshopify.com"
    const storeHandle = session?.shop ? session.shop.replace('.myshopify.com', '') : 'unknown-shop';

    return {
      products: products.nodes || [],
      pageInfo: products.pageInfo || { hasNextPage: false, hasPreviousPage: false, startCursor: null, endCursor: null },
      storeHandle,
      searchQuery,
      productsCount,
      error: undefined
    };
  } catch (error) {
    console.error('Failed to load products in loader:', error);

    // Return empty state with error message for graceful degradation
    const storeHandle = session?.shop ? session.shop.replace('.myshopify.com', '') : 'unknown-shop';
    return {
      products: [],
      pageInfo: {
        hasNextPage: false,
        hasPreviousPage: false,
        startCursor: null,
        endCursor: null,
      },
      storeHandle,
      searchQuery,
      productsCount: { count: 0, precision: 'EXACT' as const },
      error: error instanceof Error ? error.message.slice(0, 500) : 'Failed to load products. Please try again.',
    };
  }
}

export default function SelectProducts() {
  const navigate = useNavigate();
  const loaderData = useLoaderData<typeof loader>();
  const fetcher = useFetcher<LoaderData>();
  const variantFetcher = useFetcher<LoaderData>();

  // Use fetcher data if available, otherwise use loader data
  const currentProducts = fetcher.data?.products || loaderData.products || [];
  const currentPageInfo = fetcher.data?.pageInfo || loaderData.pageInfo || { hasNextPage: false, hasPreviousPage: false, startCursor: null, endCursor: null };
  const currentProductsCount = fetcher.data?.productsCount || loaderData.productsCount;


  // Initialize search query from loader data
  const [searchQuery, setSearchQuery] = useState(() => {
    return loaderData.searchQuery || '';
  });

  // Handle search via fetcher to avoid navigation issues in iframe
  const handleSearch = (query: string) => {
    setSearchQuery(query);

    // Build complete search query including advanced filters
    const completeQuery = buildCompleteSearchQuery(
      query,
      activeFilters,
      advancedFilters,
      filterLogic
    );

    fetcher.submit(
      { searchQuery: completeQuery, type: 'search' },
      { method: 'post', encType: 'application/json' }
    );
  };

  // Filter state management
  const [activeFilters, setActiveFilters] = useState<Array<{
    id: string;
    label: string;
    value: string;
    type: 'status' | 'vendor' | 'product_type' | 'tag' | 'inventory' | 'price' | 'custom';
    displayValue?: string;
  }>>([]);

  // Advanced filtering state
  const [advancedFilters, setAdvancedFilters] = useState<FilterValue[]>([]);
  const [filterMode, setFilterMode] = useState<'simple' | 'advanced'>('advanced');
  const [filterLogic, setFilterLogic] = useState<'AND' | 'OR'>('AND');

  // Handle advanced filters change (no auto-search)
  const handleAdvancedFiltersChange = (newFilters: FilterValue[]) => {
    setAdvancedFilters(newFilters);
    // Don't auto-search - wait for explicit Search button press
  };

  const [unselectedIds, setUnselectedIds] = useState<Set<string>>(new Set());

  const goToNextStep = async () => {
    // Submit unselected IDs to the action and redirect
    fetcher.submit(
      {
        type: "store-unselected-ids",
        unselectedIds: Array.from(unselectedIds)
      },
      { method: "post", encType: "application/json" }
    );
  }

  const product = variantFetcher.data?.product;
  const products = currentProducts;
  const pageInfo = currentPageInfo;
  const error = fetcher.data?.error ?? (loaderData as any).error;
  if (product) {
    const existingProduct: ProductType = products.find((p: ProductType) => p.id === product.id);
    if (existingProduct) {
      existingProduct.variants.nodes = product.variants.nodes;
      if (unselectedIds.has(product.id)) {
        product.variants.nodes.forEach((v) => unselectedIds.add(v.id));
      }
    }
  }

  const shopify = useAppBridge();

  const onSelectionChange = (checked: boolean, productId: string, variantId?: string) => {
    const newUnselectedIds = new Set(unselectedIds);
    const product: ProductType = products.find((p: ProductType) => p.id === productId);
    const variantIds = product?.variants.nodes.map((v) => v.id) || [];

    if (checked) {
      // Selecting
      if (variantId) {
        newUnselectedIds.delete(productId);
        newUnselectedIds.delete(variantId);
      } else {
        newUnselectedIds.delete(productId);
        variantIds.forEach((id) => newUnselectedIds.delete(id));
      }
    } else {
      // Unselecting
      if (variantId) {
        newUnselectedIds.add(variantId);

        // Update product state based on all variant selections
        const allVariantsUnselected = variantIds.every((id) => newUnselectedIds.has(id));
        if (allVariantsUnselected) {
          newUnselectedIds.add(productId);
        } else {
          newUnselectedIds.delete(productId);
        }
      } else {
        newUnselectedIds.add(productId);
        variantIds.forEach((id) => newUnselectedIds.add(id));
      }
    }

    setUnselectedIds(newUnselectedIds);
  }

  return (
    <Page fullWidth>
      <TitleBar title="Select Products" />
      <BlockStack gap="500">
        {error && (
          <Banner tone="critical" title="Connection Error">
            <p>{error}</p>
            <p>Please refresh the page to try again.</p>
          </Banner>
        )}
        <Box>
          <InlineStack align="space-between" blockAlign="center">
            <InlineStack gap="300" blockAlign="center">
              <Button onClick={() => navigate(-1)} icon={ArrowLeftIcon} accessibilityLabel="Go back" />
              <Text as="h1" variant="headingLg">Step 1: Product & Variant Filtering</Text>
            </InlineStack>
            <Button variant="primary" onClick={() => goToNextStep()}>Next step: Modifications</Button>
          </InlineStack>
        </Box>

        {/* Products and Variants Count Display */}
        {currentProductsCount && (
          <Box>
            <Card>
              <Box padding="300">
                <InlineStack gap="400" blockAlign="center">
                  {/* Products Count */}
                  <InlineStack gap="200" blockAlign="center">
                    <Text as="p" variant="bodyMd" fontWeight="medium">
                      Products selected: {currentProductsCount.count.toLocaleString()}
                    </Text>
                    {currentProductsCount.precision === 'AT_LEAST' && (
                      <>
                        <Text as="span" variant="bodySm" tone="subdued">
                          (at least)
                        </Text>
                        <Tooltip content="Shopify limits count queries to 10,000 products for performance reasons. The actual number of matching products may be higher than displayed.">
                          <Icon source={InfoIcon} tone="subdued" />
                        </Tooltip>
                      </>
                    )}
                  </InlineStack>


                </InlineStack>
              </Box>
            </Card>
          </Box>
        )}

        <Layout>
          <Layout.Section variant="fullWidth">
            <Card>
              <EnhancedProductsFilter
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                activeFilters={activeFilters}
                onFiltersChange={setActiveFilters}
                onSearch={handleSearch}
                advancedFilters={advancedFilters}
                onAdvancedFiltersChange={handleAdvancedFiltersChange}
                filterMode={filterMode}
                onFilterModeChange={setFilterMode}
                filterLogic={filterLogic}
                onFilterLogicChange={setFilterLogic}
              />
            </Card>
          </Layout.Section>
        </Layout>

        <ProductSelectionTable
          headings={[
            { title: "", id: "checkbox" },
            { title: "Product", id: "product" },
            { title: "", id: "product1" },
            { title: "", id: "product2" },
            { title: "", id: "product3" },
            { title: "", id: "product4" },
            { title: "", id: "product5" },
            { title: "Status", id: "status" },
            { title: "", id: "status1" },
            { title: "Inventory" },
            { title: "", id: "inventory1" },
            { title: "Info" },
            { title: "", id: "info1" },
            { title: "", id: "info2" },
            { title: "", id: "info3" },
            { title: "", id: "info4" },
            { title: "", id: "info5" },
          ]}
          products={products}
          pageInfo={pageInfo}
          seeAllVariants={(productId) => {
            shopify.modal.show('my-modal');
            variantFetcher.submit(
              { productId, type: "get-variants" },
              { method: "post", encType: "application/json" }
            )
          }}
          unselectedIds={unselectedIds}
          onSelectionChange={onSelectionChange}
          onNextPage={() => {
            fetcher.submit(
              { searchQuery, cursor: pageInfo.endCursor },
              { method: "post", encType: "application/json" }
            );
          }}
          onPreviousPage={() => {
            fetcher.submit(
              { searchQuery, cursor: pageInfo.startCursor },
              { method: "post", encType: "application/json" }
            );
          }}
          openProductDetail={(productGid) => {
            const productId = extractIdFromGid(productGid);
            const adminUrl = `https://admin.shopify.com/store/${loaderData.storeHandle}/products/${productId}`;
            // Only open if we're in the browser and have a store handle
            if (typeof window !== 'undefined' && loaderData.storeHandle) {
              window.open(adminUrl, '_blank');
            }
          }}
        />
      </BlockStack>

      <Modal id="my-modal">
        {product ? (
          <ProductVariantTable
            product={product}
            unselectedIds={unselectedIds}
            onSelectionChange={onSelectionChange}
          />
        ) : (
          <Box padding={"400"}>
            <p>Loading variant data...</p>
          </Box>
        )}
        <ui-title-bar title={product?.title ? `${product.title} - Variants` : 'Loading...'} />
      </Modal>
    </Page>
  );
}
