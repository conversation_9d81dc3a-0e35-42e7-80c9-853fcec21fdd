import {
    Page,
    Layout,
    Card,
    Text,
    BlockStack,
    InlineStack,
    Button,
    Badge,
    EmptyState,
    Tabs,
    IndexTable,
    Avatar,
    Spinner,
} from "@shopify/polaris";
import { useLoaderData, useNavigate, Link } from "@remix-run/react";
import { TitleBar } from "@shopify/app-bridge-react";
import { LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "~/shopify.server";
import { getJobsByShop } from "~/utils/jobManager.server";
import { JobDetail } from "~/types/models";
import { useState } from "react";
import JobControlButtons from "~/components/JobControlButtons";
import { useJobsRealTimeUpdates } from "~/hooks/useShopifyRealTimeUpdates";
import ErrorBoundary from "~/components/ErrorBoundary";

export const loader = async ({ request }: LoaderFunctionArgs) => {
    const { session } = await authenticate.admin(request);
    
    const jobs = await getJobsByShop(session.shop, 20);
    
    return { jobs };
};

function getStatusBadge(status: string) {
    switch (status) {
        case 'SCHEDULED':
            return <Badge tone="attention">Scheduled</Badge>;
        case 'IN_PROGRESS':
            return <Badge tone="info">In Progress</Badge>;
        case 'COMPLETED':
            return <Badge tone="success">Completed</Badge>;
        case 'FAILED':
            return <Badge tone="critical">Failed</Badge>;
        case 'CANCELLED':
            return <Badge>Cancelled</Badge>;
        default:
            return <Badge>{status}</Badge>;
    }
}

function formatDateTime(dateString?: string) {
    if (!dateString) return 'Not set';
    const date = new Date(dateString);
    // Use a consistent format to avoid hydration mismatches
    return date.toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    });
}

export default function JobsList() {
    const navigate = useNavigate();
    const { jobs } = useLoaderData<typeof loader>() as { jobs: JobDetail[] };
    const [selectedTab, setSelectedTab] = useState(0);

    // Set up real-time updates for jobs with active status
    const { isPolling, hasActiveJobs } = useJobsRealTimeUpdates(jobs, {
        onStatusChange: (_updatedJob: JobDetail, _previousStatus: string) => {
            // Status changes are handled automatically through revalidation
            // Additional status change handling can be added here if needed
        },
        onProgressUpdate: (_updatedJob: JobDetail, _progress: number) => {
            // Progress updates are handled automatically through revalidation
        },
    });

    // Filter jobs based on selected tab
    const getFilteredJobs = () => {
        switch (selectedTab) {
            case 0: // All
                return jobs;
            case 1: // Active (In Progress)
                return jobs.filter(job => job.status === 'IN_PROGRESS');
            case 2: // Scheduled
                return jobs.filter(job => job.status === 'SCHEDULED');
            case 3: // Completed
                return jobs.filter(job => job.status === 'COMPLETED');
            case 4: // Favourites (for now, just show all - can be enhanced later)
                return jobs;
            default:
                return jobs;
        }
    };

    const filteredJobs = getFilteredJobs();

    const tabs = [
        { id: 'all', content: 'All', badge: jobs.length.toString() },
        { id: 'active', content: 'Active', badge: jobs.filter(j => j.status === 'IN_PROGRESS').length.toString() },
        { id: 'scheduled', content: 'Scheduled', badge: jobs.filter(j => j.status === 'SCHEDULED').length.toString() },
        { id: 'completed', content: 'Completed', badge: jobs.filter(j => j.status === 'COMPLETED').length.toString() },
        { id: 'favourites', content: 'Favourites' },
    ];

    const resourceName = {
        singular: 'job',
        plural: 'jobs',
    };

    return (
        <Page fullWidth>
            <TitleBar title="Bulk Edit Jobs" />
            <BlockStack gap="500">
                <InlineStack align="space-between" blockAlign="center">
                    <InlineStack gap="300" blockAlign="center">
                        <Text as="h1" variant="headingLg">Bulk Edit History</Text>
                        {isPolling && hasActiveJobs && (
                            <InlineStack gap="100" blockAlign="center">
                                <Spinner size="small" />
                                <Text as="span" variant="bodyMd" tone="subdued">Live updates</Text>
                            </InlineStack>
                        )}
                    </InlineStack>
                    <Button variant="primary" onClick={() => navigate('/app/select-products')}>
                        Create New Job
                    </Button>
                </InlineStack>

                <Layout>
                    <Layout.Section>
                        <Card padding="0">
                            {jobs.length === 0 ? (
                                <div style={{ padding: '20px' }}>
                                    <EmptyState
                                        heading="No jobs yet"
                                        action={{
                                            content: 'Create your first job',
                                            onAction: () => navigate('/app/select-products'),
                                        }}
                                        image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
                                    >
                                        <Text as="p" variant="bodyMd">
                                            Start by creating a bulk edit job to modify your products and variants.
                                        </Text>
                                    </EmptyState>
                                </div>
                            ) : (
                                <>
                                    <Tabs
                                        tabs={tabs}
                                        selected={selectedTab}
                                        onSelect={setSelectedTab}
                                    />

                                    <IndexTable
                                        resourceName={resourceName}
                                        itemCount={filteredJobs.length}
                                        selectable={false}
                                        headings={[
                                            { title: 'Job title' },
                                            { title: 'Status' },
                                            { title: 'Scheduled/Run at' },
                                            { title: 'Info' },
                                            { title: 'Actions' },
                                        ]}
                                        hasZebraStriping
                                    >
                                        {filteredJobs.map((job, index) => (
                                            <IndexTable.Row
                                                id={job.id}
                                                key={job.id}
                                                position={index}
                                            >
                                                <IndexTable.Cell>
                                                    <InlineStack gap="300" blockAlign="center">
                                                        <Avatar
                                                            customer
                                                            size="xs"
                                                            name={job.title}
                                                        />
                                                        <Link to={`/app/jobs/${job.id}`}>
                                                            <Text as="span" variant="bodyMd" tone="base">
                                                                {job.title}
                                                            </Text>
                                                        </Link>
                                                    </InlineStack>
                                                </IndexTable.Cell>
                                                <IndexTable.Cell>
                                                    {getStatusBadge(job.status)}
                                                </IndexTable.Cell>
                                                <IndexTable.Cell>
                                                    <Text as="span" variant="bodyMd">
                                                        {formatDateTime(job.scheduledAt || job.createdAt)}
                                                    </Text>
                                                </IndexTable.Cell>
                                                <IndexTable.Cell>
                                                    <Text as="span" variant="bodyMd" tone="subdued">
                                                        Updated {job.totalProducts} products & {job.modifications.length} variants
                                                    </Text>
                                                </IndexTable.Cell>
                                                <IndexTable.Cell>
                                                    <ErrorBoundary>
                                                        <JobControlButtons job={job} variant="inline" />
                                                    </ErrorBoundary>
                                                </IndexTable.Cell>
                                            </IndexTable.Row>
                                        ))}
                                    </IndexTable>
                                </>
                            )}
                        </Card>
                    </Layout.Section>
                </Layout>
            </BlockStack>
        </Page>
    );
}
