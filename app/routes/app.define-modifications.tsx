import {
    <PERSON>,
    Layout,
    Card,
    TextField,
    Button,
    BlockStack,
    Text,
    Box,
    InlineStack,
} from "@shopify/polaris";
import { useState, useEffect } from "react";
import { useFetcher, useNavigate, useLoaderData } from "@remix-run/react";
import { TitleBar } from "@shopify/app-bridge-react";
import { ArrowLeftIcon } from "@shopify/polaris-icons";
import PopoverWithSearchableList from "~/components/PopoverWithSearchableList";
import { modificationFields } from "~/utils/constants";
import ProductPreviewTable from "~/components/ProductPreviewTable";
import { LoaderData } from "./app.select-products";
import { ActionFunctionArgs, LoaderFunctionArgs, redirect } from "@remix-run/node";
import { authenticate } from "~/shopify.server";
import { getUnselectedIds } from "~/utils/tempSelection.server";
import { storeModificationsData } from "~/utils/jobManager.server";

export const action = async ({ request }: ActionFunctionArgs) => {
    const { admin } = await authenticate.admin(request);
    const body = await request.json();

    console.log("define-mods action called", body);

    const { cursor, query: searchQuery, optionName, optionValue, type, sessionKey, modifications, unselectedIds } = body;

    // Handle storing modifications and redirecting to create-job page
    if (type === "store-modifications" && modifications) {
        try {
            // Store modifications data
            const newSessionKey = await storeModificationsData(request, modifications, unselectedIds, sessionKey);

            return redirect(`/app/create-job?sessionKey=${newSessionKey}`);
        } catch (error) {
            console.error('Error storing modifications:', error);
            throw error;
        }
    }
    const pageSize = 10;

    let query = searchQuery || '';
    if (optionName && optionValue) {
        query = `${query} option:${optionName}:${optionValue}`.trim();
    }

    try {
        const response = await admin.graphql(`
      query getProducts($query: String, $first: Int!, $after: String) {
        products(first: $first, after: $after, query: $query) {
          pageInfo {
            hasNextPage
            hasPreviousPage
            startCursor
            endCursor
          }
          nodes {
            id
            title
            images(first: 1) {
              nodes {
                url
              }
            }
            title
            status
            totalInventory
            vendor
            category {
              name
            }
            variants(first: 100) {
              nodes {
                id
                title
                sku
                price
                inventoryQuantity
              }
            }
          }
        }
      }
    `, {
            variables: {
                after: cursor || null,
                query,
                first: pageSize
            }
        });

        const responseData = await response.json();
        const { data, errors } = responseData as { data: any; errors?: any[] };

        if (errors) {
            console.error('GraphQL errors:', errors);
            throw new Error('Failed to fetch products');
        }

        return {
            products: data.products.nodes,
            pageInfo: data.products.pageInfo,
        };
    } catch (error) {
        console.error('Error fetching products:', error);
        throw error;
    }
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
    const { admin } = await authenticate.admin(request);

    // Get sessionKey from URL params
    const url = new URL(request.url);
    const sessionKey = url.searchParams.get('sessionKey');

    // Retrieve unselected IDs from database
    const unselectedIds = sessionKey ? await getUnselectedIds(sessionKey) : [];

    return { unselectedIds, sessionKey };
}

export default function DefineModifications() {
    const navigate = useNavigate();
    const fetcher = useFetcher<LoaderData>();
    const loaderData = useLoaderData<typeof loader>();

    // Get unselected IDs from the loader
    const unselectedIds = loaderData.unselectedIds || [];
    const sessionKey = loaderData.sessionKey;

    useEffect(() => {
        if (!fetcher.data && fetcher.state === "idle") {
            fetcher.submit(
                { query: "" },
                { method: "post", encType: "application/json" }
            );
        }
    }, [fetcher]);

    const products = fetcher.data?.products ?? [];
    const pageInfo = fetcher.data?.pageInfo ?? {
        hasNextPage: false,
        hasPreviousPage: false,
        startCursor: null,
        endCursor: null,
    };

    const [selectedSegmentIndex, setSelectedSegmentIndex] = useState(4);
    const [fieldValue, setFieldValue] = useState("");
    const [modifications, setModifications] = useState<Array<{
        fieldType: 'product' | 'variant';
        fieldName: string;
        fieldValue: string;
    }>>([]);

    const handleSegmentSelect = (segmentIndex: string) => {
        setSelectedSegmentIndex(Number(segmentIndex));
    };

    const handleAddModification = () => {
        const selectedField = modificationFields[selectedSegmentIndex];
        if (selectedField && fieldValue.trim()) {
            const fieldType = selectedField.id === 'category' || selectedField.id === 'description' ? 'product' : 'variant';

            setModifications(prev => [...prev, {
                fieldType,
                fieldName: selectedField.id,
                fieldValue: fieldValue.trim(),
            }]);

            setFieldValue("");
        }
    };

    const handleRemoveModification = (index: number) => {
        setModifications(prev => prev.filter((_, i) => i !== index));
    };

    const handleNextStep = async () => {
        if (modifications.length === 0) {
            alert("Please add at least one modification before proceeding.");
            return;
        }

        // Submit modifications to the action and redirect
        fetcher.submit(
            {
                type: "store-modifications",
                sessionKey: sessionKey || '',
                modifications: modifications,
                unselectedIds: unselectedIds
            },
            { method: "post", encType: "application/json" }
        );
    };

    return (
        <Page fullWidth>
            <TitleBar title="Define Modifications" />
            <BlockStack gap="500">
                <Box>
                    <InlineStack align="space-between" blockAlign="center">
                        <InlineStack gap="300" blockAlign="center">
                            <Button onClick={() => navigate('/app')} icon={ArrowLeftIcon} accessibilityLabel="Go back" />
                            <Text as="h1" variant="headingLg">Step 2: Define Modifications</Text>
                        </InlineStack>
                        <Button variant="primary" onClick={handleNextStep}>Next step: Create Job</Button>
                    </InlineStack>
                </Box>

                {/* Debug display for unselected IDs */}
                <Card>
                    <BlockStack gap="200">
                        <Text as="h2" variant="headingMd">Selected Products/Variants</Text>
                        <Text as="p" variant="bodyMd">
                            Unselected items count: {unselectedIds.length}
                        </Text>
                        {unselectedIds.length > 0 && (
                            <Text as="p" variant="bodyMd" tone="subdued">
                                Unselected IDs: {unselectedIds.slice(0, 5).join(', ')}
                                {unselectedIds.length > 5 && '...'}
                            </Text>
                        )}
                        {sessionKey && (
                            <Text as="p" variant="bodyMd" tone="subdued">
                                Session Key: {sessionKey}
                            </Text>
                        )}
                    </BlockStack>
                </Card>

                <Layout>
                    <Layout.Section variant="fullWidth">
                        <Card>
                            <BlockStack gap="400">
                                <Text as="h2" variant="headingMd">Select field to edit</Text>
                                <InlineStack gap="400" blockAlign="end">
                                    <div style={{ minWidth: '200px' }}>
                                        <PopoverWithSearchableList
                                            segments={modificationFields}
                                            selectedSegmentIndex={selectedSegmentIndex}
                                            onSegmentSelect={handleSegmentSelect}
                                            activator={(onClick) => (
                                                <Button onClick={onClick} disclosure>
                                                    {modificationFields[selectedSegmentIndex].label}
                                                </Button>
                                            )}
                                        />
                                    </div>
                                    <div style={{ minWidth: '200px' }}>
                                        <TextField
                                            label="New value"
                                            value={fieldValue}
                                            onChange={setFieldValue}
                                            placeholder="Enter new value"
                                            autoComplete="off"
                                        />
                                    </div>
                                    <Button variant="primary" onClick={handleAddModification}>
                                        Add Modification
                                    </Button>
                                </InlineStack>
                            </BlockStack>
                        </Card>
                    </Layout.Section>
                </Layout>

                {modifications.length > 0 && (
                    <Card>
                        <BlockStack gap="400">
                            <Text as="h2" variant="headingMd">Modifications ({modifications.length})</Text>
                            <BlockStack gap="200">
                                {modifications.map((mod, index) => (
                                    <InlineStack key={index} align="space-between" blockAlign="center">
                                        <InlineStack gap="400">
                                            <Text as="span" variant="bodyMd">
                                                <strong>{mod.fieldType === 'product' ? 'Product' : 'Variant'}</strong>
                                            </Text>
                                            <Text as="span" variant="bodyMd">{mod.fieldName}:</Text>
                                            <Text as="span" variant="bodyMd" tone="success">{mod.fieldValue}</Text>
                                        </InlineStack>
                                        <Button
                                            variant="plain"
                                            tone="critical"
                                            onClick={() => handleRemoveModification(index)}
                                        >
                                            Remove
                                        </Button>
                                    </InlineStack>
                                ))}
                            </BlockStack>
                        </BlockStack>
                    </Card>
                )}

                <ProductPreviewTable
                    headings={[
                        { title: "" },
                        { title: "Product" },
                        { title: "Status" },
                        { title: "Inventory" },
                        { title: "Info" },
                    ]}
                    products={products}
                    pageInfo={pageInfo}
                    fetcher={fetcher}
                />


            </BlockStack>
        </Page>
    );
}