import { LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "~/shopify.server";
import { getJobsByShop } from "~/utils/jobManager.server";
import { serverError } from "./api.stats";

/**
 * API endpoint for recent jobs with real-time updates support
 *
 * Returns recent jobs from the database instead of dummy data
 * Supports real-time updates through the useShopifyRealTimeUpdates hook
 */
export async function loader({ request }: LoaderFunctionArgs) {
    try {
        // Authenticate using session tokens (Shopify iframe requirement)
        const { session } = await authenticate.admin(request);
        const shopDomain = session.shop;

        // Get recent jobs (limit to 5 for dashboard display)
        const jobs = await getJobsByShop(shopDomain, 5);

        // Filter to show only active and recent jobs (last 7 days or active status)
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        const recentJobs = jobs.filter(job => {
            const isActive = job.status === 'IN_PROGRESS' || job.status === 'SCHEDULED';
            const isRecent = new Date(job.createdAt) >= sevenDaysAgo;
            return isActive || isRecent;
        });

        return recentJobs;
    } catch (error) {
        console.error("Error fetching recent jobs:", error);
        throw serverError("Internal server error while fetching recent jobs");
    }
}