import type { LoaderFunctionArgs } from "@remix-run/node";
import { Page, Layout, BlockStack, } from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { appName } from "~/utils/constants";
import { StatsComponent } from "~/components/StatsComponent";
import RecentJobsList from "~/components/RecentJobsList";
import StartNewJob from "~/components/StartNewJob";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);

  return null;
};

export default function Index() {
  return (
    <Page fullWidth>
      <TitleBar title={appName} />
      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <StatsComponent />
          </Layout.Section>

          <Layout.Section>
            <StartNewJob />
          </Layout.Section>

          <Layout.Section variant="oneThird" />

          <Layout.Section>
            <RecentJobsList />
          </Layout.Section>

          <Layout.Section variant="oneThird" />
        </Layout>
      </BlockStack>
    </Page>
  );
}
