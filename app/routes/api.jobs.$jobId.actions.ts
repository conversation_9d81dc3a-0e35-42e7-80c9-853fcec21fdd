import { ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "~/shopify.server";
import { getJobById, updateJobStatus } from "~/utils/jobManager.server";
import { enqueueBulkUpdateJob, getShopQueue } from "~/services/jobQueue.server";

type JobAction = "run" | "stop";
type JobStatus = "SCHEDULED" | "IN_PROGRESS" | "COMPLETED" | "FAILED" | "CANCELLED";

interface ActionRequest {
  action: JobAction;
}

interface ActionResponse {
  success: boolean;
  message?: string;
  error?: string;
}

interface Job {
  id: string;
  status: JobStatus;
  shopId: string;
  modifications: Array<{
    fieldType: string;
    fieldName: string;
    fieldValue: string;
  }>;
  unselectedIds?: string[];
  filterCriteria?: any;
}

// Helper function to create JSON responses
function createJsonResponse(data: ActionResponse, status: number = 200): Response {
  return new Response(JSON.stringify(data), {
    status,
    headers: { "Content-Type": "application/json" },
  });
}

export const action = async ({ request, params }: ActionFunctionArgs) => {
  try {
    const { session } = await authenticate.admin(request);
    const jobId = params.jobId;

    // Validate job ID
    if (!jobId) {
      return createJsonResponse(
        { success: false, error: "Job ID is required" },
        400
      );
    }

    // Parse and validate request body
    const actionRequest = await parseActionRequest(request);
    if (!actionRequest.success) {
      return createJsonResponse(actionRequest.response, 400);
    }

    const { action: actionType } = actionRequest.data;

    // Get the job
    const job = await getJobById(jobId, session.shop);
    if (!job) {
      return createJsonResponse(
        { success: false, error: "Job not found" },
        404
      );
    }

    // Handle the action
    switch (actionType) {
      case "run":
        return await handleRunJob(job, session.shop);
      case "stop":
        return await handleStopJob(job, session.shop);
      default:
        return createJsonResponse(
          { success: false, error: "Unknown action" },
          400
        );
    }
  } catch (error) {
    console.error("Error in job action:", error);
    return createJsonResponse(
      { success: false, error: "Internal server error" },
      500
    );
  }
};

async function parseActionRequest(request: Request): Promise<{
  success: true;
  data: ActionRequest;
} | {
  success: false;
  response: ActionResponse;
}> {
  try {
    const body = await request.json();
    const { action: actionType } = body;

    if (!actionType) {
      return {
        success: false,
        response: { success: false, error: "Action is required" },
      };
    }

    if (actionType !== "run" && actionType !== "stop") {
      return {
        success: false,
        response: { success: false, error: 'Invalid action. Must be "run" or "stop"' },
      };
    }

    return {
      success: true,
      data: { action: actionType },
    };
  } catch (error) {
    return {
      success: false,
      response: { success: false, error: "Invalid JSON in request body" },
    };
  }
}

async function handleRunJob(job: Job, shopDomain: string) {
  // Validate job can be run
  const validationResult = validateJobCanRun(job);
  if (!validationResult.canRun) {
    return createJsonResponse(
      { success: false, error: validationResult.reason },
      400
    );
  }

  try {
    // Prepare job data for Bull queue
    const bulkUpdateJobData = {
      jobId: job.id,
      shopDomain: shopDomain,
      modifications: job.modifications.map((mod: any) => ({
        fieldType: mod.fieldType,
        fieldName: mod.fieldName,
        fieldValue: mod.fieldValue,
      })),
      productIds: [], // Will be populated when job starts processing
      options: {
        batchSize: 10, // Default batch size
        delayBetweenBatches: 1000, // Default delay
      },
    };

    // Enqueue the job to Bull queue
    await enqueueBulkUpdateJob(bulkUpdateJobData);

    // Update job status to IN_PROGRESS
    await updateJobStatus(job.id, "IN_PROGRESS", shopDomain);

    console.log(`✅ Job ${job.id} manually triggered for shop ${shopDomain}`);

    return createJsonResponse({
      success: true,
      message: "Job enqueued successfully",
    });
  } catch (error) {
    console.error(`❌ Failed to enqueue job ${job.id}:`, error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    return createJsonResponse(
      { success: false, error: `Failed to enqueue job: ${errorMessage}` },
      500
    );
  }
}

function validateJobCanRun(job: Job): { canRun: boolean; reason?: string } {
  const runnableStatuses: JobStatus[] = ["SCHEDULED"];

  if (!runnableStatuses.includes(job.status)) {
    switch (job.status) {
      case "IN_PROGRESS":
        return { canRun: false, reason: "Job is already running" };
      case "COMPLETED":
        return { canRun: false, reason: "Job has already completed" };
      case "FAILED":
        return { canRun: false, reason: "Job has failed and cannot be rerun" };
      case "CANCELLED":
        return { canRun: false, reason: "Job was cancelled and cannot be rerun" };
      default:
        return { canRun: false, reason: "Job is in an invalid state for running" };
    }
  }

  // Additional validation: check if job has modifications
  if (!job.modifications || job.modifications.length === 0) {
    return { canRun: false, reason: "Job has no modifications to apply" };
  }

  return { canRun: true };
}

async function handleStopJob(job: Job, shopDomain: string) {
  // Validate job can be stopped
  const validationResult = validateJobCanStop(job);
  if (!validationResult.canStop) {
    return createJsonResponse(
      { success: false, error: validationResult.reason },
      400
    );
  }

  try {
    // First, update job status to CANCELLED in database
    // This is the primary signal that the worker checks
    await updateJobStatus(job.id, "CANCELLED", shopDomain);
    console.log(`🛑 Job ${job.id} status updated to CANCELLED in database`);

    // Try to remove the job from Bull queue (if it still exists)
    try {
      const queue = getShopQueue(shopDomain);
      const bullJob = await queue.getJob(job.id);

      if (bullJob) {
        // Check if job is still waiting or active
        const jobState = await bullJob.getState();
        console.log(`📊 Bull job ${job.id} state: ${jobState}`);

        if (jobState === 'waiting' || jobState === 'delayed') {
          await bullJob.remove();
          console.log(`🛑 Removed waiting job ${job.id} from Bull queue`);
        } else if (jobState === 'active') {
          console.log(`⚠️ Job ${job.id} is currently active - worker will check cancellation status`);
        } else {
          console.log(`ℹ️ Job ${job.id} is in state '${jobState}' - cannot remove`);
        }
      } else {
        console.log(`⚠️ Job ${job.id} not found in Bull queue (may have been auto-removed)`);
      }
    } catch (bullError) {
      // Don't fail the entire operation if Bull queue removal fails
      console.warn(`⚠️ Failed to remove job ${job.id} from Bull queue:`, bullError);
      console.log(`✅ Job cancellation will still work via database status check`);
    }

    console.log(`✅ Job ${job.id} stopped successfully for shop ${shopDomain}`);

    return createJsonResponse({
      success: true,
      message: "Job stopped successfully",
    });
  } catch (error) {
    console.error(`❌ Failed to stop job ${job.id}:`, error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    return createJsonResponse(
      { success: false, error: `Failed to stop job: ${errorMessage}` },
      500
    );
  }
}

function validateJobCanStop(job: Job): { canStop: boolean; reason?: string } {
  const stoppableStatuses: JobStatus[] = ["IN_PROGRESS", "SCHEDULED"];

  if (!stoppableStatuses.includes(job.status)) {
    switch (job.status) {
      case "COMPLETED":
        return { canStop: false, reason: "Job has already completed" };
      case "FAILED":
        return { canStop: false, reason: "Job has already failed" };
      case "CANCELLED":
        return { canStop: false, reason: "Job is already cancelled" };
      default:
        return { canStop: false, reason: "Job is in an invalid state for stopping" };
    }
  }

  return { canStop: true };
}
