import {
    Page,
    Layout,
    Card,
    Text,
    BlockStack,
    InlineStack,
    Badge,
    Button,
    DataTable,
    Box,
    Divider,
    Spinner,
} from "@shopify/polaris";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { TitleBar } from "@shopify/app-bridge-react";
import { ArrowLeftIcon } from "@shopify/polaris-icons";
import { LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "~/shopify.server";
import { getJobById } from "~/utils/jobManager.server";
import type { JobDetail } from "~/types/models";
import JobControlButtons from "~/components/JobControlButtons";
import JobProgressIndicator from "~/components/JobProgressIndicator";
import { useJobRealTimeUpdates } from "~/hooks/useShopifyRealTimeUpdates";
import { isSignificantStatusChange } from "~/hooks/useJobPolling";

import { useJobToast } from "~/contexts/ToastContext";
import ErrorBoundary from "~/components/ErrorBoundary";
import { sanitizeText } from "~/utils/sanitization";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
    const { session } = await authenticate.admin(request);
    const jobId = params.jobId;
    
    if (!jobId) {
        throw new Response("Job ID is required", { status: 400 });
    }

    const job = await getJobById(jobId, session.shop);
    
    if (!job) {
        throw new Response("Job not found", { status: 404 });
    }

    return { job };
};

function getStatusBadge(status: string) {
    switch (status) {
        case 'SCHEDULED':
            return <Badge tone="attention">Scheduled</Badge>;
        case 'IN_PROGRESS':
            return <Badge tone="info">In Progress</Badge>;
        case 'COMPLETED':
            return <Badge tone="success">Completed</Badge>;
        case 'FAILED':
            return <Badge tone="critical">Failed</Badge>;
        case 'CANCELLED':
            return <Badge>Cancelled</Badge>;
        default:
            return <Badge>{status}</Badge>;
    }
}

function formatDateTime(dateString?: string) {
    if (!dateString) return 'Not set';
    const date = new Date(dateString);
    // Use a consistent format to avoid hydration mismatches
    return date.toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    });
}

export default function JobDetail() {
    const navigate = useNavigate();
    const { job } = useLoaderData<typeof loader>() as { job: JobDetail };
    const jobToast = useJobToast();

    // Set up real-time updates for header polling indicator
    const { isPolling } = useJobRealTimeUpdates(job);

    // Prepare modifications table data
    const modificationsRows = job.modifications.map(mod => [
        mod.fieldType === 'product' ? 'Product' : 'Variant',
        mod.fieldName,
        mod.fieldValue,
    ]);

    // Prepare product variants table data (showing first 50)
    const productVariantsRows = job.productVariants.slice(0, 50).map(pv => [
        pv.productId,
        pv.variantId || 'N/A',
        getStatusBadge(pv.status),
        pv.errorMessage || 'N/A',
    ]);

    return (
        <Page fullWidth>
            <TitleBar title={`Job: ${job.title}`} />
            <BlockStack gap="500">
                <Box>
                    <InlineStack align="space-between" blockAlign="center">
                        <InlineStack gap="300" blockAlign="center">
                            <Button onClick={() => navigate('/app')} icon={ArrowLeftIcon} accessibilityLabel="Go back" />
                            <Text as="h1" variant="headingLg">{job.title}</Text>
                        </InlineStack>
                        <InlineStack gap="300" blockAlign="center">
                            {getStatusBadge(job.status)}
                            {isPolling && (
                                <InlineStack gap="100" blockAlign="center">
                                    <Spinner size="small" />
                                    <Text as="span" variant="bodyMd" tone="subdued">Live updates</Text>
                                </InlineStack>
                            )}
                            {/* Job controls moved to main content area to prevent duplicate renders */}
                        </InlineStack>
                    </InlineStack>
                </Box>

                <Layout>
                    <Layout.Section variant="oneThird">
                        <Card>
                            <BlockStack gap="400">
                                <Text as="h2" variant="headingMd">Job Info</Text>
                                
                                <BlockStack gap="200">
                                    <Text variant="bodyMd" as="p">
                                        <strong>Job title:</strong> {job.title}
                                    </Text>
                                    <Text variant="bodyMd" as="p">
                                        <strong>Status:</strong> {getStatusBadge(job.status)}
                                    </Text>
                                    <Text variant="bodyMd" as="p">
                                        <strong>Description:</strong> {job.description || 'No description'}
                                    </Text>
                                </BlockStack>

                                <Divider />

                                <BlockStack gap="200">
                                    <Text variant="bodyMd" as="p">
                                        <strong>When to run:</strong> {job.scheduledAt ? 'Scheduled' : 'Now/Later'}
                                    </Text>
                                    {job.scheduledAt && (
                                        <Text variant="bodyMd" as="p">
                                            <strong>Select date:</strong> {formatDateTime(job.scheduledAt)}
                                        </Text>
                                    )}
                                    <Text variant="bodyMd" as="p">
                                        <strong>Timezone:</strong> America/New_York (GMT-04:00 (EST))
                                    </Text>
                                </BlockStack>

                                <Divider />

                                <BlockStack gap="200">
                                    <Text variant="bodyMd" as="p">
                                        <strong>Created:</strong> {formatDateTime(job.createdAt)}
                                    </Text>
                                    {job.startedAt && (
                                        <Text variant="bodyMd" as="p">
                                            <strong>Started:</strong> {formatDateTime(job.startedAt)}
                                        </Text>
                                    )}
                                    {job.completedAt && (
                                        <Text variant="bodyMd" as="p">
                                            <strong>Completed:</strong> {formatDateTime(job.completedAt)}
                                        </Text>
                                    )}
                                </BlockStack>

                                <JobProgressIndicator
                                    job={job}
                                    onStatusChange={(updatedJob: JobDetail, previousStatus: string) => {
                                        if (isSignificantStatusChange(previousStatus, updatedJob.status)) {
                                            if (updatedJob.status === 'COMPLETED') {
                                                jobToast.showJobCompleted(sanitizeText(updatedJob.title));
                                            } else if (updatedJob.status === 'FAILED') {
                                                jobToast.showJobFailed(sanitizeText(updatedJob.title));
                                            } else if (updatedJob.status === 'CANCELLED') {
                                                jobToast.showJobStopped(sanitizeText(updatedJob.title));
                                            } else {
                                                jobToast.showGenericSuccess(`Job status changed to ${updatedJob.status}`);
                                            }
                                        }
                                    }}
                                />

                                <Divider />

                                <ErrorBoundary>
                                    <JobControlButtons job={job} />
                                </ErrorBoundary>
                            </BlockStack>
                        </Card>
                    </Layout.Section>

                    <Layout.Section>
                        <BlockStack gap="400">
                            <Card>
                                <BlockStack gap="400">
                                    <Text as="h2" variant="headingMd">
                                        Modifications ({job.modifications.length})
                                    </Text>
                                    
                                    <DataTable
                                        columnContentTypes={['text', 'text', 'text']}
                                        headings={['Type', 'Field', 'New Value']}
                                        rows={modificationsRows}
                                    />
                                </BlockStack>
                            </Card>

                            <Card>
                                <BlockStack gap="400">
                                    <InlineStack align="space-between">
                                        <Text as="h2" variant="headingMd">
                                            Progress Summary
                                        </Text>
                                        <InlineStack gap="200">
                                            <Badge tone="info">{`${job.totalProducts} total`}</Badge>
                                            <Badge tone="success">{`${job.successfulUpdates} success`}</Badge>
                                            <Badge tone="critical">{`${job.failedUpdates} failed`}</Badge>
                                        </InlineStack>
                                    </InlineStack>

                                    {job.productVariants.length > 0 && (
                                        <>
                                            <DataTable
                                                columnContentTypes={['text', 'text', 'text', 'text']}
                                                headings={['Product ID', 'Variant ID', 'Status', 'Error']}
                                                rows={productVariantsRows}
                                            />
                                            
                                            {job.productVariants.length > 50 && (
                                                <Text as="p" variant="bodyMd" tone="subdued">
                                                    Showing first 50 items. Total: {job.productVariants.length} items
                                                </Text>
                                            )}
                                        </>
                                    )}
                                </BlockStack>
                            </Card>
                        </BlockStack>
                    </Layout.Section>
                </Layout>
            </BlockStack>
        </Page>
    );
}
