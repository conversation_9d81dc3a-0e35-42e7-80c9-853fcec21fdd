// app/routes/api.stats.ts
import { LoaderFunctionArgs } from "@remix-run/node"; // or your specific runtime
import { getJobsCount, getPlanDetails, getTimeSaved, totalProductUpdated } from "~/data/dummyRepo";
import { StatsResponse } from "~/types/models";

function delay(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

export async function loader({ request }: LoaderFunctionArgs) {
    const url = new URL(request.url);
    const type = url.searchParams.get("type");

    await delay(2000);

    try {
        var result: StatsResponse = {
            totalJobs: getJobsCount(),
            plan: getPlanDetails(),
            timeSaved: getTimeSaved(),
            productsUpdated: totalProductUpdated()
        };

        return result;
    } catch (error) {
        console.error("Error fetching stats:", error);
        throw serverError("Internal server error while fetching stats");
    }
}

export function serverError(message: string) {
    return new Response(JSON.stringify({ error: message }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
    });
}