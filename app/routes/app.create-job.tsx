import {
    Page,
    Layout,
    Card,
    FormLayout,
    TextField,
    Button,
    BlockStack,
    Text,
    Box,
    InlineStack,
    Badge,
    DataTable,
    Select,
    Divider,
} from "@shopify/polaris";
import { useState, useCallback, useEffect } from "react";
import { useF<PERSON>cher, useNavigate, useLoaderData, Form } from "@remix-run/react";
import { TitleBar } from "@shopify/app-bridge-react";
import { ArrowLeftIcon, CalendarIcon } from "@shopify/polaris-icons";
import { ActionFunctionArgs, LoaderFunctionArgs, redirect } from "@remix-run/node";
import { authenticate } from "~/shopify.server";
import { getModificationsData, createJob } from "~/utils/jobManager.server";
import { getProductsFromShopify } from "~/data/graphql/getProducts";
import { LoaderData } from "./app.select-products";

export const action = async ({ request }: ActionFunctionArgs) => {
    const { admin } = await authenticate.admin(request);

    // Check content type to determine how to parse the request
    const contentType = request.headers.get("content-type");

    if (contentType?.includes("application/x-www-form-urlencoded")) {
        // Handle form submission for creating jobs
        const formData = await request.formData();
        const actionType = formData.get("actionType") as string;

        if (actionType === "create-job") {
            const title = formData.get("title") as string;
            const description = formData.get("description") as string;
            const scheduledAt = formData.get("scheduledAt") as string;
            const sessionKey = formData.get("sessionKey") as string;

            // Get modifications data from session
            const modificationsData = await getModificationsData(sessionKey);
            if (!modificationsData) {
                throw new Error("No modifications data found");
            }

            // Create the job
            const jobId = await createJob(request, {
                title,
                description: description || undefined,
                scheduledAt: scheduledAt || undefined,
                modifications: modificationsData.modifications,
                unselectedIds: modificationsData.unselectedIds,
            });

            return redirect(`/app/jobs/${jobId}`);
        }
    } else if (contentType?.includes("application/json")) {
        // Handle JSON requests for product fetching
        const body = await request.json();
        const { cursor, query: searchQuery } = body;
        const pageSize = 10;

        try {
            const products = await getProductsFromShopify({
                admin,
                searchQuery: searchQuery || '',
                cursor,
                pageSize,
            });

            return {
                products: products.nodes,
                pageInfo: products.pageInfo,
            };
        } catch (error) {
            console.error('Error fetching products:', error);
            return { products: [], pageInfo: { hasNextPage: false, hasPreviousPage: false, startCursor: null, endCursor: null } };
        }
    }

    // Default fallback
    return { products: [], pageInfo: { hasNextPage: false, hasPreviousPage: false, startCursor: null, endCursor: null } };
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
    const { admin } = await authenticate.admin(request);
    
    const url = new URL(request.url);
    const sessionKey = url.searchParams.get('sessionKey');
    
    if (!sessionKey) {
        return redirect('/app/define-modifications');
    }

    // Get modifications data from session
    const modificationsData = await getModificationsData(sessionKey);
    if (!modificationsData) {
        return redirect('/app/define-modifications');
    }

    return {
        modificationsData,
        sessionKey
    };
};

export default function CreateJob() {
    const navigate = useNavigate();
    const loaderData = useLoaderData<typeof loader>();
    const fetcher = useFetcher<LoaderData>();
    
    const { modificationsData, sessionKey } = loaderData;
    const { modifications, unselectedIds } = modificationsData;

    const [title, setTitle] = useState("Change for Christmas");
    const [description, setDescription] = useState("Change for Christmas");
    const [whenToRun, setWhenToRun] = useState("now");
    const [scheduledDate, setScheduledDate] = useState("");
    const [timezone, setTimezone] = useState("America/New_York");

    // Fetch products for preview
    useEffect(() => {
        if (!fetcher.data && fetcher.state === "idle") {
            fetcher.submit(
                { query: "" },
                { method: "post", encType: "application/json" }
            );
        }
    }, [fetcher]);

    const products = fetcher.data?.products ?? [];
    const pageInfo = fetcher.data?.pageInfo ?? {
        hasNextPage: false,
        hasPreviousPage: false,
        startCursor: null,
        endCursor: null,
    };

    // Filter out unselected products/variants for preview
    const selectedProducts = products.filter(product => {
        const productSelected = !unselectedIds.includes(product.id);
        if (productSelected) {
            // Filter variants
            if (product.variants?.nodes) {
                product.variants.nodes = product.variants.nodes.filter(variant =>
                    !unselectedIds.includes(variant.id)
                );
            }
        }
        return productSelected || (product.variants?.nodes || []).length > 0;
    });



    // Calculate preview data
    const previewData = (selectedProducts || []).flatMap(product => {
        const rows = [];
        
        // Product-level modifications
        const productMods = modifications.filter(mod => mod.fieldType === 'product');
        if (productMods.length > 0) {
            rows.push([
                product.title,
                '',
                productMods.map(mod => `${mod.fieldName}: ${mod.fieldValue}`).join(', ')
            ]);
        }

        // Variant-level modifications
        const variantMods = modifications.filter(mod => mod.fieldType === 'variant');
        if (variantMods.length > 0) {
            (product.variants?.nodes || []).forEach(variant => {
                rows.push([
                    '',
                    variant.title,
                    variantMods.map(mod => {
                        const currentValue = variant[mod.fieldName as keyof typeof variant] || '';
                        return `${mod.fieldName}: ${currentValue} → ${mod.fieldValue}`;
                    }).join(', ')
                ]);
            });
        }

        return rows;
    });

    const totalSelectedProducts = selectedProducts.length;
    const totalSelectedVariants = selectedProducts.reduce((sum, product) => sum + (product.variants?.nodes || []).length, 0);

    return (
        <Page fullWidth>
            <TitleBar title="Create Job" />
            <BlockStack gap="500">
                <Box>
                    <InlineStack align="space-between" blockAlign="center">
                        <InlineStack gap="300" blockAlign="center">
                            <Button onClick={() => navigate(-1)} icon={ArrowLeftIcon} accessibilityLabel="Go back" />
                            <Text as="h1" variant="headingLg">Step 3: Create Job</Text>
                        </InlineStack>
                    </InlineStack>
                </Box>

                <Layout>
                    <Layout.Section variant="oneThird">
                        <Card>
                            <Form id="job-form" method="post">
                                <input type="hidden" name="actionType" value="create-job" />
                                <input type="hidden" name="sessionKey" value={sessionKey} />
                                <FormLayout>
                                    <TextField
                                        label="Job title"
                                        value={title}
                                        onChange={setTitle}
                                        name="title"
                                        autoComplete="off"
                                    />
                                    
                                    <TextField
                                        label="Description"
                                        value={description}
                                        onChange={setDescription}
                                        name="description"
                                        multiline={3}
                                        autoComplete="off"
                                    />

                                    <Select
                                        label="When to run"
                                        options={[
                                            { label: 'Now/Later', value: 'now' },
                                            { label: 'Schedule for later', value: 'schedule' }
                                        ]}
                                        value={whenToRun}
                                        onChange={setWhenToRun}
                                    />

                                    {whenToRun === 'schedule' && (
                                        <>
                                            <TextField
                                                label="Select date"
                                                value={scheduledDate}
                                                onChange={setScheduledDate}
                                                name="scheduledAt"
                                                type="datetime-local"
                                                prefix={<CalendarIcon />}
                                                autoComplete="off"
                                            />
                                            
                                            <Select
                                                label="Timezone"
                                                options={[
                                                    { label: 'America/New_York (GMT-04:00 (EST))', value: 'America/New_York' }
                                                ]}
                                                value={timezone}
                                                onChange={setTimezone}
                                            />
                                        </>
                                    )}

                                    <Button
                                        variant="primary"
                                        submit
                                        size="large"
                                    >
                                        Run Job / Schedule Job
                                    </Button>
                                </FormLayout>
                            </Form>
                        </Card>
                    </Layout.Section>

                    <Layout.Section>
                        <Card>
                            <BlockStack gap="400">
                                <Text as="h2" variant="headingMd">
                                    Preview Changes ({totalSelectedProducts} products & {totalSelectedVariants} variants selected)
                                </Text>
                                
                                <DataTable
                                    columnContentTypes={['text', 'text', 'text']}
                                    headings={['Product', 'Variant', 'Changes']}
                                    rows={previewData.slice(0, 20)} // Show first 20 for preview
                                />

                                {previewData.length > 20 && (
                                    <Text as="p" variant="bodyMd" tone="subdued">
                                        Showing first 20 items. Total: {previewData.length} changes
                                    </Text>
                                )}

                                <Divider />
                                
                                <InlineStack gap="400">
                                    <Badge tone="info">{`${totalSelectedProducts} products`}</Badge>
                                    <Badge tone="info">{`${totalSelectedVariants} variants`}</Badge>
                                    <Badge tone="success">{`${modifications.length} modifications`}</Badge>
                                </InlineStack>
                            </BlockStack>
                        </Card>
                    </Layout.Section>
                </Layout>
            </BlockStack>
        </Page>
    );
}
