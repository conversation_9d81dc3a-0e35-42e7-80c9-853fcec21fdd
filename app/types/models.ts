import { IconSource } from "@shopify/polaris";
import { JobStatus, StatsCardType } from "./enum";

export type Job = {
    id: string;
    name: string;
    executionTime: string;
    status: JobStatus;
    productsAffected: number;
};

export type StatsResponse = {
    totalJobs?: number;
    plan?: string;
    timeSaved?: string;
    productsUpdated?: number;
    error?: string;
}

export type StatsCardProps = {
    data: StatsCard;
    loading: boolean;
    value?: string;
};

export type StatsCard = {
    title: string;
    type: StatsCardType;
    icon: IconSource;
    tooltip?: {
        title: string;
        desc: string;
    }
}

export type ProductType = {
    id: string;
    title: string;
    images: {
        nodes?: {
            id: string;
            url: string;
            altText: string;
        }[]
    };
    status: string;
    totalInventory: number;
    vendor: string;
    category: {
        name: string;
    };
    variants: {
        nodes: {
            id: string;
            title: string;
            sku: string;
            price: string;
            inventoryQuantity: number;
            compareAtPrice: string | null;
        }[],
        pageInfo: PageInfo;
    };
};

export type PageInfo = {
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    startCursor: string | null;
    endCursor: string | null;
};

export type QueryData = {
    searchQuery: string;
}

export type JobModification = {
    id: string;
    fieldType: 'product' | 'variant';
    fieldName: string;
    fieldValue: string;
};

export type JobProductVariant = {
    id: string;
    productId: string;
    variantId?: string;
    status: 'PENDING' | 'SUCCESS' | 'FAILED';
    errorMessage?: string;
    originalValues?: Record<string, any>;
    newValues?: Record<string, any>;
    processedAt?: string;
};

export type JobDetail = {
    id: string;
    title: string;
    description?: string;
    shopId: string;
    status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
    scheduledAt?: string;
    startedAt?: string;
    completedAt?: string;
    failedAt?: string;
    errorMessage?: string;
    createdAt: string;
    updatedAt: string;
    filterCriteria?: Record<string, any>;
    unselectedIds?: string[];
    totalProducts: number;
    processedProducts: number;
    successfulUpdates: number;
    failedUpdates: number;
    modifications: JobModification[];
    productVariants: JobProductVariant[];
};

export type CreateJobData = {
    title: string;
    description?: string;
    scheduledAt?: string;
    modifications: Omit<JobModification, 'id'>[];
    unselectedIds: string[];
    filterCriteria?: Record<string, any>;
};