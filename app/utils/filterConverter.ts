/**
 * Converts advanced FilterValue objects to Shopify GraphQL search syntax
 * 
 * This utility transforms our sophisticated filter builder output into
 * the search strings that Shopify's GraphQL API understands.
 */

import { FilterValue, FilterOperator } from './filterFields';

/**
 * Maps our field IDs to Shopify search field names
 */
const SHOPIFY_FIELD_MAP: Record<string, string> = {
  // Product Fields
  'title': 'title',
  'description': 'body', // Shopify uses 'body' for description
  'vendor': 'vendor',
  'product_type': 'product_type',
  'tags': 'tag',
  'status': 'status',
  'handle': 'handle',
  
  // Variant Fields
  'price': 'price',
  'compare_at_price': 'compare_at_price',
  'cost': 'cost',
  'sku': 'sku',
  'barcode': 'barcode',
  'inventory_quantity': 'inventory_quantity',
  'weight': 'weight',
  
  // Date Fields
  'created_at': 'created_at',
  'updated_at': 'updated_at',
  'published_at': 'published_at',
};

/**
 * Maps our operators to Shopify search operators
 */
const SHOPIFY_OPERATOR_MAP: Record<FilterOperator, string> = {
  'is': '',
  'is_not': '-',
  'contains': '',
  'starts_with': '',
  'ends_with': '',
  'is_blank': '',
  'is_not_blank': '',
  'greater_than': '>',
  'less_than': '<',
  'between': '',
  'is_true': 'true',
  'is_false': 'false',
  'is_in': '',
  'is_not_in': '-',
};

/**
 * Converts a single FilterValue to Shopify search syntax
 */
export function convertFilterToSearchString(filter: FilterValue): string {
  const shopifyField = SHOPIFY_FIELD_MAP[filter.fieldId];
  if (!shopifyField) {
    console.warn(`Unknown field ID: ${filter.fieldId}`);
    return '';
  }

  const operator = SHOPIFY_OPERATOR_MAP[filter.operator];
  const value = Array.isArray(filter.value) ? filter.value[0] : filter.value;

  // Handle special cases
  switch (filter.fieldId) {
    case 'status':
      // Convert our status values to Shopify format
      const statusValue = value.toString().toLowerCase();
      return `status:${statusValue}`;

    case 'tags':
      if (filter.operator === 'contains') {
        return `tag:${value}`;
      }
      return `tag:${operator}${value}`;

    case 'price':
    case 'compare_at_price':
    case 'cost':
    case 'inventory_quantity':
    case 'weight':
      // Handle numeric operators
      switch (filter.operator) {
        case 'is':
          return `${shopifyField}:${value}`;
        case 'is_not':
          return `-${shopifyField}:${value}`;
        case 'greater_than':
          return `${shopifyField}:>${value}`;
        case 'less_than':
          return `${shopifyField}:<${value}`;
        case 'between':
          const secondValue = filter.secondValue || value;
          return `${shopifyField}:>${value} ${shopifyField}:<${secondValue}`;
        case 'is_blank':
          return `-${shopifyField}:*`;
        case 'is_not_blank':
          return `${shopifyField}:*`;
        default:
          return `${shopifyField}:${value}`;
      }

    case 'title':
    case 'description':
    case 'vendor':
    case 'product_type':
    case 'handle':
    case 'sku':
    case 'barcode':
      // Handle string operators
      switch (filter.operator) {
        case 'is':
          return `${shopifyField}:"${value}"`;
        case 'is_not':
          return `-${shopifyField}:"${value}"`;
        case 'contains':
          // Use wildcard syntax for contains searches to be field-specific
          return `${shopifyField}:*${value}*`;
        case 'starts_with':
          return `${shopifyField}:${value}*`;
        case 'ends_with':
          return `${shopifyField}:*${value}`;
        case 'is_blank':
          return `-${shopifyField}:*`;
        case 'is_not_blank':
          return `${shopifyField}:*`;
        default:
          return `${shopifyField}:${value}`;
      }

    case 'requires_shipping':
    case 'taxable':
      // Handle boolean fields
      const boolValue = filter.operator === 'is_true' ? 'true' : 'false';
      return `${shopifyField}:${boolValue}`;

    case 'created_at':
    case 'updated_at':
    case 'published_at':
      // Handle date fields
      switch (filter.operator) {
        case 'is':
          return `${shopifyField}:${value}`;
        case 'greater_than':
          return `${shopifyField}:>${value}`;
        case 'less_than':
          return `${shopifyField}:<${value}`;
        case 'between':
          const endDate = filter.secondValue || value;
          return `${shopifyField}:>${value} ${shopifyField}:<${endDate}`;
        default:
          return `${shopifyField}:${value}`;
      }

    default:
      // Fallback for unknown fields
      return `${shopifyField}:${value}`;
  }
}

/**
 * Handles custom filters that don't map directly to Shopify fields
 */
export function convertCustomFilter(filter: FilterValue): string {
  switch (filter.fieldId) {
    case 'out_of_stock':
      return filter.operator === 'is_true' 
        ? 'inventory_quantity:0' 
        : 'inventory_quantity:>0';

    case 'has_images':
      return filter.operator === 'is_true'
        ? 'image:*'
        : '-image:*';

    case 'price_vs_compare':
      // This is a complex custom filter that would need special handling
      // For now, return empty string as it requires more complex logic
      console.warn('price_vs_compare filter not yet implemented');
      return '';

    case 'has_variants':
      // This would require counting variants, which is complex in search
      console.warn('has_variants filter not yet implemented');
      return '';

    default:
      return '';
  }
}

/**
 * Converts an array of FilterValue objects to a single search string
 * Supports both AND and OR logic
 */
export function convertAdvancedFiltersToSearchString(
  filters: FilterValue[],
  filterLogic: 'AND' | 'OR' = 'AND'
): string {
  const searchTerms: string[] = [];

  for (const filter of filters) {
    // Skip incomplete filters
    if (!filter.fieldId || !filter.operator || (!filter.value && filter.operator !== 'is_blank' && filter.operator !== 'is_not_blank')) {
      continue;
    }

    let searchTerm = '';

    // Handle custom filters
    if (filter.groupId === 'custom_filters') {
      searchTerm = convertCustomFilter(filter);
    } else {
      searchTerm = convertFilterToSearchString(filter);
    }

    if (searchTerm) {
      searchTerms.push(searchTerm);
    }
  }

  if (searchTerms.length === 0) {
    return '';
  }

  if (searchTerms.length === 1) {
    return searchTerms[0];
  }

  // Handle OR logic with proper Shopify syntax
  if (filterLogic === 'OR') {
    // Wrap each term in parentheses and join with OR
    return searchTerms.map(term => `(${term})`).join(' OR ');
  }

  // Default AND logic (space-separated)
  return searchTerms.join(' ');
}

/**
 * Combines search query, simple filters, and advanced filters into one search string
 */
export function buildCompleteSearchQuery(
  searchQuery: string,
  simpleFilters: Array<{ value: string }>,
  advancedFilters: FilterValue[],
  filterLogic: 'AND' | 'OR' = 'AND'
): string {
  const parts: string[] = [];

  // Add search query
  if (searchQuery.trim()) {
    parts.push(searchQuery.trim());
  }

  // Add simple filters
  if (simpleFilters.length > 0) {
    const simpleFilterQuery = simpleFilters.map(f => f.value).join(' ');
    if (simpleFilterQuery) {
      parts.push(simpleFilterQuery);
    }
  }

  // Add advanced filters with specified logic
  const advancedFilterQuery = convertAdvancedFiltersToSearchString(advancedFilters, filterLogic);
  if (advancedFilterQuery) {
    parts.push(advancedFilterQuery);
  }

  // Join all parts with spaces (AND logic)
  return parts.join(' ');
}
