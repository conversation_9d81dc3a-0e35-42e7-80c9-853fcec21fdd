import { SegmentType } from "~/components/PopoverWithSearchableList";

export const generateShortKey = (length = 6): string => {
    const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    const bytes = new Uint8Array(length);
    crypto.getRandomValues(bytes);
    return Array.from(bytes, (b) => charset[b % charset.length]).join("");
}

export const appName = "Bulk Product Editor"

export const modificationFields = [
    // PRODUCT FIELDS
    {
        label: 'Product Fields',
        id: 'product_fields',
        value: '0',
        type: SegmentType.HEADER
    },
    {
        label: 'Title',
        id: 'title',
        value: '1',
        type: SegmentType.ITEM
    },
    {
        label: 'Description',
        id: 'description',
        value: '2',
        type: SegmentType.ITEM
    },
    {
        label: 'Vendor',
        id: 'vendor',
        value: '3',
        type: SegmentType.ITEM
    },
    {
        label: 'Product Type',
        id: 'productType',
        value: '4',
        type: SegmentType.ITEM
    },
    {
        label: 'Tags',
        id: 'tags',
        value: '5',
        type: SegmentType.ITEM
    },
    {
        label: 'Status',
        id: 'status',
        value: '6',
        type: SegmentType.ITEM
    },
    {
        label: 'Category',
        id: 'category',
        value: '23',
        type: SegmentType.ITEM
    },
    {
        label: 'SEO Title',
        id: 'seoTitle',
        value: '7',
        type: SegmentType.ITEM
    },
    {
        label: 'SEO Description',
        id: 'seoDescription',
        value: '8',
        type: SegmentType.ITEM
    },
    {
        label: 'URL Handle',
        id: 'handle',
        value: '9',
        type: SegmentType.ITEM
    },

    // VARIANT FIELDS
    {
        label: 'Variant Fields',
        id: 'variant_fields',
        value: '10',
        type: SegmentType.HEADER
    },
    {
        label: 'Price',
        id: 'price',
        value: '11',
        type: SegmentType.ITEM
    },
    {
        label: 'Compare At Price',
        id: 'compareAtPrice',
        value: '12',
        type: SegmentType.ITEM
    },
    {
        label: 'Cost Per Item',
        id: 'cost',
        value: '13',
        type: SegmentType.ITEM
    },
    {
        label: 'SKU',
        id: 'sku',
        value: '14',
        type: SegmentType.ITEM
    },
    {
        label: 'Barcode',
        id: 'barcode',
        value: '15',
        type: SegmentType.ITEM
    },
    {
        label: 'Weight',
        id: 'weight',
        value: '16',
        type: SegmentType.ITEM
    },
    {
        label: 'Inventory Quantity',
        id: 'inventoryQuantity',
        value: '17',
        type: SegmentType.ITEM
    },
    {
        label: 'Track Inventory',
        id: 'tracked',
        value: '18',
        type: SegmentType.ITEM
    },
    {
        label: 'Continue Selling When Out of Stock',
        id: 'inventoryPolicy',
        value: '19',
        type: SegmentType.ITEM
    },
    {
        label: 'Requires Shipping',
        id: 'requiresShipping',
        value: '20',
        type: SegmentType.ITEM
    },
    {
        label: 'Taxable',
        id: 'taxable',
        value: '21',
        type: SegmentType.ITEM
    },
]

// Add a function to extract the numeric ID from the full Shopify ID
export function extractIdFromGid(gid: string): string {
    const parts = gid.split('/');
    return parts[parts.length - 1];
}

export const getStoreHandle = () => {
    // Check if we're in the browser (not during SSR)
    if (typeof window === 'undefined') {
        return undefined; // Return undefined during SSR
    }


    const match = window.location.pathname.match(/^\/store\/([^/]+)/);
    return match?.[1]; // e.g. 'ranjan-client'
};