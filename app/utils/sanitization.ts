/**
 * Text sanitization utilities for security and safety
 */

/**
 * Sanitizes text for safe display in UI components
 * Removes potentially dangerous HTML characters and control characters
 * 
 * @param text - The text to sanitize
 * @returns Sanitized text safe for display
 */
export function sanitizeText(text: string): string {
  return text.replace(/[<>&"'\x00-\x1f\x7f-\x9f]/g, '')
}

/**
 * Sanitizes text for use in HTML attributes
 * More restrictive than general text sanitization
 * 
 * @param text - The text to sanitize for attributes
 * @returns Sanitized text safe for HTML attributes
 */
export function sanitizeAttribute(text: string): string {
  return text.replace(/[<>&"'\x00-\x1f\x7f-\x9f\s]/g, '')
}

/**
 * Sanitizes text for use in URLs
 * Removes characters that could break URL structure
 * 
 * @param text - The text to sanitize for URLs
 * @returns Sanitized text safe for URLs
 */
export function sanitizeUrl(text: string): string {
  return text.replace(/[<>&"'\x00-\x1f\x7f-\x9f\s#?]/g, '')
}
