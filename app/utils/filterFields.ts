/**
 * Advanced Filter System - Inspired by Ablestar's 3-stage filtering
 * 
 * This provides a sophisticated filtering system with:
 * 1. Field Groups (Product Fields, Variant Fields, etc.)
 * 2. Specific Fields within each group
 * 3. Operators based on data type
 * 4. Value inputs appropriate for each field type
 */

export type FilterDataType = 
  | 'string' 
  | 'number' 
  | 'boolean' 
  | 'category' 
  | 'collection' 
  | 'date' 
  | 'status' 
  | 'tag'
  | 'custom';

export type FilterOperator = 
  | 'is' 
  | 'is_not' 
  | 'contains' 
  | 'starts_with' 
  | 'ends_with' 
  | 'is_blank' 
  | 'is_not_blank'
  | 'greater_than' 
  | 'less_than' 
  | 'between'
  | 'is_true' 
  | 'is_false'
  | 'is_in' 
  | 'is_not_in';

export interface FilterField {
  id: string;
  name: string;
  dataType: FilterDataType;
  group: string;
  operators: FilterOperator[];
  placeholder?: string;
  options?: Array<{ label: string; value: string }>;
}

export interface FilterGroup {
  id: string;
  name: string;
  fields: FilterField[];
}

// Operator definitions with labels
export const FILTER_OPERATORS: Record<FilterOperator, string> = {
  is: 'is',
  is_not: 'is not',
  contains: 'contains',
  starts_with: 'starts with',
  ends_with: 'ends with',
  is_blank: 'is blank',
  is_not_blank: 'is not blank',
  greater_than: 'greater than',
  less_than: 'less than',
  between: 'between',
  is_true: 'is true',
  is_false: 'is false',
  is_in: 'is in',
  is_not_in: 'is not in',
};

// Common operator sets for different data types
const STRING_OPERATORS: FilterOperator[] = ['is', 'is_not', 'contains', 'starts_with', 'ends_with', 'is_blank', 'is_not_blank'];
const NUMBER_OPERATORS: FilterOperator[] = ['is', 'is_not', 'greater_than', 'less_than', 'between', 'is_blank', 'is_not_blank'];
const BOOLEAN_OPERATORS: FilterOperator[] = ['is_true', 'is_false'];
const COLLECTION_OPERATORS: FilterOperator[] = ['is_in', 'is_not_in'];
const STATUS_OPERATORS: FilterOperator[] = ['is', 'is_not'];

// Status options
const PRODUCT_STATUS_OPTIONS = [
  { label: 'Active', value: 'ACTIVE' },
  { label: 'Draft', value: 'DRAFT' },
  { label: 'Archived', value: 'ARCHIVED' },
];

// Filter Groups and Fields
export const FILTER_GROUPS: FilterGroup[] = [
  {
    id: 'product_fields',
    name: 'Product Fields',
    fields: [
      {
        id: 'title',
        name: 'Title',
        dataType: 'string',
        group: 'product_fields',
        operators: STRING_OPERATORS,
        placeholder: 'e.g., Nike Air Max',
      },
      {
        id: 'description',
        name: 'Description',
        dataType: 'string',
        group: 'product_fields',
        operators: STRING_OPERATORS,
        placeholder: 'e.g., comfortable running shoes',
      },
      {
        id: 'vendor',
        name: 'Vendor',
        dataType: 'string',
        group: 'product_fields',
        operators: STRING_OPERATORS,
        placeholder: 'e.g., Nike, Adidas',
      },
      {
        id: 'product_type',
        name: 'Product Type',
        dataType: 'string',
        group: 'product_fields',
        operators: STRING_OPERATORS,
        placeholder: 'e.g., Shoes, T-Shirts',
      },
      {
        id: 'tags',
        name: 'Tags',
        dataType: 'tag',
        group: 'product_fields',
        operators: ['contains', 'is', 'is_not'],
        placeholder: 'e.g., summer, sale',
      },
      {
        id: 'status',
        name: 'Status',
        dataType: 'status',
        group: 'product_fields',
        operators: STATUS_OPERATORS,
        options: PRODUCT_STATUS_OPTIONS,
      },
      {
        id: 'handle',
        name: 'URL Handle',
        dataType: 'string',
        group: 'product_fields',
        operators: STRING_OPERATORS,
        placeholder: 'e.g., nike-air-max',
      },
    ],
  },
  {
    id: 'variant_fields',
    name: 'Variant Fields',
    fields: [
      {
        id: 'price',
        name: 'Price',
        dataType: 'number',
        group: 'variant_fields',
        operators: NUMBER_OPERATORS,
        placeholder: 'e.g., 29.99',
      },
      {
        id: 'compare_at_price',
        name: 'Compare-at Price',
        dataType: 'number',
        group: 'variant_fields',
        operators: NUMBER_OPERATORS,
        placeholder: 'e.g., 39.99',
      },
      {
        id: 'cost',
        name: 'Cost Per Item',
        dataType: 'number',
        group: 'variant_fields',
        operators: NUMBER_OPERATORS,
        placeholder: 'e.g., 15.00',
      },
      {
        id: 'sku',
        name: 'SKU',
        dataType: 'string',
        group: 'variant_fields',
        operators: STRING_OPERATORS,
        placeholder: 'e.g., NIKE-001',
      },
      {
        id: 'barcode',
        name: 'Barcode',
        dataType: 'string',
        group: 'variant_fields',
        operators: STRING_OPERATORS,
        placeholder: 'e.g., 123456789',
      },
      {
        id: 'inventory_quantity',
        name: 'Inventory Quantity',
        dataType: 'number',
        group: 'variant_fields',
        operators: NUMBER_OPERATORS,
        placeholder: 'e.g., 100',
      },
      {
        id: 'weight',
        name: 'Weight',
        dataType: 'number',
        group: 'variant_fields',
        operators: NUMBER_OPERATORS,
        placeholder: 'e.g., 1.5',
      },
      {
        id: 'requires_shipping',
        name: 'Requires Shipping',
        dataType: 'boolean',
        group: 'variant_fields',
        operators: BOOLEAN_OPERATORS,
      },
      {
        id: 'taxable',
        name: 'Taxable',
        dataType: 'boolean',
        group: 'variant_fields',
        operators: BOOLEAN_OPERATORS,
      },
    ],
  },
  {
    id: 'custom_filters',
    name: 'Custom Filters',
    fields: [
      {
        id: 'out_of_stock',
        name: 'Product is Out of Stock',
        dataType: 'boolean',
        group: 'custom_filters',
        operators: BOOLEAN_OPERATORS,
      },
      {
        id: 'has_images',
        name: 'Has Images',
        dataType: 'boolean',
        group: 'custom_filters',
        operators: BOOLEAN_OPERATORS,
      },
      {
        id: 'price_vs_compare',
        name: 'Price vs Compare-at Price',
        dataType: 'custom',
        group: 'custom_filters',
        operators: ['is'],
        options: [
          { label: 'Price < Compare-at Price', value: 'price_less_than_compare' },
          { label: 'Price = Compare-at Price', value: 'price_equals_compare' },
          { label: 'Price > Compare-at Price', value: 'price_greater_than_compare' },
        ],
      },
      {
        id: 'has_variants',
        name: 'Has Multiple Variants',
        dataType: 'boolean',
        group: 'custom_filters',
        operators: BOOLEAN_OPERATORS,
      },
    ],
  },
  {
    id: 'date_fields',
    name: 'Date Fields',
    fields: [
      {
        id: 'created_at',
        name: 'Date Created',
        dataType: 'date',
        group: 'date_fields',
        operators: ['is', 'is_not', 'greater_than', 'less_than', 'between'],
      },
      {
        id: 'updated_at',
        name: 'Date Updated',
        dataType: 'date',
        group: 'date_fields',
        operators: ['is', 'is_not', 'greater_than', 'less_than', 'between'],
      },
      {
        id: 'published_at',
        name: 'Date Published',
        dataType: 'date',
        group: 'date_fields',
        operators: ['is', 'is_not', 'greater_than', 'less_than', 'between'],
      },
    ],
  },
];

// Helper functions
export function getFilterGroupById(groupId: string): FilterGroup | undefined {
  return FILTER_GROUPS.find(group => group.id === groupId);
}

export function getFilterFieldById(fieldId: string): FilterField | undefined {
  for (const group of FILTER_GROUPS) {
    const field = group.fields.find(f => f.id === fieldId);
    if (field) return field;
  }
  return undefined;
}

export function getAllFilterFields(): FilterField[] {
  return FILTER_GROUPS.flatMap(group => group.fields);
}

// Filter value interface
export interface FilterValue {
  groupId: string;
  fieldId: string;
  operator: FilterOperator;
  value: string | string[];
  secondValue?: string; // For 'between' operator
}
