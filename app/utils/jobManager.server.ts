import { authenticate } from "~/shopify.server";
import db from "~/db.server";
import { CreateJobData, JobDetail } from "~/types/models";
import { enqueueBulkUpdateJob } from "~/services/jobQueue.server";

export async function createJob(request: Request, jobData: CreateJobData): Promise<string> {
  const { session } = await authenticate.admin(request);
  
  // Create the job
  const job = await db.job.create({
    data: {
      title: jobData.title,
      description: jobData.description,
      shopId: session.shop,
      status: jobData.scheduledAt ? 'SCHEDULED' : 'IN_PROGRESS',
      scheduledAt: jobData.scheduledAt ? new Date(jobData.scheduledAt) : null,
      filterCriteria: jobData.filterCriteria ? JSON.stringify(jobData.filterCriteria) : null,
      unselectedIds: JSON.stringify(jobData.unselectedIds),
      totalProducts: 0, // Will be calculated when processing starts
      processedProducts: 0,
      successfulUpdates: 0,
      failedUpdates: 0,
    },
  });

  // Create job modifications
  if (jobData.modifications.length > 0) {
    await db.jobModification.createMany({
      data: jobData.modifications.map(mod => ({
        jobId: job.id,
        fieldType: mod.fieldType,
        fieldName: mod.fieldName,
        fieldValue: mod.fieldValue,
      })),
    });
  }

  // If job is not scheduled, enqueue it immediately for processing
  if (!jobData.scheduledAt) {
    try {
      // Prepare job data for Bull queue
      const bulkUpdateJobData = {
        jobId: job.id,
        shopDomain: session.shop,
        modifications: jobData.modifications.map(mod => ({
          fieldType: mod.fieldType,
          fieldName: mod.fieldName,
          fieldValue: mod.fieldValue,
        })),
        productIds: [], // Will be populated when job starts processing
        options: {
          batchSize: 10, // Default batch size
          delayBetweenBatches: 1000, // Default delay
        },
      };

      // Enqueue the job to Bull queue
      await enqueueBulkUpdateJob(bulkUpdateJobData);

      console.log(`✅ Job ${job.id} enqueued for shop ${session.shop}`);
    } catch (error) {
      console.error(`❌ Failed to enqueue job ${job.id}:`, error);

      // Update job status to FAILED if enqueueing fails
      await db.job.update({
        where: { id: job.id },
        data: { status: 'FAILED' },
      });

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Failed to enqueue job: ${errorMessage}`);
    }
  }

  return job.id;
}

export async function getJobById(jobId: string, shopId: string): Promise<JobDetail | null> {
  const job = await db.job.findFirst({
    where: {
      id: jobId,
      shopId: shopId,
    },
    include: {
      modifications: true,
      productVariants: true,
    },
  });

  if (!job) return null;

  return {
    id: job.id,
    title: job.title,
    description: job.description || undefined,
    shopId: job.shopId,
    status: job.status as any,
    scheduledAt: job.scheduledAt?.toISOString(),
    startedAt: job.startedAt?.toISOString(),
    completedAt: job.completedAt?.toISOString(),
    createdAt: job.createdAt.toISOString(),
    updatedAt: job.updatedAt.toISOString(),
    filterCriteria: job.filterCriteria ? JSON.parse(job.filterCriteria) : undefined,
    unselectedIds: job.unselectedIds ? JSON.parse(job.unselectedIds) : [],
    totalProducts: job.totalProducts,
    processedProducts: job.processedProducts,
    successfulUpdates: job.successfulUpdates,
    failedUpdates: job.failedUpdates,
    modifications: job.modifications.map(mod => ({
      id: mod.id,
      fieldType: mod.fieldType as 'product' | 'variant',
      fieldName: mod.fieldName,
      fieldValue: mod.fieldValue,
    })),
    productVariants: job.productVariants.map(pv => ({
      id: pv.id,
      productId: pv.productId,
      variantId: pv.variantId || undefined,
      status: pv.status as 'PENDING' | 'SUCCESS' | 'FAILED',
      errorMessage: pv.errorMessage || undefined,
      originalValues: pv.originalValues ? JSON.parse(pv.originalValues) : undefined,
      newValues: pv.newValues ? JSON.parse(pv.newValues) : undefined,
      processedAt: pv.processedAt?.toISOString(),
    })),
  };
}

export async function getJobsByShop(shopId: string, limit: number = 10): Promise<JobDetail[]> {
  const jobs = await db.job.findMany({
    where: { shopId },
    include: {
      modifications: true,
      productVariants: true,
    },
    orderBy: { createdAt: 'desc' },
    take: limit,
  });

  return jobs.map(job => ({
    id: job.id,
    title: job.title,
    description: job.description || undefined,
    shopId: job.shopId,
    status: job.status as any,
    scheduledAt: job.scheduledAt?.toISOString(),
    startedAt: job.startedAt?.toISOString(),
    completedAt: job.completedAt?.toISOString(),
    createdAt: job.createdAt.toISOString(),
    updatedAt: job.updatedAt.toISOString(),
    filterCriteria: job.filterCriteria ? JSON.parse(job.filterCriteria) : undefined,
    unselectedIds: job.unselectedIds ? JSON.parse(job.unselectedIds) : [],
    totalProducts: job.totalProducts,
    processedProducts: job.processedProducts,
    successfulUpdates: job.successfulUpdates,
    failedUpdates: job.failedUpdates,
    modifications: job.modifications.map(mod => ({
      id: mod.id,
      fieldType: mod.fieldType as 'product' | 'variant',
      fieldName: mod.fieldName,
      fieldValue: mod.fieldValue,
    })),
    productVariants: job.productVariants.map(pv => ({
      id: pv.id,
      productId: pv.productId,
      variantId: pv.variantId || undefined,
      status: pv.status as 'PENDING' | 'SUCCESS' | 'FAILED',
      errorMessage: pv.errorMessage || undefined,
      originalValues: pv.originalValues ? JSON.parse(pv.originalValues) : undefined,
      newValues: pv.newValues ? JSON.parse(pv.newValues) : undefined,
      processedAt: pv.processedAt?.toISOString(),
    })),
  }));
}

export async function updateJobStatus(
  jobId: string, 
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'CANCELLED',
  shopId: string
): Promise<void> {
  const updateData: any = { status };
  
  if (status === 'IN_PROGRESS') {
    updateData.startedAt = new Date();
  } else if (status === 'COMPLETED' || status === 'FAILED') {
    updateData.completedAt = new Date();
  }

  await db.job.updateMany({
    where: { id: jobId, shopId },
    data: updateData,
  });
}

export async function storeModificationsData(
  request: Request, 
  modifications: any[], 
  unselectedIds: string[], 
  sessionKey?: string
): Promise<string> {
  const { session } = await authenticate.admin(request);
  const newSessionKey = sessionKey || `modifications_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  
  // Store modifications and unselected IDs together
  const data = {
    modifications,
    unselectedIds,
  };
  
  // Clean up any existing entry for this session key
  if (sessionKey) {
    await db.tempSelection.deleteMany({
      where: { sessionKey },
    });
  }
  
  await db.tempSelection.create({
    data: {
      sessionKey: newSessionKey,
      shopId: session.shop,
      data: JSON.stringify(data),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    },
  });
  
  return newSessionKey;
}

export async function getModificationsData(sessionKey: string): Promise<{
  modifications: any[];
  unselectedIds: string[];
} | null> {
  if (!sessionKey) return null;
  
  try {
    const selection = await db.tempSelection.findUnique({
      where: { 
        sessionKey,
        expiresAt: { gt: new Date() }
      },
    });
    
    if (selection) {
      return JSON.parse(selection.data);
    }
  } catch (error) {
    console.error('Error retrieving modifications data:', error);
  }
  
  return null;
}
