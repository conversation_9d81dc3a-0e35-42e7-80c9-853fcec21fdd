import { authenticate } from "~/shopify.server";
import db from "~/db.server";

// Generate a unique session key
function generateSessionKey(): string {
  return `selection_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}

// Store unselected IDs in database with session key
export async function storeUnselectedIds(request: Request, unselectedIds: string[]): Promise<string> {
  const { session } = await authenticate.admin(request);
  const sessionKey = generateSessionKey();
  
  // Clean up any expired entries for this shop first
  await cleanupExpiredSelections(session.shop);
  
  // Store in database
  await db.tempSelection.create({
    data: {
      sessionKey,
      shopId: session.shop,
      data: JSON.stringify(unselectedIds),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    },
  });
  
  return sessionKey;
}

// Retrieve unselected IDs from database using session key
export async function getUnselectedIds(sessionKey: string): Promise<string[]> {
  if (!sessionKey) return [];
  
  try {
    const selection = await db.tempSelection.findUnique({
      where: { 
        sessionKey,
        expiresAt: { gt: new Date() } // Only get non-expired entries
      },
    });
    
    if (selection) {
      return JSON.parse(selection.data);
    }
  } catch (error) {
    console.error('Error retrieving unselected IDs:', error);
  }
  
  return [];
}

// Clean up a specific selection
export async function clearSelection(sessionKey: string) {
  if (!sessionKey) return;

  try {
    // Use deleteMany instead of delete to avoid errors when record doesn't exist
    await db.tempSelection.deleteMany({
      where: { sessionKey },
    });
  } catch (error) {
    console.error('Error clearing selection:', error);
  }
}

// Clean up expired entries for a shop
export async function cleanupExpiredSelections(shopId: string) {
  try {
    await db.tempSelection.deleteMany({
      where: {
        OR: [
          { expiresAt: { lt: new Date() } }, // Expired entries
          { shopId, createdAt: { lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } } // Entries older than 7 days for this shop
        ]
      },
    });
  } catch (error) {
    console.error('Error cleaning up expired selections:', error);
  }
}
