import { createContext, useContext, useState, useCallback, useRef, useEffect, useMemo, ReactNode } from 'react'
import { Toast } from '@shopify/polaris'

export interface ToastMessage {
  id: string
  content: string
  error?: boolean
  duration?: number
  action?: {
    content: string
    onAction: () => void
  }
}

interface ToastContextType {
  showToast: (message: Omit<ToastMessage, 'id'>) => void
  showSuccess: (content: string, duration?: number) => void
  showError: (content: string, duration?: number) => void
  showWarning: (content: string, duration?: number) => void
  dismissToast: (id: string) => void
  dismissAll: () => void
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

export function useToast() {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context
}

interface ToastProviderProps {
  children: ReactNode
}

export function ToastProvider({ children }: ToastProviderProps) {
  const [toasts, setToasts] = useState<ToastMessage[]>([])
  const timeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map())

  const dismissToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
    // Clear timeout if it exists
    const timeout = timeoutsRef.current.get(id)
    if (timeout) {
      clearTimeout(timeout)
      timeoutsRef.current.delete(id)
    }
  }, [])

  const showToast = useCallback((message: Omit<ToastMessage, 'id'>) => {
    const id = `toast-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
    const newToast: ToastMessage = {
      id,
      duration: 5000, // Default 5 seconds
      ...message,
    }

    setToasts(prev => [...prev, newToast])

    // Auto-dismiss after duration
    if (newToast.duration && newToast.duration > 0) {
      const timeout = setTimeout(() => {
        dismissToast(id)
      }, newToast.duration)
      timeoutsRef.current.set(id, timeout)
    }
  }, [dismissToast])

  const showSuccess = useCallback((content: string, duration = 5000) => {
    showToast({ content, error: false, duration })
  }, [showToast])

  const showError = useCallback((content: string, duration = 8000) => {
    showToast({ content, error: true, duration })
  }, [showToast])

  const showWarning = useCallback((content: string, duration = 6000) => {
    showToast({ content, error: false, duration })
  }, [showToast])

  const dismissAll = useCallback(() => {
    setToasts([])
    // Clear all timeouts
    timeoutsRef.current.forEach(timeout => clearTimeout(timeout))
    timeoutsRef.current.clear()
  }, [])

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      timeoutsRef.current.forEach(timeout => clearTimeout(timeout))
      timeoutsRef.current.clear()
    }
  }, [])

  const contextValue: ToastContextType = {
    showToast,
    showSuccess,
    showError,
    showWarning,
    dismissToast,
    dismissAll,
  }

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      
      {/* Render toasts */}
      {toasts.map(toast => (
        <Toast
          key={toast.id}
          content={toast.content}
          error={toast.error}
          onDismiss={() => dismissToast(toast.id)}
          action={toast.action}
        />
      ))}
    </ToastContext.Provider>
  )
}

/**
 * Hook for job-specific toast messages
 */
export function useJobToast() {
  const { showSuccess, showError, showWarning } = useToast()

  return useMemo(() => ({
    showJobStarted: (jobTitle: string) => {
      showSuccess(`Job "${jobTitle}" started successfully`)
    },

    showJobStopped: (jobTitle: string) => {
      showWarning(`Job "${jobTitle}" has been stopped`)
    },

    showJobCompleted: (jobTitle: string) => {
      showSuccess(`Job "${jobTitle}" completed successfully`)
    },

    showGenericSuccess: (message: string) => {
      showSuccess(message)
    },

    showJobFailed: (jobTitle: string, error?: string) => {
      const message = error
        ? `Job "${jobTitle}" failed: ${error}`
        : `Job "${jobTitle}" failed to complete`
      showError(message)
    },

    showJobError: (action: string, error: string) => {
      showError(`Failed to ${action}: ${error}`)
    },

    showNetworkError: () => {
      showError('Network error. Please check your connection and try again.')
    },

    showUnexpectedError: () => {
      showError('An unexpected error occurred. Please try again.')
    },
  }), [showSuccess, showError, showWarning])
}

/**
 * Utility function to extract error message from various error types
 */
export function getErrorMessage(error: unknown): string {
  if (typeof error === 'string') {
    return error
  }
  
  if (error instanceof Error) {
    return error.message
  }
  
  if (error && typeof error === 'object' && 'message' in error) {
    return String((error as any).message)
  }
  
  return 'An unknown error occurred'
}

/**
 * Utility function to determine if an error is a network error
 */
export function isNetworkError(error: unknown): boolean {
  if (error instanceof Error) {
    return error.message.toLowerCase().includes('network') ||
           error.message.toLowerCase().includes('fetch') ||
           error.message.toLowerCase().includes('connection')
  }
  
  return false
}
