// utils/getProducts.ts or top of your route file

// Function to get the total count of products matching the query
export async function getProductsCountFromShopify({
    admin,
    searchQuery = '',
    optionName,
    optionValue,
}: {
    admin: any;
    searchQuery?: string;
    optionName?: string;
    optionValue?: string;
}) {
    // Input validation
    if (!admin) {
        throw new Error('Admin GraphQL client is required');
    }

    // Sanitize and validate inputs
    const sanitizedQuery = (searchQuery || '').slice(0, 1000); // Limit query length

    let query = sanitizedQuery;
    if (optionName && optionValue) {
        const sanitizedOptionName = optionName.slice(0, 100);
        const sanitizedOptionValue = optionValue.slice(0, 100);
        query = `${query} option:${sanitizedOptionName}:${sanitizedOptionValue}`.trim();
    }

    try {
        const response = await admin.graphql(
            `query getProductsCount($query: String) {
                productsCount(query: $query) {
                    count
                    precision
                }
            }`,
            {
                variables: {
                    query: query || null,
                },
            }
        );

        const json = await response.json();

        if (json.errors) {
            console.error('GraphQL errors in productsCount:', json.errors);
            throw new Error(`GraphQL Error: ${json.errors[0]?.message || 'Unknown error'}`);
        }

        return json.data.productsCount;
    } catch (error) {
        console.error('Error fetching products count:', error);
        if (error instanceof Error) {
            if (error.message.includes('fetch failed') || error.message.includes('no response available')) {
                throw new Error('Unable to connect to Shopify. Please check your internet connection and try again.');
            }
            if (error.message.includes('GraphQL Error')) {
                throw error; // Re-throw GraphQL errors as-is
            }
            throw new Error(`Failed to load products count: ${error.message}`);
        }
        throw new Error('Failed to load products count. Please try again.');
    }
}

// Configuration for variant display
export const VARIANT_DISPLAY_CONFIG = {
    // Number of variants to show initially per product
    initialVariantsToShow: 6,
    // Maximum variants to fetch per product (for performance)
    maxVariantsToFetch: 50,
} as const;

export async function getProductsFromShopify({
    admin,
    searchQuery = '',
    optionName,
    optionValue,
    cursor = null,
    pageSize = 10
}: {
    admin: any;
    searchQuery?: string;
    optionName?: string;
    optionValue?: string;
    cursor?: string | null;
    pageSize?: number;
}) {
    // Input validation
    if (!admin) {
        throw new Error('Admin GraphQL client is required');
    }

    // Sanitize and validate inputs
    const sanitizedPageSize = Math.min(Math.max(pageSize || 10, 1), 250); // Limit page size
    const sanitizedQuery = (searchQuery || '').slice(0, 1000); // Limit query length

    let query = sanitizedQuery;
    if (optionName && optionValue) {
        const sanitizedOptionName = optionName.slice(0, 100);
        const sanitizedOptionValue = optionValue.slice(0, 100);
        query = `${query} option:${sanitizedOptionName}:${sanitizedOptionValue}`.trim();
    }

    // console.log('🔍 GraphQL query being sent to Shopify:', { originalSearchQuery: searchQuery, sanitizedQuery, finalQuery: query });

    const maxRetries = 3;
    const baseDelay = 500; // 500ms base delay
    const maxDelay = 2000; // Maximum 2 second delay

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            // Add timeout protection for the GraphQL request
            const timeoutMs = 10000; // 10 second timeout
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Request timeout')), timeoutMs);
            });

            const graphqlPromise = admin.graphql(
                `query getProducts($query: String, $first: Int!, $after: String) {
                products(first: $first, after: $after, query: $query) {
                  pageInfo {
                    hasNextPage
                    hasPreviousPage
                    startCursor
                    endCursor
                  }
                  nodes {
                    id
                    title
                    status
                    totalInventory
                    vendor
                    category {
                      name
                    }
                    images(first: 1) {
                      edges {
                        node {
                          id
                          url
                          altText
                        }
                      }
                    }
                    variants(first: 50) {
                      nodes {
                        id
                        title
                        sku
                        price
                        inventoryQuantity
                        selectedOptions {
                          name
                          value
                        }
                      }
                      pageInfo {
                        hasNextPage
                        hasPreviousPage
                        startCursor
                        endCursor
                      }
                    }
                  }
                }
              }`,
                {
                    variables: {
                        after: cursor || null,
                        query,
                        first: sanitizedPageSize,
                    },
                }
            );

            const response = await Promise.race([graphqlPromise, timeoutPromise]);
            const json = await response.json();
            if (json.errors && Array.isArray(json.errors)) {
                const errorMessages = json.errors
                    .filter((e: any) => e && typeof e.message === 'string')
                    .map((e: any) => e.message)
                    .slice(0, 3); // Limit to first 3 errors
                throw new Error(`GraphQL Error: ${errorMessages.join(', ')}`);
            }



            return json.data.products;
        } catch (error) {
            console.error(`Product fetch attempt ${attempt}/${maxRetries} failed:`, error);

            // If this is the last attempt, throw a user-friendly error
            if (attempt === maxRetries) {
                if (error instanceof Error) {
                    if (error.message.includes('fetch failed') || error.message.includes('no response available')) {
                        throw new Error('Unable to connect to Shopify. Please check your internet connection and try again.');
                    }
                    if (error.message.includes('GraphQL Error')) {
                        throw error; // Re-throw GraphQL errors as-is
                    }
                    throw new Error(`Failed to load products: ${error.message}`);
                }
                throw new Error('Failed to load products. Please try again.');
            }

            // Wait before retrying (exponential backoff with max limit)
            const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
            await new Promise((resolve) => {
                setTimeout(() => {
                    resolve(undefined);
                }, delay);
            });
        }
    }

    // This should never be reached, but TypeScript requires it
    throw new Error('Failed to load products after all retry attempts.');
}