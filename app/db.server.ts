import { PrismaClient } from "@prisma/client";

declare global {
  var prismaGlobal: PrismaClient;
}

// Initialize global client in non-production environments
if (process.env.NODE_ENV !== "production") {
  if (!global.prismaGlobal) {
    global.prismaGlobal = new PrismaClient();
  }
}

/**
 * PROXY PATTERN FOR TEST COMPATIBILITY
 *
 * Why we use a Proxy instead of direct PrismaClient:
 * 1. **Test Flexibility**: Tests can override `global.prismaGlobal` and have it picked up immediately
 * 2. **Singleton Pattern**: Maintains singleton behavior in development while allowing test isolation
 * 3. **Function Caching**: Ensures consistent function references for test assertions
 *
 * The Proxy intercepts all property access and delegates to the current global client.
 * This allows tests to swap out the database client without module reloading.
 *
 * 📚 **See**: docs/current-status-and-next-steps.md - "Database Testing Strategy" section
 * 🧪 **Example**: tests/dbServer.regression.test.ts - behavior testing patterns
 */

// Cache for bound functions to ensure reference equality in tests
// This prevents test failures when comparing function references
const functionCache = new Map<string, { client: PrismaClient; boundFunction: Function }>();

/**
 * Database client proxy that dynamically resolves to the current global client.
 *
 * IMPORTANT: This is NOT a direct PrismaClient instance - it's a Proxy that:
 * - Delegates all calls to `global.prismaGlobal ?? new PrismaClient()`
 * - Caches bound functions to ensure consistent references for testing
 * - Allows tests to override the global client dynamically
 *
 * For production use, this behaves exactly like a normal PrismaClient.
 * For testing, this allows dynamic client swapping without module reloading.
 */
const prisma = new Proxy({} as PrismaClient, {
  get(_target, prop) {
    // Always use the current global client, or create a new one
    const client = global.prismaGlobal ?? new PrismaClient();
    const value = client[prop as keyof PrismaClient];

    // For functions, return cached bound functions to ensure reference equality
    // This is crucial for tests that expect the same function reference on repeated access
    if (typeof value === 'function') {
      const cacheKey = String(prop);
      const cached = functionCache.get(cacheKey);

      // Return cached function if it's for the same client instance
      if (cached && cached.client === client) {
        return cached.boundFunction;
      }

      // Create new bound function and cache it for future calls
      const boundFunction = value.bind(client);
      functionCache.set(cacheKey, { client, boundFunction });
      return boundFunction;
    }

    // For non-function properties, return directly
    return value;
  }
});

export default prisma;
