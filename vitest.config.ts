import { defineConfig } from 'vitest/config'
import { resolve } from 'path'

const testMode = process.env.TEST_MODE || 'essential'

const getTestConfig = () => {
  const baseConfig = {
    globals: true,
    environment: 'node',
    setupFiles: ['./tests/setup.ts'],
    alias: {
      '~': resolve(__dirname, './app'),
    },
    isolate: true,
    testTimeout: 30000, // 30 second timeout for slow database tests
    deps: {
      moduleDirectories: ['node_modules', 'tests/__mocks__'],
    },
    server: {
      deps: {
        inline: ['react']
      }
    },
    env: {
      NODE_ENV: 'test' as const,
      PORT: '3001',
    },
    coverage: {
      provider: 'v8' as const,
      reporter: ['text', 'html', 'json'],
      reportsDirectory: './coverage',
      include: ['app/**/*.ts', 'app/**/*.tsx', 'workers/**/*.ts'],
      exclude: [
        'app/**/*.test.ts',
        'app/**/*.spec.ts',
        'app/entry.client.tsx',
        'app/entry.server.tsx',
        'app/root.tsx',
        'workers/**/*.test.ts',
        'workers/**/*.spec.ts',
        'node_modules/**',
        'tests/**',
        'build/**',
        'public/**'
      ]
    },
  }

  switch (testMode) {
    case 'all':
      return {
        ...baseConfig,
        include: ['tests/**/*.test.ts'],
        exclude: [],
        pool: 'forks', // Force sequential execution to avoid database conflicts
        poolOptions: {
          forks: {
            singleFork: true
          }
        }
      }
    case 'fast':
      return {
        ...baseConfig,
        include: ['tests/**/*.fast.test.ts'],
        exclude: []
      }
    case 'demos':
      return {
        ...baseConfig,
        include: ['tests/**/*.demo.test.ts'],
        exclude: []
      }
    case 'regression':
      return {
        ...baseConfig,
        include: ['tests/**/*.regression.test.ts'],
        exclude: [],
        pool: 'forks', // Force sequential execution for regression tests
        poolOptions: {
          forks: {
            singleFork: true
          }
        }
      }
    case 'essential':
    default:
      return {
        ...baseConfig,
        include: ['tests/*.test.ts'],
        exclude: ['tests/**/*.demo.test.ts', 'tests/**/*.fast.test.ts', 'tests/**/*.regression.test.ts', 'tests/demos/**']
      }
  }
}

export default defineConfig({
  test: {
    ...getTestConfig(),
    server: {
      deps: {
        inline: ['react']
      }
    }
  },
  define: {
    global: 'globalThis',
  },
})
