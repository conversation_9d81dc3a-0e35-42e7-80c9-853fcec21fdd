version: '3.8'

services:
  # Redis for job queues
  redis:
    image: redis:7-alpine
    container_name: bpe-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Redis Commander for queue visualization
  redis-commander:
    image: rediscommander/redis-commander:latest
    platform: linux/amd64
    container_name: bpe-redis-commander
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
      - HTTP_USER=
      - HTTP_PASSWORD=
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped

volumes:
  redis_data:
    driver: local
