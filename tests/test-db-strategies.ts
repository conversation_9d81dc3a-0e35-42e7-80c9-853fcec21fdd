import { PrismaClient } from '@prisma/client'
import { randomBytes } from 'crypto'
import { execSync } from 'child_process'
import { readFileSync, writeFileSync, unlinkSync, mkdirSync, rmSync } from 'fs'
import { join, resolve } from 'path'

// Test database directory (absolute path to avoid working directory issues)
const TEST_DB_DIR = resolve('./prisma/test-dbs')

// Ensure test directory exists
function ensureTestDir() {
  try {
    mkdirSync(TEST_DB_DIR, { recursive: true })
  } catch (e) {
    // Directory might already exist, ignore
  }
}

// Clean up entire test directory
export function cleanupAllTestDatabases() {
  try {
    rmSync(TEST_DB_DIR, { recursive: true, force: true })
    console.log('🧹 Cleaned up test database directory')
  } catch (e) {
    // Directory might not exist, ignore
  }
}

// Global shared database for fast tests
let sharedPrisma: PrismaClient | null = null
let sharedDbName: string | null = null

/**
 * STRATEGY 1: Shared Database with Transaction Rollback (FASTEST)
 * - Single database for entire test suite
 * - Each test runs in a transaction that gets rolled back
 * - Perfect isolation, ~500ms for all tests
 *
 * NOTE: This strategy is more complex to implement correctly with Vitest
 * For now, use setupSharedCleanDb() which is simpler and nearly as fast
 */
export async function setupSharedTransactionDb() {
  if (!sharedPrisma) {
    ensureTestDir()
    sharedDbName = `shared_test_${randomBytes(4).toString('hex')}.sqlite`
    const testDbPath = join(TEST_DB_DIR, sharedDbName)
    const testDbUrl = `file:${testDbPath}`

    // Create schema once for all tests
    await createTestSchema(testDbUrl)

    sharedPrisma = new PrismaClient({
      datasources: { db: { url: testDbUrl } }
    })
  }

  // For now, just return the shared client
  // TODO: Implement proper transaction rollback strategy
  return {
    prisma: sharedPrisma,
    cleanup: async () => {
      // Transaction rollback would go here
    }
  }
}

/**
 * STRATEGY 2: Shared Database with Table Truncation (FAST)
 * - Single database for entire test suite
 * - Tables are truncated between tests
 * - Good isolation, ~1-2s for all tests
 */
export async function setupSharedCleanDb() {
  if (!sharedPrisma) {
    ensureTestDir()
    sharedDbName = `shared_test_${randomBytes(4).toString('hex')}.sqlite`
    const testDbPath = join(TEST_DB_DIR, sharedDbName)
    const testDbUrl = `file:${testDbPath}`

    console.log('🔧 Creating shared test database (one-time setup)...')
    await createTestSchema(testDbUrl)

    sharedPrisma = new PrismaClient({
      datasources: { db: { url: testDbUrl } }
    })
    console.log('✅ Shared test database ready')
  }

  // Clean all tables before test (fast operation)
  await cleanAllTables(sharedPrisma)

  return {
    prisma: sharedPrisma,
    cleanup: async () => {
      // Tables will be cleaned before next test
    }
  }
}

/**
 * STRATEGY 3: Isolated Database per Test (SLOWEST but MAXIMUM ISOLATION)
 * - New database for each test
 * - Complete isolation
 * - Use for complex scenarios, ~2s per test
 */
export async function setupIsolatedDb() {
  ensureTestDir()
  const testDbName = `isolated_test_${randomBytes(8).toString('hex')}.sqlite`
  const testDbPath = join(TEST_DB_DIR, testDbName)
  const testDbUrl = `file:${testDbPath}`

  await createTestSchema(testDbUrl)

  const prisma = new PrismaClient({
    datasources: { db: { url: testDbUrl } }
  })

  return {
    prisma,
    cleanup: async () => {
      await prisma.$disconnect()
      try { unlinkSync(testDbPath) } catch {}
      try { unlinkSync(`${testDbPath}-journal`) } catch {}
    }
  }
}

/**
 * Helper: Create test schema (reused by all strategies)
 */
async function createTestSchema(testDbUrl: string) {
  const originalSchema = readFileSync('./prisma/schema.prisma', 'utf8')
  const tempSchemaFile = `./prisma/schema.test.${randomBytes(4).toString('hex')}.prisma`
  
  const testSchema = originalSchema.replace(
    /url\s*=\s*env\("[^"]*"\)/,
    `url = "${testDbUrl}"`
  )
  
  writeFileSync(tempSchemaFile, testSchema)

  try {
    execSync(`npx prisma db push --force-reset --accept-data-loss --schema=${tempSchemaFile}`, {
      stdio: 'pipe',
      encoding: 'utf8'
    })
  } finally {
    try { unlinkSync(tempSchemaFile) } catch {}
  }
}

/**
 * Helper: Clean all tables (for shared database strategy)
 */
async function cleanAllTables(prisma: PrismaClient) {
  // Get all table names
  const tables = await prisma.$queryRaw`
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name != '_prisma_migrations'
  ` as Array<{ name: string }>
  
  // Truncate all tables
  for (const table of tables) {
    await prisma.$executeRawUnsafe(`DELETE FROM "${table.name}"`)
  }
}

/**
 * Cleanup shared resources (call at end of test suite)
 */
export async function cleanupSharedResources() {
  if (sharedPrisma) {
    await sharedPrisma.$disconnect()
    sharedPrisma = null
  }

  sharedDbName = null

  // Clean up entire test directory
  cleanupAllTestDatabases()
}
