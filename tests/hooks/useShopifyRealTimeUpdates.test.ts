import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import type { JobDetail } from '~/types/models'

// Mock the useRevalidator hook from Remix
const mockRevalidator = {
  revalidate: vi.fn(),
  state: 'idle' as any,
}

vi.mock('@remix-run/react', () => ({
  useRevalidator: () => mockRevalidator,
}))

// Import after mocking
import { useShopifyRealTimeUpdates, useJobRealTimeUpdates, useJobsRealTimeUpdates } from '~/hooks/useShopifyRealTimeUpdates'

// Mock timers for testing intervals
vi.useFakeTimers()

const createMockJob = (overrides: Partial<JobDetail> = {}): JobDetail => ({
  id: 'job-123',
  title: 'Test Job',
  description: 'Test Description',
  shopId: 'test-shop',
  status: 'SCHEDULED',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  modifications: [],
  totalProducts: 100,
  processedProducts: 0,
  successfulUpdates: 0,
  failedUpdates: 0,
  productVariants: [],
  ...overrides,
})

describe('Shopify Real-Time Updates Hook Logic', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockRevalidator.state = 'idle'
  })

  afterEach(() => {
    vi.clearAllTimers()
  })

  describe('Polling Interval Logic', () => {
    it('should use active interval for jobs in progress', () => {
      const activeJob = createMockJob({ status: 'IN_PROGRESS' })
      
      // Test the interval calculation logic
      const hasActiveJobs = activeJob.status === 'IN_PROGRESS'
      const hasScheduledJobs = activeJob.status === 'SCHEDULED'
      
      const expectedInterval = hasActiveJobs ? 2000 : hasScheduledJobs ? 5000 : 30000
      expect(expectedInterval).toBe(2000)
    })

    it('should use scheduled interval for scheduled jobs', () => {
      const scheduledJob = createMockJob({ status: 'SCHEDULED' })
      
      const hasActiveJobs = scheduledJob.status === 'IN_PROGRESS'
      const hasScheduledJobs = scheduledJob.status === 'SCHEDULED'
      
      const expectedInterval = hasActiveJobs ? 2000 : hasScheduledJobs ? 5000 : 30000
      expect(expectedInterval).toBe(5000)
    })

    it('should use idle interval for completed jobs', () => {
      const completedJob = createMockJob({ status: 'COMPLETED' })
      
      const hasActiveJobs = completedJob.status === 'IN_PROGRESS'
      const hasScheduledJobs = completedJob.status === 'SCHEDULED'
      
      const expectedInterval = hasActiveJobs ? 2000 : hasScheduledJobs ? 5000 : 30000
      expect(expectedInterval).toBe(30000)
    })

    it('should handle custom intervals', () => {
      const customIntervals = {
        active: 1000,
        scheduled: 3000,
        idle: 60000,
      }
      
      // Test custom interval logic
      const activeJob = createMockJob({ status: 'IN_PROGRESS' })
      const hasActiveJobs = activeJob.status === 'IN_PROGRESS'
      const hasScheduledJobs = activeJob.status === 'SCHEDULED'
      
      const expectedInterval = hasActiveJobs ? customIntervals.active : 
                              hasScheduledJobs ? customIntervals.scheduled : 
                              customIntervals.idle
      expect(expectedInterval).toBe(1000)
    })
  })

  describe('Job Status Detection', () => {
    it('should detect active jobs correctly', () => {
      const jobs = [
        createMockJob({ id: 'job-1', status: 'COMPLETED' }),
        createMockJob({ id: 'job-2', status: 'IN_PROGRESS' }),
        createMockJob({ id: 'job-3', status: 'SCHEDULED' }),
      ]
      
      const hasActiveJobs = jobs.some(job => job.status === 'IN_PROGRESS')
      const hasScheduledJobs = jobs.some(job => job.status === 'SCHEDULED')
      
      expect(hasActiveJobs).toBe(true)
      expect(hasScheduledJobs).toBe(true)
    })

    it('should detect when no active jobs exist', () => {
      const jobs = [
        createMockJob({ id: 'job-1', status: 'COMPLETED' }),
        createMockJob({ id: 'job-2', status: 'FAILED' }),
        createMockJob({ id: 'job-3', status: 'CANCELLED' }),
      ]
      
      const hasActiveJobs = jobs.some(job => job.status === 'IN_PROGRESS')
      const hasScheduledJobs = jobs.some(job => job.status === 'SCHEDULED')
      
      expect(hasActiveJobs).toBe(false)
      expect(hasScheduledJobs).toBe(false)
    })

    it('should handle single job correctly', () => {
      const job = createMockJob({ status: 'IN_PROGRESS' })
      const jobArray = [job]
      
      const hasActiveJobs = jobArray.some(j => j.status === 'IN_PROGRESS')
      expect(hasActiveJobs).toBe(true)
    })

    it('should handle empty job array', () => {
      const jobs: JobDetail[] = []
      
      const hasActiveJobs = jobs.some(job => job.status === 'IN_PROGRESS')
      const hasScheduledJobs = jobs.some(job => job.status === 'SCHEDULED')
      
      expect(hasActiveJobs).toBe(false)
      expect(hasScheduledJobs).toBe(false)
    })
  })

  describe('Status Change Detection', () => {
    it('should detect status changes correctly', () => {
      const previousStatus: string = 'SCHEDULED'
      const currentStatus: string = 'IN_PROGRESS'

      const hasStatusChanged = previousStatus !== currentStatus
      expect(hasStatusChanged).toBe(true)
    })

    it('should not trigger on same status', () => {
      const previousStatus: string = 'IN_PROGRESS'
      const currentStatus: string = 'IN_PROGRESS'

      const hasStatusChanged = previousStatus !== currentStatus
      expect(hasStatusChanged).toBe(false)
    })

    it('should handle completion status change', () => {
      const previousStatus: string = 'IN_PROGRESS'
      const currentStatus: string = 'COMPLETED'

      const hasStatusChanged = previousStatus !== currentStatus
      const isCompleted = currentStatus === 'COMPLETED'

      expect(hasStatusChanged).toBe(true)
      expect(isCompleted).toBe(true)
    })

    it('should handle failure status change', () => {
      const previousStatus: string = 'IN_PROGRESS'
      const currentStatus: string = 'FAILED'

      const hasStatusChanged = previousStatus !== currentStatus
      const isFailed = currentStatus === 'FAILED'

      expect(hasStatusChanged).toBe(true)
      expect(isFailed).toBe(true)
    })
  })

  describe('Progress Calculation', () => {
    it('should calculate progress correctly', () => {
      const job = createMockJob({
        status: 'IN_PROGRESS',
        processedProducts: 25,
        totalProducts: 100,
      })
      
      const progress = (job.processedProducts / job.totalProducts) * 100
      expect(progress).toBe(25)
    })

    it('should handle zero total products', () => {
      const job = createMockJob({
        status: 'IN_PROGRESS',
        processedProducts: 0,
        totalProducts: 0,
      })
      
      const shouldCalculateProgress = job.status === 'IN_PROGRESS' && job.totalProducts > 0
      expect(shouldCalculateProgress).toBe(false)
    })

    it('should handle completed progress', () => {
      const job = createMockJob({
        status: 'COMPLETED',
        processedProducts: 100,
        totalProducts: 100,
      })
      
      const progress = (job.processedProducts / job.totalProducts) * 100
      expect(progress).toBe(100)
    })
  })

  describe('Revalidation Logic', () => {
    it('should trigger revalidation when revalidator is idle', () => {
      mockRevalidator.state = 'idle'

      // Simulate polling interval trigger
      const shouldRevalidate = mockRevalidator.state === 'idle'

      if (shouldRevalidate) {
        mockRevalidator.revalidate()
      }

      expect(mockRevalidator.revalidate).toHaveBeenCalledTimes(1)
    })

    it('should not trigger revalidation when revalidator is busy', () => {
      mockRevalidator.state = 'loading'

      // Simulate polling interval trigger
      const shouldRevalidate = mockRevalidator.state === 'idle'

      if (shouldRevalidate) {
        mockRevalidator.revalidate()
      }

      expect(mockRevalidator.revalidate).not.toHaveBeenCalled()
    })

    it('should handle submitting state', () => {
      mockRevalidator.state = 'submitting'

      const shouldRevalidate = mockRevalidator.state === 'idle'
      expect(shouldRevalidate).toBe(false)
    })
  })

  describe('Callback Triggers', () => {
    it('should identify when to trigger onJobComplete callback', () => {
      const previousStatus: string = 'IN_PROGRESS'
      const currentStatus: string = 'COMPLETED'

      const hasStatusChanged = previousStatus !== currentStatus
      const shouldTriggerComplete = hasStatusChanged && currentStatus === 'COMPLETED'

      expect(shouldTriggerComplete).toBe(true)
    })

    it('should identify when to trigger onJobFailed callback', () => {
      const previousStatus: string = 'IN_PROGRESS'
      const currentStatus: string = 'FAILED'

      const hasStatusChanged = previousStatus !== currentStatus
      const shouldTriggerFailed = hasStatusChanged && currentStatus === 'FAILED'

      expect(shouldTriggerFailed).toBe(true)
    })

    it('should identify when to trigger onProgressUpdate callback', () => {
      const job = createMockJob({
        status: 'IN_PROGRESS',
        processedProducts: 50,
        totalProducts: 100,
      })
      
      const shouldTriggerProgress = job.status === 'IN_PROGRESS' && job.totalProducts > 0
      expect(shouldTriggerProgress).toBe(true)
    })
  })
})
