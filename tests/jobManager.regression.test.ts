import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { setupIsolatedDb } from './test-db-strategies'
import { PrismaClient } from '@prisma/client'
import {
  createJob,
  getJobById,
  getJobsByShop,
  updateJobStatus,
  storeModificationsData,
  getModificationsData
} from '~/utils/jobManager.server'
import { CreateJobData } from '~/types/models'

// Mock Shopify authentication
vi.mock('~/shopify.server', () => ({
  authenticate: {
    admin: vi.fn().mockResolvedValue({
      session: {
        shop: 'test-shop.myshopify.com',
        accessToken: 'test-token'
      }
    })
  }
}))

// Mock the database to use our test instance
let testPrisma: PrismaClient
vi.mock('~/db.server', () => ({
  default: new Proxy({} as PrismaClient, {
    get(target, prop) {
      if (!testPrisma) {
        throw new Error('Test database not initialized. Make sure beforeEach has run.')
      }
      const value = testPrisma[prop as keyof PrismaClient]

      // Bind methods to the actual client instance
      if (typeof value === 'function') {
        return value.bind(testPrisma)
      }

      return value
    }
  })
}))

describe('JobManager Regression Tests', () => {
  let prisma: PrismaClient
  let cleanup: () => Promise<void>

  beforeEach(async () => {
    const setup = await setupIsolatedDb()
    prisma = setup.prisma
    cleanup = setup.cleanup
    testPrisma = prisma // Set the test instance for the mock
  })

  afterEach(async () => {
    await cleanup()
  })

  describe('createJob', () => {
    it('should create a job with modifications', async () => {
      const mockRequest = new Request('http://localhost:3000/test')
      const jobData: CreateJobData = {
        title: 'Test Job',
        description: 'Test job description',
        modifications: [
          {
            fieldType: 'product',
            fieldName: 'title',
            fieldValue: 'New Title'
          },
          {
            fieldType: 'variant',
            fieldName: 'price',
            fieldValue: '29.99'
          }
        ],
        unselectedIds: ['product-1', 'variant-2'],
        filterCriteria: { vendor: 'Test Vendor' }
      }

      const jobId = await createJob(mockRequest, jobData)

      expect(jobId).toBeDefined()
      expect(typeof jobId).toBe('string')

      // Verify job was created in database
      const job = await prisma.job.findUnique({
        where: { id: jobId },
        include: { modifications: true }
      })

      expect(job).toBeDefined()
      expect(job?.title).toBe('Test Job')
      expect(job?.description).toBe('Test job description')
      expect(job?.shopId).toBe('test-shop.myshopify.com')
      expect(job?.status).toBe('IN_PROGRESS')
      expect(job?.scheduledAt).toBeNull()
      expect(job?.filterCriteria).toBe(JSON.stringify({ vendor: 'Test Vendor' }))
      expect(job?.unselectedIds).toBe(JSON.stringify(['product-1', 'variant-2']))
      expect(job?.modifications).toHaveLength(2)
    })

    it('should create a scheduled job', async () => {
      const mockRequest = new Request('http://localhost:3000/test')
      const scheduledDate = new Date(Date.now() + 3600000).toISOString() // 1 hour from now
      const jobData: CreateJobData = {
        title: 'Scheduled Job',
        scheduledAt: scheduledDate,
        modifications: [],
        unselectedIds: []
      }

      const jobId = await createJob(mockRequest, jobData)
      const job = await prisma.job.findUnique({ where: { id: jobId } })

      expect(job?.status).toBe('SCHEDULED')
      expect(job?.scheduledAt).toEqual(new Date(scheduledDate))
    })

    it('should handle job with no modifications', async () => {
      const mockRequest = new Request('http://localhost:3000/test')
      const jobData: CreateJobData = {
        title: 'No Modifications Job',
        modifications: [],
        unselectedIds: []
      }

      const jobId = await createJob(mockRequest, jobData)
      const job = await prisma.job.findUnique({
        where: { id: jobId },
        include: { modifications: true }
      })

      expect(job?.modifications).toHaveLength(0)
    })
  })

  describe('getJobById', () => {
    it('should retrieve job with all related data', async () => {
      // Create test job
      const job = await prisma.job.create({
        data: {
          title: 'Test Retrieval Job',
          description: 'Test description',
          shopId: 'test-shop.myshopify.com',
          status: 'COMPLETED',
          filterCriteria: JSON.stringify({ category: 'Electronics' }),
          unselectedIds: JSON.stringify(['product-1']),
          totalProducts: 10,
          processedProducts: 10,
          successfulUpdates: 8,
          failedUpdates: 2
        }
      })

      // Add modifications
      await prisma.jobModification.create({
        data: {
          jobId: job.id,
          fieldType: 'product',
          fieldName: 'vendor',
          fieldValue: 'New Vendor'
        }
      })

      // Add product variant
      await prisma.jobProductVariant.create({
        data: {
          jobId: job.id,
          productId: 'product-123',
          variantId: 'variant-456',
          status: 'SUCCESS',
          originalValues: JSON.stringify({ price: '19.99' }),
          newValues: JSON.stringify({ price: '24.99' })
        }
      })

      const result = await getJobById(job.id, 'test-shop.myshopify.com')

      expect(result).toBeDefined()
      expect(result?.id).toBe(job.id)
      expect(result?.title).toBe('Test Retrieval Job')
      expect(result?.shopId).toBe('test-shop.myshopify.com')
      expect(result?.status).toBe('COMPLETED')
      expect(result?.filterCriteria).toEqual({ category: 'Electronics' })
      expect(result?.unselectedIds).toEqual(['product-1'])
      expect(result?.modifications).toHaveLength(1)
      expect(result?.productVariants).toHaveLength(1)
      expect(result?.productVariants[0].originalValues).toEqual({ price: '19.99' })
    })

    it('should handle complex job data with multiple modifications and variants', async () => {
      // Create test job with complex data
      const job = await prisma.job.create({
        data: {
          title: 'Complex Test Job',
          description: 'Complex test description',
          shopId: 'test-shop.myshopify.com',
          status: 'IN_PROGRESS',
          filterCriteria: JSON.stringify({ category: 'Electronics', vendor: 'Apple' }),
          unselectedIds: JSON.stringify(['product-1', 'product-2']),
          totalProducts: 50,
          processedProducts: 25,
          successfulUpdates: 20,
          failedUpdates: 5
        }
      })

      // Add multiple modifications with different field types
      await prisma.jobModification.createMany({
        data: [
          {
            jobId: job.id,
            fieldType: 'product',
            fieldName: 'vendor',
            fieldValue: 'New Vendor'
          },
          {
            jobId: job.id,
            fieldType: 'variant',
            fieldName: 'price',
            fieldValue: '29.99'
          },
          {
            jobId: job.id,
            fieldType: 'product',
            fieldName: 'tags',
            fieldValue: 'sale,featured'
          }
        ]
      })

      // Add multiple product variants with different statuses and edge cases
      await prisma.jobProductVariant.createMany({
        data: [
          {
            jobId: job.id,
            productId: 'product-123',
            variantId: 'variant-456',
            status: 'SUCCESS',
            originalValues: JSON.stringify({ price: '19.99', inventory: 100 }),
            newValues: JSON.stringify({ price: '24.99', inventory: 95 }),
            processedAt: new Date('2024-01-15T10:30:00Z')
          },
          {
            jobId: job.id,
            productId: 'product-124',
            variantId: null, // Test null variantId
            status: 'FAILED',
            errorMessage: 'Validation failed',
            originalValues: JSON.stringify({ title: 'Old Title' }),
            newValues: null, // Test null newValues
            processedAt: null // Test null processedAt
          },
          {
            jobId: job.id,
            productId: 'product-125',
            variantId: 'variant-789',
            status: 'PENDING',
            errorMessage: null, // Test null errorMessage
            originalValues: null, // Test null originalValues
            newValues: JSON.stringify({ description: 'New Description' }),
            processedAt: new Date('2024-01-15T11:00:00Z')
          }
        ]
      })

      const result = await getJobById(job.id, 'test-shop.myshopify.com')

      expect(result).toBeDefined()
      expect(result?.modifications).toHaveLength(3)
      expect(result?.productVariants).toHaveLength(3)

      // Test modification mapping
      const productMods = result?.modifications.filter(m => m.fieldType === 'product')
      const variantMods = result?.modifications.filter(m => m.fieldType === 'variant')
      expect(productMods).toHaveLength(2)
      expect(variantMods).toHaveLength(1)

      // Test product variant mapping with all edge cases
      const variants = result?.productVariants || []

      // Test variant with all fields populated
      const successVariant = variants.find(v => v.status === 'SUCCESS')
      expect(successVariant?.variantId).toBe('variant-456')
      expect(successVariant?.originalValues).toEqual({ price: '19.99', inventory: 100 })
      expect(successVariant?.newValues).toEqual({ price: '24.99', inventory: 95 })
      expect(successVariant?.processedAt).toBe('2024-01-15T10:30:00.000Z')

      // Test variant with null variantId and newValues
      const failedVariant = variants.find(v => v.status === 'FAILED')
      expect(failedVariant?.variantId).toBeUndefined()
      expect(failedVariant?.newValues).toBeUndefined()
      expect(failedVariant?.errorMessage).toBe('Validation failed')
      expect(failedVariant?.processedAt).toBeUndefined()

      // Test variant with null originalValues and errorMessage
      const pendingVariant = variants.find(v => v.status === 'PENDING')
      expect(pendingVariant?.originalValues).toBeUndefined()
      expect(pendingVariant?.errorMessage).toBeUndefined()
      expect(pendingVariant?.newValues).toEqual({ description: 'New Description' })
    })

    it('should return null for non-existent job', async () => {
      const result = await getJobById('non-existent-id', 'test-shop.myshopify.com')
      expect(result).toBeNull()
    })

    it('should return null for job from different shop', async () => {
      const job = await prisma.job.create({
        data: {
          title: 'Other Shop Job',
          shopId: 'other-shop.myshopify.com',
          status: 'IN_PROGRESS'
        }
      })

      const result = await getJobById(job.id, 'test-shop.myshopify.com')
      expect(result).toBeNull()
    })
  })

  describe('getJobsByShop', () => {
    it('should retrieve jobs for specific shop ordered by creation date', async () => {
      const shopId = 'test-shop.myshopify.com'

      // Create multiple jobs
      const job1 = await prisma.job.create({
        data: {
          title: 'First Job',
          shopId,
          status: 'COMPLETED',
          createdAt: new Date(Date.now() - 2000) // 2 seconds ago
        }
      })

      const job2 = await prisma.job.create({
        data: {
          title: 'Second Job',
          shopId,
          status: 'IN_PROGRESS',
          createdAt: new Date(Date.now() - 1000) // 1 second ago
        }
      })

      const job3 = await prisma.job.create({
        data: {
          title: 'Third Job',
          shopId,
          status: 'SCHEDULED'
        }
      })

      // Create job for different shop
      await prisma.job.create({
        data: {
          title: 'Other Shop Job',
          shopId: 'other-shop.myshopify.com',
          status: 'COMPLETED'
        }
      })

      const results = await getJobsByShop(shopId)

      expect(results).toHaveLength(3)
      expect(results[0].title).toBe('Third Job') // Most recent first
      expect(results[1].title).toBe('Second Job')
      expect(results[2].title).toBe('First Job')
    })

    it('should respect limit parameter', async () => {
      const shopId = 'test-shop-limit.myshopify.com'

      // Create 5 jobs
      for (let i = 0; i < 5; i++) {
        await prisma.job.create({
          data: {
            title: `Job ${i}`,
            shopId,
            status: 'COMPLETED'
          }
        })
      }

      const results = await getJobsByShop(shopId, 3)
      expect(results).toHaveLength(3)
    })
  })

  describe('updateJobStatus', () => {
    it('should update job status to IN_PROGRESS and set startedAt', async () => {
      const job = await prisma.job.create({
        data: {
          title: 'Status Update Job',
          shopId: 'test-shop.myshopify.com',
          status: 'SCHEDULED'
        }
      })

      await updateJobStatus(job.id, 'IN_PROGRESS', 'test-shop.myshopify.com')

      const updatedJob = await prisma.job.findUnique({ where: { id: job.id } })
      expect(updatedJob?.status).toBe('IN_PROGRESS')
      expect(updatedJob?.startedAt).toBeInstanceOf(Date)
      expect(updatedJob?.completedAt).toBeNull()
    })

    it('should update job status to COMPLETED and set completedAt', async () => {
      const job = await prisma.job.create({
        data: {
          title: 'Completion Job',
          shopId: 'test-shop.myshopify.com',
          status: 'IN_PROGRESS'
        }
      })

      await updateJobStatus(job.id, 'COMPLETED', 'test-shop.myshopify.com')

      const updatedJob = await prisma.job.findUnique({ where: { id: job.id } })
      expect(updatedJob?.status).toBe('COMPLETED')
      expect(updatedJob?.completedAt).toBeInstanceOf(Date)
    })

    it('should only update jobs for the correct shop', async () => {
      const job = await prisma.job.create({
        data: {
          title: 'Wrong Shop Job',
          shopId: 'other-shop.myshopify.com',
          status: 'SCHEDULED'
        }
      })

      await updateJobStatus(job.id, 'IN_PROGRESS', 'test-shop.myshopify.com')

      const unchangedJob = await prisma.job.findUnique({ where: { id: job.id } })
      expect(unchangedJob?.status).toBe('SCHEDULED') // Should remain unchanged
    })
  })

  describe('storeModificationsData', () => {
    it('should store modifications and unselected IDs', async () => {
      const mockRequest = new Request('http://localhost:3000/test')
      const modifications = [
        { fieldType: 'product', fieldName: 'title', fieldValue: 'New Title' },
        { fieldType: 'variant', fieldName: 'price', fieldValue: '19.99' }
      ]
      const unselectedIds = ['product-1', 'variant-2']

      const sessionKey = await storeModificationsData(mockRequest, modifications, unselectedIds)

      expect(sessionKey).toBeDefined()
      expect(sessionKey).toMatch(/^modifications_\d+_[a-z0-9]+$/)

      // Verify data was stored
      const stored = await prisma.tempSelection.findUnique({
        where: { sessionKey }
      })

      expect(stored).toBeDefined()
      expect(stored?.shopId).toBe('test-shop.myshopify.com')

      const parsedData = JSON.parse(stored?.data || '{}')
      expect(parsedData.modifications).toEqual(modifications)
      expect(parsedData.unselectedIds).toEqual(unselectedIds)
    })

    it('should replace existing data when sessionKey is provided', async () => {
      const mockRequest = new Request('http://localhost:3000/test')
      const existingKey = 'existing-modifications-key'

      // Create existing data
      await prisma.tempSelection.create({
        data: {
          sessionKey: existingKey,
          shopId: 'test-shop.myshopify.com',
          data: JSON.stringify({ modifications: [], unselectedIds: [] }),
          expiresAt: new Date(Date.now() + 3600000)
        }
      })

      const newModifications = [{ fieldType: 'product', fieldName: 'vendor', fieldValue: 'New Vendor' }]
      const newUnselectedIds = ['product-new']

      const returnedKey = await storeModificationsData(mockRequest, newModifications, newUnselectedIds, existingKey)

      expect(returnedKey).toBe(existingKey)

      // Verify old data was replaced
      const stored = await prisma.tempSelection.findUnique({
        where: { sessionKey: existingKey }
      })

      const parsedData = JSON.parse(stored?.data || '{}')
      expect(parsedData.modifications).toEqual(newModifications)
      expect(parsedData.unselectedIds).toEqual(newUnselectedIds)
    })
  })

  describe('getModificationsData', () => {
    it('should retrieve stored modifications data', async () => {
      const sessionKey = 'test-modifications-key'
      const testData = {
        modifications: [{ fieldType: 'product', fieldName: 'title', fieldValue: 'Test Title' }],
        unselectedIds: ['product-1', 'product-2']
      }

      await prisma.tempSelection.create({
        data: {
          sessionKey,
          shopId: 'test-shop.myshopify.com',
          data: JSON.stringify(testData),
          expiresAt: new Date(Date.now() + 3600000)
        }
      })

      const result = await getModificationsData(sessionKey)

      expect(result).toEqual(testData)
    })

    it('should return null for non-existent session key', async () => {
      const result = await getModificationsData('non-existent-key')
      expect(result).toBeNull()
    })

    it('should return null for empty session key', async () => {
      const result = await getModificationsData('')
      expect(result).toBeNull()
    })

    it('should return null for expired session', async () => {
      const sessionKey = 'expired-modifications-key'

      await prisma.tempSelection.create({
        data: {
          sessionKey,
          shopId: 'test-shop.myshopify.com',
          data: JSON.stringify({ modifications: [], unselectedIds: [] }),
          expiresAt: new Date(Date.now() - 1000) // 1 second ago
        }
      })

      const result = await getModificationsData(sessionKey)
      expect(result).toBeNull()
    })

    it('should handle malformed JSON gracefully', async () => {
      const sessionKey = 'malformed-modifications-key'

      await prisma.tempSelection.create({
        data: {
          sessionKey,
          shopId: 'test-shop.myshopify.com',
          data: 'invalid-json{',
          expiresAt: new Date(Date.now() + 3600000)
        }
      })

      const result = await getModificationsData(sessionKey)
      expect(result).toBeNull()
    })
  })
})
