import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { setupIsolatedDb } from './test-db-strategies'

// Import the functions we want to test
import { loader as statsLoader, serverError } from '~/routes/api.stats'

// Mock the dummyRepo functions
vi.mock('~/data/dummyRepo', () => ({
  getJobsCount: vi.fn(() => 15),
  getPlanDetails: vi.fn(() => 'Pro plan (Till: 21 Mar 2025)'),
  getTimeSaved: vi.fn(() => '45 minutes'),
  totalProductUpdated: vi.fn(() => 1200),
  getActiveRecentJobs: vi.fn(() => [
    {
      id: 'abc',
      name: 'Updates for Christmas',
      executionTime: '21 Mar 2026 7:00PM',
      status: 0, // JobStatus.InProcess
      productsAffected: 42
    },
    {
      id: 'xyz',
      name: 'Update Taxes',
      executionTime: '21 Mar 2024 7:00PM',
      status: 2, // JobStatus.Completed
      productsAffected: 100
    }
  ])
}))

// Don't mock the api.stats module - let it use the real implementation with mocked dummyRepo

describe('API Routes Regression Tests', () => {
  let cleanup: () => Promise<void>

  beforeEach(async () => {
    const setup = await setupIsolatedDb()
    cleanup = setup.cleanup
    
    // Reset all mocks
    vi.clearAllMocks()
  })

  afterEach(async () => {
    await cleanup()
  })

  describe('api.stats.ts', () => {
    describe('statsLoader', () => {
      it('should return correct stats response structure', async () => {
        const mockRequest = new Request('http://localhost:3000/api/stats')
        
        const result = await statsLoader({ request: mockRequest, params: {}, context: {} })
        
        expect(result).toEqual({
          totalJobs: 15,
          plan: 'Pro plan (Till: 21 Mar 2025)',
          timeSaved: '45 minutes',
          productsUpdated: 1200
        })
      })

      it('should handle query parameters correctly', async () => {
        const mockRequest = new Request('http://localhost:3000/api/stats?type=summary')
        
        const result = await statsLoader({ request: mockRequest, params: {}, context: {} })
        
        // Should still return the same structure regardless of type parameter
        expect(result).toHaveProperty('totalJobs')
        expect(result).toHaveProperty('plan')
        expect(result).toHaveProperty('timeSaved')
        expect(result).toHaveProperty('productsUpdated')
      })

      it('should handle empty query parameters', async () => {
        const mockRequest = new Request('http://localhost:3000/api/stats')
        
        const result = await statsLoader({ request: mockRequest, params: {}, context: {} })
        
        expect(result).toBeDefined()
        expect(typeof result.totalJobs).toBe('number')
        expect(typeof result.plan).toBe('string')
        expect(typeof result.timeSaved).toBe('string')
        expect(typeof result.productsUpdated).toBe('number')
      })

      it('should handle malformed URLs gracefully', async () => {
        const mockRequest = new Request('http://localhost:3000/api/stats?invalid=param&another=value')
        
        const result = await statsLoader({ request: mockRequest, params: {}, context: {} })
        
        // Should still work despite invalid parameters
        expect(result).toEqual({
          totalJobs: 15,
          plan: 'Pro plan (Till: 21 Mar 2025)',
          timeSaved: '45 minutes',
          productsUpdated: 1200
        })
      })

      it('should call all required dummyRepo functions', async () => {
        const { getJobsCount, getPlanDetails, getTimeSaved, totalProductUpdated } = await import('~/data/dummyRepo')
        const mockRequest = new Request('http://localhost:3000/api/stats')

        await statsLoader({ request: mockRequest, params: {}, context: {} })

        expect(getJobsCount).toHaveBeenCalledTimes(1)
        expect(getPlanDetails).toHaveBeenCalledTimes(1)
        expect(getTimeSaved).toHaveBeenCalledTimes(1)
        expect(totalProductUpdated).toHaveBeenCalledTimes(1)
      })

      it('should handle errors from dummyRepo functions', async () => {
        const { getJobsCount } = await import('~/data/dummyRepo')

        // Mock one of the functions to throw an error
        ;(getJobsCount as any).mockImplementation(() => {
          throw new Error('Database connection failed')
        })

        const mockRequest = new Request('http://localhost:3000/api/stats')

        // The error should be caught and re-thrown as a Response with serverError
        try {
          await statsLoader({
            request: mockRequest,
            params: {},
            context: {}
          })
          // If we get here, the test should fail
          expect(true).toBe(false)
        } catch (error) {
          // Should throw a Response object with status 500
          expect(error).toBeInstanceOf(Response)
          expect((error as Response).status).toBe(500)

          const body = await (error as Response).json()
          expect(body).toEqual({ error: 'Internal server error while fetching stats' })
        }
      })
    })

    describe('serverError', () => {
      it('should create proper error response with message', () => {
        const errorMessage = 'Test error message'
        const response = serverError(errorMessage)
        
        expect(response).toBeInstanceOf(Response)
        expect(response.status).toBe(500)
        expect(response.headers.get('Content-Type')).toBe('application/json')
      })

      it('should include error message in response body', async () => {
        const errorMessage = 'Database connection failed'
        const response = serverError(errorMessage)
        
        const body = await response.json()
        expect(body).toEqual({ error: errorMessage })
      })

      it('should handle empty error message', async () => {
        const response = serverError('')
        
        const body = await response.json()
        expect(body).toEqual({ error: '' })
      })

      it('should handle special characters in error message', async () => {
        const errorMessage = 'Error with "quotes" and special chars: <>&'
        const response = serverError(errorMessage)
        
        const body = await response.json()
        expect(body.error).toBe(errorMessage)
      })

      it('should handle very long error messages', async () => {
        const longMessage = 'A'.repeat(1000)
        const response = serverError(longMessage)
        
        const body = await response.json()
        expect(body.error).toBe(longMessage)
        expect(body.error.length).toBe(1000)
      })
    })
  })

  describe('api.jobs.ts', () => {
    describe('Recent Jobs API', () => {
      it('should export a loader function with proper signature', async () => {
        // Mock Shopify authentication to avoid environment dependencies
        const mockAuthenticate = {
          admin: vi.fn().mockResolvedValue({
            session: { shop: 'test-shop.myshopify.com' }
          })
        }

        const mockGetJobsByShop = vi.fn().mockResolvedValue([
          {
            id: 'job-1',
            title: 'Test Job',
            status: 'IN_PROGRESS',
            createdAt: new Date().toISOString(),
            totalProducts: 100,
            modifications: [],
            productVariants: [],
          }
        ])

        // Mock the dependencies before importing
        vi.doMock('~/shopify.server', () => ({
          authenticate: mockAuthenticate
        }))

        vi.doMock('~/utils/jobManager.server', () => ({
          getJobsByShop: mockGetJobsByShop
        }))

        // Now import and test the loader
        const { loader } = await import('~/routes/api.jobs')

        expect(typeof loader).toBe('function')
        expect(loader.length).toBe(1) // Should accept LoaderFunctionArgs

        // Test that it can be called with proper arguments
        const mockRequest = new Request('https://test.com/api/jobs')
        const result = await loader({
          request: mockRequest,
          params: {},
          context: {},
        })

        expect(mockAuthenticate.admin).toHaveBeenCalledWith(mockRequest)
        expect(mockGetJobsByShop).toHaveBeenCalledWith('test-shop.myshopify.com', 5)
        expect(Array.isArray(result)).toBe(true)
      })

      it('should validate recent jobs filtering logic', () => {
        const now = new Date()
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        const eightDaysAgo = new Date(now.getTime() - 8 * 24 * 60 * 60 * 1000)
        const threeDaysAgo = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000)

        const mockJobs = [
          {
            id: 'job-1',
            status: 'IN_PROGRESS',
            createdAt: eightDaysAgo.toISOString(),
          },
          {
            id: 'job-2',
            status: 'COMPLETED',
            createdAt: threeDaysAgo.toISOString(),
          },
          {
            id: 'job-3',
            status: 'COMPLETED',
            createdAt: eightDaysAgo.toISOString(),
          },
          {
            id: 'job-4',
            status: 'SCHEDULED',
            createdAt: eightDaysAgo.toISOString(),
          },
        ]

        // Test filtering logic
        const recentJobs = mockJobs.filter(job => {
          const isActive = job.status === 'IN_PROGRESS' || job.status === 'SCHEDULED'
          const isRecent = new Date(job.createdAt) >= sevenDaysAgo
          return isActive || isRecent
        })

        expect(recentJobs).toHaveLength(3)
        expect(recentJobs.map(job => job.id)).toEqual(['job-1', 'job-2', 'job-4'])
      })
    })
  })
})
