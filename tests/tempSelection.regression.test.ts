import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { setupIsolatedDb } from './test-db-strategies'
import { PrismaClient } from '@prisma/client'
import {
  storeUnselectedIds,
  getUnselectedIds,
  clearSelection,
  cleanupExpiredSelections
} from '~/utils/tempSelection.server'

// Mock Shopify authentication
vi.mock('~/shopify.server', () => ({
  authenticate: {
    admin: vi.fn().mockResolvedValue({
      session: {
        shop: 'test-shop.myshopify.com',
        accessToken: 'test-token'
      }
    })
  }
}))

// Mock the database to use our test instance
let testPrisma: PrismaClient
vi.mock('~/db.server', () => ({
  default: new Proxy({} as PrismaClient, {
    get(target, prop) {
      if (!testPrisma) {
        throw new Error('Test database not initialized. Make sure beforeEach has run.')
      }
      const value = testPrisma[prop as keyof PrismaClient]

      // Bind methods to the actual client instance
      if (typeof value === 'function') {
        return value.bind(testPrisma)
      }

      return value
    }
  })
}))

describe('TempSelection Regression Tests', () => {
  let prisma: PrismaClient
  let cleanup: () => Promise<void>

  beforeEach(async () => {
    const setup = await setupIsolatedDb()
    prisma = setup.prisma
    cleanup = setup.cleanup
    testPrisma = prisma // Set the test instance for the mock
  })

  afterEach(async () => {
    await cleanup()
  })

  describe('storeUnselectedIds', () => {
    it('should store unselected IDs and return session key', async () => {
      const mockRequest = new Request('http://localhost:3000/test')
      const unselectedIds = ['product-1', 'product-2', 'variant-3']

      const sessionKey = await storeUnselectedIds(mockRequest, unselectedIds)

      expect(sessionKey).toBeDefined()
      expect(sessionKey).toMatch(/^selection_\d+_[a-z0-9]+$/)

      // Verify data was stored in database
      const stored = await prisma.tempSelection.findUnique({
        where: { sessionKey }
      })

      expect(stored).toBeDefined()
      expect(stored?.shopId).toBe('test-shop.myshopify.com')
      expect(JSON.parse(stored?.data || '')).toEqual(unselectedIds)
      expect(stored?.expiresAt).toBeInstanceOf(Date)
    })

    it('should handle empty unselected IDs array', async () => {
      const mockRequest = new Request('http://localhost:3000/test')
      const unselectedIds: string[] = []

      const sessionKey = await storeUnselectedIds(mockRequest, unselectedIds)

      expect(sessionKey).toBeDefined()

      const stored = await prisma.tempSelection.findUnique({
        where: { sessionKey }
      })

      expect(JSON.parse(stored?.data || '')).toEqual([])
    })

    it('should clean up expired selections before storing new ones', async () => {
      const mockRequest = new Request('http://localhost:3000/test')
      
      // Create an expired selection
      await prisma.tempSelection.create({
        data: {
          sessionKey: 'expired-key',
          shopId: 'test-shop.myshopify.com',
          data: JSON.stringify(['old-data']),
          expiresAt: new Date(Date.now() - 1000) // 1 second ago
        }
      })

      const unselectedIds = ['new-product']
      await storeUnselectedIds(mockRequest, unselectedIds)

      // Expired selection should be cleaned up
      const expiredSelection = await prisma.tempSelection.findUnique({
        where: { sessionKey: 'expired-key' }
      })
      expect(expiredSelection).toBeNull()
    })
  })

  describe('getUnselectedIds', () => {
    it('should retrieve stored unselected IDs', async () => {
      const testData = ['product-1', 'variant-2', 'product-3']
      const sessionKey = 'test-session-key'

      // Store test data
      await prisma.tempSelection.create({
        data: {
          sessionKey,
          shopId: 'test-shop.myshopify.com',
          data: JSON.stringify(testData),
          expiresAt: new Date(Date.now() + 3600000) // 1 hour from now
        }
      })

      const result = await getUnselectedIds(sessionKey)

      expect(result).toEqual(testData)
    })

    it('should return empty array for non-existent session key', async () => {
      const result = await getUnselectedIds('non-existent-key')
      expect(result).toEqual([])
    })

    it('should return empty array for empty session key', async () => {
      const result = await getUnselectedIds('')
      expect(result).toEqual([])
    })

    it('should return empty array for expired session', async () => {
      const sessionKey = 'expired-session-key'

      // Store expired data
      await prisma.tempSelection.create({
        data: {
          sessionKey,
          shopId: 'test-shop.myshopify.com',
          data: JSON.stringify(['expired-data']),
          expiresAt: new Date(Date.now() - 1000) // 1 second ago
        }
      })

      const result = await getUnselectedIds(sessionKey)
      expect(result).toEqual([])
    })

    it('should handle malformed JSON gracefully', async () => {
      const sessionKey = 'malformed-json-key'

      // Store malformed JSON
      await prisma.tempSelection.create({
        data: {
          sessionKey,
          shopId: 'test-shop.myshopify.com',
          data: 'invalid-json{',
          expiresAt: new Date(Date.now() + 3600000)
        }
      })

      const result = await getUnselectedIds(sessionKey)
      expect(result).toEqual([])
    })
  })

  describe('clearSelection', () => {
    it('should delete specific selection', async () => {
      const sessionKey = 'test-clear-key'

      // Store test data
      await prisma.tempSelection.create({
        data: {
          sessionKey,
          shopId: 'test-shop.myshopify.com',
          data: JSON.stringify(['test-data']),
          expiresAt: new Date(Date.now() + 3600000)
        }
      })

      await clearSelection(sessionKey)

      const result = await prisma.tempSelection.findUnique({
        where: { sessionKey }
      })
      expect(result).toBeNull()
    })

    it('should handle non-existent session key gracefully', async () => {
      // Should not throw error
      await expect(clearSelection('non-existent-key')).resolves.toBeUndefined()
    })

    it('should handle empty session key gracefully', async () => {
      // Should not throw error
      await expect(clearSelection('')).resolves.toBeUndefined()
    })
  })

  describe('cleanupExpiredSelections', () => {
    it('should remove expired selections for specific shop', async () => {
      const shopId = 'test-shop.myshopify.com'
      const otherShopId = 'other-shop.myshopify.com'

      // Create expired selection for target shop
      await prisma.tempSelection.create({
        data: {
          sessionKey: 'expired-target-shop',
          shopId,
          data: JSON.stringify(['expired']),
          expiresAt: new Date(Date.now() - 1000)
        }
      })

      // Create valid selection for target shop
      await prisma.tempSelection.create({
        data: {
          sessionKey: 'valid-target-shop',
          shopId,
          data: JSON.stringify(['valid']),
          expiresAt: new Date(Date.now() + 3600000)
        }
      })

      // Create expired selection for other shop
      await prisma.tempSelection.create({
        data: {
          sessionKey: 'expired-other-shop',
          shopId: otherShopId,
          data: JSON.stringify(['other-expired']),
          expiresAt: new Date(Date.now() - 1000)
        }
      })

      await cleanupExpiredSelections(shopId)

      // Expired selection for target shop should be removed
      const expiredTarget = await prisma.tempSelection.findUnique({
        where: { sessionKey: 'expired-target-shop' }
      })
      expect(expiredTarget).toBeNull()

      // Valid selection for target shop should remain
      const validTarget = await prisma.tempSelection.findUnique({
        where: { sessionKey: 'valid-target-shop' }
      })
      expect(validTarget).toBeDefined()

      // Expired selection for other shop should remain (not cleaned up)
      const expiredOther = await prisma.tempSelection.findUnique({
        where: { sessionKey: 'expired-other-shop' }
      })
      expect(expiredOther).toBeDefined()
    })

    it('should remove old selections (7+ days) for specific shop', async () => {
      const shopId = 'test-shop.myshopify.com'
      const oldDate = new Date(Date.now() - 8 * 24 * 60 * 60 * 1000) // 8 days ago

      // Create old selection for target shop
      await prisma.tempSelection.create({
        data: {
          sessionKey: 'old-selection',
          shopId,
          data: JSON.stringify(['old']),
          expiresAt: new Date(Date.now() + 3600000), // Not expired by time
          createdAt: oldDate
        }
      })

      await cleanupExpiredSelections(shopId)

      const oldSelection = await prisma.tempSelection.findUnique({
        where: { sessionKey: 'old-selection' }
      })
      expect(oldSelection).toBeNull()
    })

    it('should handle database errors gracefully during cleanup', async () => {
      const shopId = 'test-shop.myshopify.com'

      // Mock database error
      const originalDeleteMany = prisma.tempSelection.deleteMany
      prisma.tempSelection.deleteMany = vi.fn().mockRejectedValue(new Error('Database cleanup failed'))

      // Should not throw error, just log it
      await expect(cleanupExpiredSelections(shopId)).resolves.toBeUndefined()

      // Restore original function
      prisma.tempSelection.deleteMany = originalDeleteMany
    })
  })
})
