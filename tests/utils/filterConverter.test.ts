import { describe, it, expect } from 'vitest';
import { 
  convertAdvancedFiltersToSearchString, 
  buildCompleteSearchQuery 
} from '~/utils/filterConverter';
import type { FilterValue } from '~/utils/filterFields';

describe('filterConverter - OR Logic Implementation', () => {
  const sampleFilters: FilterValue[] = [
    {
      groupId: 'product_fields',
      fieldId: 'title',
      operator: 'contains',
      value: 'green'
    },
    {
      groupId: 'product_fields',
      fieldId: 'title',
      operator: 'contains',
      value: 'blue'
    },
    {
      groupId: 'product_fields',
      fieldId: 'vendor',
      operator: 'is',
      value: 'Nike'
    }
  ];

  describe('convertAdvancedFiltersToSearchString', () => {
    it('should handle empty filters array', () => {
      expect(convertAdvancedFiltersToSearchString([], 'AND')).toBe('');
      expect(convertAdvancedFiltersToSearchString([], 'OR')).toBe('');
    });

    it('should handle single filter (same for AND/OR)', () => {
      const singleFilter = [sampleFilters[0]];
      const andResult = convertAdvancedFiltersToSearchString(singleFilter, 'AND');
      const orResult = convertAdvancedFiltersToSearchString(singleFilter, 'OR');
      
      expect(andResult).toBe('title:*green*');
      expect(orResult).toBe('title:*green*');
      expect(andResult).toBe(orResult);
    });

    it('should generate correct AND logic (default)', () => {
      const result = convertAdvancedFiltersToSearchString(sampleFilters, 'AND');
      expect(result).toBe('title:*green* title:*blue* vendor:"Nike"');
    });

    it('should generate correct AND logic (implicit default)', () => {
      const result = convertAdvancedFiltersToSearchString(sampleFilters);
      expect(result).toBe('title:*green* title:*blue* vendor:"Nike"');
    });

    it('should generate correct OR logic with parentheses', () => {
      const result = convertAdvancedFiltersToSearchString(sampleFilters, 'OR');
      expect(result).toBe('(title:*green*) OR (title:*blue*) OR (vendor:"Nike")');
    });

    it('should handle filters with different operators', () => {
      const mixedFilters: FilterValue[] = [
        {
          groupId: 'product_fields',
          fieldId: 'title',
          operator: 'is',
          value: 'Exact Title'
        },
        {
          groupId: 'product_fields',
          fieldId: 'vendor',
          operator: 'contains',
          value: 'partial'
        }
      ];

      const andResult = convertAdvancedFiltersToSearchString(mixedFilters, 'AND');
      const orResult = convertAdvancedFiltersToSearchString(mixedFilters, 'OR');

      expect(andResult).toBe('title:"Exact Title" vendor:*partial*');
      expect(orResult).toBe('(title:"Exact Title") OR (vendor:*partial*)');
    });

    it('should skip incomplete filters', () => {
      const incompleteFilters: FilterValue[] = [
        {
          groupId: 'product_fields',
          fieldId: 'title',
          operator: 'contains',
          value: 'valid'
        },
        {
          groupId: 'product_fields',
          fieldId: 'vendor',
          operator: 'contains',
          value: '' // Empty value should be skipped
        },
        {
          groupId: 'product_fields',
          fieldId: '', // Empty fieldId should be skipped
          operator: 'contains',
          value: 'invalid'
        }
      ];

      const result = convertAdvancedFiltersToSearchString(incompleteFilters, 'OR');
      expect(result).toBe('title:*valid*');
    });
  });

  describe('buildCompleteSearchQuery', () => {
    const simpleFilters = [{ value: 'status:ACTIVE' }];
    const searchQuery = 'test search';

    it('should combine search query, simple filters, and advanced filters with AND logic', () => {
      const result = buildCompleteSearchQuery(
        searchQuery,
        simpleFilters,
        sampleFilters,
        'AND'
      );
      
      expect(result).toBe('test search status:ACTIVE title:*green* title:*blue* vendor:"Nike"');
    });

    it('should combine search query, simple filters, and advanced filters with OR logic', () => {
      const result = buildCompleteSearchQuery(
        searchQuery,
        simpleFilters,
        sampleFilters,
        'OR'
      );
      
      expect(result).toBe('test search status:ACTIVE (title:*green*) OR (title:*blue*) OR (vendor:"Nike")');
    });

    it('should default to AND logic when filterLogic not specified', () => {
      const result = buildCompleteSearchQuery(
        searchQuery,
        simpleFilters,
        sampleFilters
      );
      
      expect(result).toBe('test search status:ACTIVE title:*green* title:*blue* vendor:"Nike"');
    });

    it('should handle empty components gracefully', () => {
      expect(buildCompleteSearchQuery('', [], [], 'OR')).toBe('');
      expect(buildCompleteSearchQuery('search', [], [], 'OR')).toBe('search');
      expect(buildCompleteSearchQuery('', simpleFilters, [], 'OR')).toBe('status:ACTIVE');
    });
  });

  describe('Shopify Syntax Validation', () => {
    it('should generate valid Shopify OR syntax', () => {
      const filters: FilterValue[] = [
        {
          groupId: 'product_fields',
          fieldId: 'title',
          operator: 'contains',
          value: 'Caramel Apple'
        },
        {
          groupId: 'product_fields',
          fieldId: 'vendor',
          operator: 'is',
          value: 'Nike'
        }
      ];

      const result = convertAdvancedFiltersToSearchString(filters, 'OR');
      
      // Should match Shopify's documented OR syntax
      expect(result).toBe('(title:*Caramel Apple*) OR (vendor:"Nike")');
    });

    it('should handle complex field mappings correctly', () => {
      const filters: FilterValue[] = [
        {
          groupId: 'product_fields',
          fieldId: 'product_type',
          operator: 'is',
          value: 'clothing'
        },
        {
          groupId: 'product_fields',
          fieldId: 'tags',
          operator: 'contains',
          value: 'summer'
        }
      ];

      const andResult = convertAdvancedFiltersToSearchString(filters, 'AND');
      const orResult = convertAdvancedFiltersToSearchString(filters, 'OR');

      expect(andResult).toBe('product_type:"clothing" tag:summer');
      expect(orResult).toBe('(product_type:"clothing") OR (tag:summer)');
    });
  });
});
