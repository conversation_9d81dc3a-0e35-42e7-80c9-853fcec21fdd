import { describe, it, expect } from 'vitest'
import { sanitizeText, sanitizeAttribute, sanitizeUrl } from '~/utils/sanitization'

describe('Sanitization Utilities', () => {
  describe('sanitizeText', () => {
    it('should remove dangerous HTML characters', () => {
      const input = '<script>alert("xss")</script>Test'
      const result = sanitizeText(input)
      expect(result).toBe('scriptalert(xss)/scriptTest')
      expect(result).not.toContain('<')
      expect(result).not.toContain('>')
      expect(result).not.toContain('"')
    })

    it('should remove control characters', () => {
      const input = 'Test\x00\x01\x1f\x7f\x9fText'
      const result = sanitizeText(input)
      expect(result).toBe('TestText')
    })

    it('should preserve safe characters', () => {
      const input = 'Normal Text 123 !@#$%^*()_+-=[]{}|;:,.?'
      const result = sanitizeText(input)
      expect(result).toBe('Normal Text 123 !@#$%^*()_+-=[]{}|;:,.?')
    })

    it('should handle empty string', () => {
      expect(sanitizeText('')).toBe('')
    })

    it('should handle string with only dangerous characters', () => {
      const input = '<>&"\'\x00\x1f'
      const result = sanitizeText(input)
      expect(result).toBe('')
    })
  })

  describe('sanitizeAttribute', () => {
    it('should remove HTML characters and whitespace', () => {
      const input = '<script>alert("test")</script> with spaces'
      const result = sanitizeAttribute(input)
      expect(result).toBe('scriptalert(test)/scriptwithspaces')
    })

    it('should remove all whitespace characters', () => {
      const input = 'test\t\n\r spaces'
      const result = sanitizeAttribute(input)
      expect(result).toBe('testspaces')
    })

    it('should preserve safe characters without spaces', () => {
      const input = 'test-value_123'
      const result = sanitizeAttribute(input)
      expect(result).toBe('test-value_123')
    })
  })

  describe('sanitizeUrl', () => {
    it('should remove URL-breaking characters', () => {
      const input = 'test<script>#fragment?query=value&other'
      const result = sanitizeUrl(input)
      expect(result).toBe('testscriptfragmentquery=valueother')
    })

    it('should remove hash and query characters', () => {
      const input = 'path#hash?query'
      const result = sanitizeUrl(input)
      expect(result).toBe('pathhashquery')
    })

    it('should preserve safe URL characters', () => {
      const input = 'path/to/resource-123_test.html'
      const result = sanitizeUrl(input)
      expect(result).toBe('path/to/resource-123_test.html')
    })
  })

  describe('Edge Cases', () => {
    it('should handle null-like inputs gracefully', () => {
      // These would normally cause TypeScript errors, but testing runtime behavior
      expect(() => sanitizeText(null as any)).toThrow()
      expect(() => sanitizeText(undefined as any)).toThrow()
    })

    it('should handle very long strings', () => {
      const longString = 'a'.repeat(10000) + '<script>' + 'b'.repeat(10000)
      const result = sanitizeText(longString)
      expect(result).toBe('a'.repeat(10000) + 'script' + 'b'.repeat(10000))
      expect(result).not.toContain('<')
      expect(result).not.toContain('>')
    })

    it('should handle unicode characters safely', () => {
      const input = 'Test 🚀 Unicode ñáéíóú'
      const result = sanitizeText(input)
      expect(result).toBe('Test 🚀 Unicode ñáéíóú')
    })
  })
})
