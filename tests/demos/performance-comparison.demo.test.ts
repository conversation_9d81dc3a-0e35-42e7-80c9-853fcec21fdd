import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { setupSharedCleanDb, setupIsolatedDb, cleanupSharedResources } from '../test-db-strategies'

/**
 * PERFORMANCE COMPARISON TESTS (DEMO ONLY - NOT ESSENTIAL)
 *
 * Purpose: Compare different database strategies for speed
 *
 * This file demonstrates:
 * - Shared database strategy vs Isolated database strategy
 * - Performance timing and measurement
 * - Real-world performance differences between approaches
 *
 * Usage:
 * - Run with: npm run test:demos
 * - Not included in regular test runs (npm run test)
 * - Useful for AI assistants to understand strategy trade-offs
 * - Reference for choosing the right testing strategy
 */

describe('Performance Comparison', () => {
  afterAll(async () => {
    await cleanupSharedResources()
  })

  describe('Shared Database Strategy (FAST)', () => {
    it('should run multiple database operations quickly', async () => {
      const start = Date.now()
      
      const { prisma } = await setupSharedCleanDb()
      
      // Simulate typical test operations
      await prisma.session.create({
        data: {
          id: 'perf-test-1',
          shop: 'perf-shop.myshopify.com',
          state: 'test-state',
          isOnline: false,
          accessToken: 'test-token',
          accountOwner: true
        }
      })
      
      const sessions = await prisma.session.findMany()
      expect(sessions).toHaveLength(1)
      
      const duration = Date.now() - start
      console.log(`Shared DB test took: ${duration}ms`)
      
      // Should be faster than isolated DB (< 3000ms including setup)
      expect(duration).toBeLessThan(3000)
    })
  })

  describe('Isolated Database Strategy (SLOW)', () => {
    it('should run with complete isolation but slower', async () => {
      const start = Date.now()
      
      const { prisma, cleanup } = await setupIsolatedDb()
      
      try {
        // Same operations as above
        await prisma.session.create({
          data: {
            id: 'perf-test-2',
            shop: 'perf-shop-2.myshopify.com',
            state: 'test-state',
            isOnline: false,
            accessToken: 'test-token',
            accountOwner: true
          }
        })
        
        const sessions = await prisma.session.findMany()
        expect(sessions).toHaveLength(1)
        
        const duration = Date.now() - start
        console.log(`Isolated DB test took: ${duration}ms`)
        
        // Will be slower due to schema creation
        expect(duration).toBeGreaterThan(100)
      } finally {
        await cleanup()
      }
    })
  })
})
