import { describe, it, expect, beforeAll, beforeEach, afterAll } from 'vitest'
import { setupSharedCleanDb, cleanupSharedResources } from '../test-db-strategies'
import { PrismaClient } from '@prisma/client'

/**
 * SPEED DEMONSTRATION (DEMO ONLY - NOT ESSENTIAL)
 *
 * Purpose: Shows how fast tests can be with shared database strategy
 *
 * This file demonstrates:
 * - 10 identical tests running in ~2.5 seconds
 * - Shared database with table cleanup between tests
 * - Performance benefits of the optimized testing approach
 *
 * Usage:
 * - Run with: npm run test:demos
 * - Not included in regular test runs (npm run test)
 * - Useful for AI assistants to understand performance characteristics
 * - Reference for implementing similar fast test patterns
 */

describe('Speed Demo: 10 Fast Tests', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    const setup = await setupSharedCleanDb()
    prisma = setup.prisma
  })

  beforeEach(async () => {
    await setupSharedCleanDb() // Clean tables only (fast)
  })

  afterAll(async () => {
    await cleanupSharedResources()
  })

  // 10 identical tests to show speed
  it('test 1: should create session quickly', async () => {
    const session = await prisma.session.create({
      data: {
        id: 'test-1',
        shop: 'shop1.myshopify.com',
        state: 'state1',
        isOnline: false,
        accessToken: 'token1',
        accountOwner: true
      }
    })
    expect(session.id).toBe('test-1')
  })

  it('test 2: should create session quickly', async () => {
    const session = await prisma.session.create({
      data: {
        id: 'test-2',
        shop: 'shop2.myshopify.com',
        state: 'state2',
        isOnline: false,
        accessToken: 'token2',
        accountOwner: true
      }
    })
    expect(session.id).toBe('test-2')
  })

  it('test 3: should create session quickly', async () => {
    const session = await prisma.session.create({
      data: {
        id: 'test-3',
        shop: 'shop3.myshopify.com',
        state: 'state3',
        isOnline: false,
        accessToken: 'token3',
        accountOwner: true
      }
    })
    expect(session.id).toBe('test-3')
  })

  it('test 4: should create session quickly', async () => {
    const session = await prisma.session.create({
      data: {
        id: 'test-4',
        shop: 'shop4.myshopify.com',
        state: 'state4',
        isOnline: false,
        accessToken: 'token4',
        accountOwner: true
      }
    })
    expect(session.id).toBe('test-4')
  })

  it('test 5: should create session quickly', async () => {
    const session = await prisma.session.create({
      data: {
        id: 'test-5',
        shop: 'shop5.myshopify.com',
        state: 'state5',
        isOnline: false,
        accessToken: 'token5',
        accountOwner: true
      }
    })
    expect(session.id).toBe('test-5')
  })

  it('test 6: should create session quickly', async () => {
    const session = await prisma.session.create({
      data: {
        id: 'test-6',
        shop: 'shop6.myshopify.com',
        state: 'state6',
        isOnline: false,
        accessToken: 'token6',
        accountOwner: true
      }
    })
    expect(session.id).toBe('test-6')
  })

  it('test 7: should create session quickly', async () => {
    const session = await prisma.session.create({
      data: {
        id: 'test-7',
        shop: 'shop7.myshopify.com',
        state: 'state7',
        isOnline: false,
        accessToken: 'token7',
        accountOwner: true
      }
    })
    expect(session.id).toBe('test-7')
  })

  it('test 8: should create session quickly', async () => {
    const session = await prisma.session.create({
      data: {
        id: 'test-8',
        shop: 'shop8.myshopify.com',
        state: 'state8',
        isOnline: false,
        accessToken: 'token8',
        accountOwner: true
      }
    })
    expect(session.id).toBe('test-8')
  })

  it('test 9: should create session quickly', async () => {
    const session = await prisma.session.create({
      data: {
        id: 'test-9',
        shop: 'shop9.myshopify.com',
        state: 'state9',
        isOnline: false,
        accessToken: 'token9',
        accountOwner: true
      }
    })
    expect(session.id).toBe('test-9')
  })

  it('test 10: should create session quickly', async () => {
    const session = await prisma.session.create({
      data: {
        id: 'test-10',
        shop: 'shop10.myshopify.com',
        state: 'state10',
        isOnline: false,
        accessToken: 'token10',
        accountOwner: true
      }
    })
    expect(session.id).toBe('test-10')
  })
})
