import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { setupTestEnv } from './setup'

// Mock Bull queue using vi.hoisted to ensure proper hoisting
const { mockQueue, mockBull } = vi.hoisted(() => {
  const mockQueue = {
    add: vi.fn().mockResolvedValue({ id: 'mock-bull-job-id' }),
    process: vi.fn(),
    close: vi.fn(),
    clean: vi.fn(),
    getJobs: vi.fn(),
    getJob: vi.fn(),
    getWaiting: vi.fn().mockResolvedValue([]),
    on: vi.fn(),
  }

  const mockBull = vi.fn(() => mockQueue)

  return { mockQueue, mockBull }
})

vi.mock('bull', () => ({
  default: mockBull,
}))

// Mock Redis
const mockRedis = vi.fn(() => ({
  set: vi.fn(),
  get: vi.fn(),
  del: vi.fn(),
  disconnect: vi.fn(),
  keys: vi.fn().mockResolvedValue([]),
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
}))

vi.mock('ioredis', () => ({
  default: mockRedis,
}))

// Mock job processor
const mockProcessJob = vi.fn()
const mockShouldMoveToDeadLetter = vi.fn()
const mockHandleDeadLetterJob = vi.fn()
const mockClassifyError = vi.fn()
vi.mock('~/services/jobProcessor.server', () => ({
  processJob: mockProcessJob,
  shouldMoveToDeadLetter: mockShouldMoveToDeadLetter,
  handleDeadLetterJob: mockHandleDeadLetterJob,
  classifyError: mockClassifyError,
}))

// Mock database
const mockDb = {
  job: {
    findMany: vi.fn(),
    findUnique: vi.fn(),
    update: vi.fn(),
  },
}

vi.mock('~/db.server', () => ({
  default: mockDb,
}))

describe('Background Worker', () => {
  beforeEach(async () => {
    setupTestEnv()
    vi.clearAllMocks()

    // Reset mock to default behavior
    mockQueue.add.mockResolvedValue({ id: 'mock-bull-job-id' })
    mockQueue.process.mockImplementation(() => {})
    mockQueue.getWaiting.mockResolvedValue([])
    mockQueue.close.mockResolvedValue(undefined)
    mockQueue.clean.mockResolvedValue([])
    mockQueue.getJobs.mockResolvedValue([])
    mockQueue.getJob.mockResolvedValue(null)
    mockQueue.on.mockImplementation(() => {})

    mockProcessJob.mockResolvedValue({
      success: true,
      jobId: 'job-123',
      processedProducts: 2,
      successfulUpdates: 2,
      failedUpdates: 0,
    })
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('Queue Discovery', () => {
    it('should discover existing shop queues from Redis', async () => {
      // Mock Redis constructor to return instance with keys method
      mockRedis.mockImplementation(() => ({
        keys: vi.fn().mockResolvedValue([
          'bull:bpe-s-shop1.myshopify.com:waiting',
          'bull:bpe-s-shop2.myshopify.com:active',
          'bull:bpe-s-shop3.myshopify.com:completed',
        ]),
        disconnect: vi.fn(),
        set: vi.fn(),
        get: vi.fn(),
        del: vi.fn(),
        on: vi.fn(),
        off: vi.fn(),
        emit: vi.fn(),
      }))

      // Import worker after mocks are set up
      const { discoverShopQueues } = await import('../workers/worker.server')

      const shopDomains = await discoverShopQueues()

      expect(shopDomains).toEqual([
        's-shop1.myshopify.com',
        's-shop2.myshopify.com',
        's-shop3.myshopify.com'
      ])
    })

    it('should handle empty Redis keys gracefully', async () => {
      mockRedis.mockImplementation(() => ({
        keys: vi.fn().mockResolvedValue([]),
        disconnect: vi.fn(),
        set: vi.fn(),
        get: vi.fn(),
        del: vi.fn(),
        on: vi.fn(),
        off: vi.fn(),
        emit: vi.fn(),
      }))

      const { discoverShopQueues } = await import('../workers/worker.server')

      const shopDomains = await discoverShopQueues()

      expect(shopDomains).toEqual([])
    })

    it('should handle non-iterable Redis keys response gracefully', async () => {
      mockRedis.mockImplementation(() => ({
        keys: vi.fn().mockResolvedValue(null), // Redis returns null instead of array
        disconnect: vi.fn(),
        set: vi.fn(),
        get: vi.fn(),
        del: vi.fn(),
        on: vi.fn(),
        off: vi.fn(),
        emit: vi.fn(),
      }))

      const { discoverShopQueues } = await import('../workers/worker.server')

      const shopDomains = await discoverShopQueues()

      expect(shopDomains).toEqual([])
    })

    it('should handle undefined Redis keys response gracefully', async () => {
      mockRedis.mockImplementation(() => ({
        keys: vi.fn().mockResolvedValue(undefined), // Redis returns undefined
        disconnect: vi.fn(),
        set: vi.fn(),
        get: vi.fn(),
        del: vi.fn(),
        on: vi.fn(),
        off: vi.fn(),
        emit: vi.fn(),
      }))

      const { discoverShopQueues } = await import('../workers/worker.server')

      const shopDomains = await discoverShopQueues()

      expect(shopDomains).toEqual([])
    })
  })

  describe('Job Processing Setup', () => {
    it('should set up job processors for discovered shop queues', async () => {
      const { setupJobProcessors } = await import('../workers/worker.server')

      const shopDomains = ['shop1.myshopify.com', 'shop2.myshopify.com']
      await setupJobProcessors(shopDomains)

      // Should create queues for each shop
      expect(mockBull).toHaveBeenCalledWith(
        'bpe-shop1.myshopify.com',
        expect.objectContaining({
          redis: expect.any(Object),
        })
      )
      expect(mockBull).toHaveBeenCalledWith(
        'bpe-shop2.myshopify.com',
        expect.objectContaining({
          redis: expect.any(Object),
        })
      )

      // Should set up processors for each queue
      expect(mockQueue.process).toHaveBeenCalledTimes(2)
      expect(mockQueue.process).toHaveBeenCalledWith('bulk-update', expect.any(Function))
    })

    it('should handle job processing with proper error handling', async () => {
      const { setupJobProcessors } = await import('../workers/worker.server')

      const shopDomains = ['shop1.myshopify.com']
      await setupJobProcessors(shopDomains)

      // Get the processor function that was passed to queue.process (simplified format)
      const processorFunction = mockQueue.process.mock.calls[0][1]

      const mockJob = {
        id: 'bull-job-123',
        data: {
          jobId: 'job-123',
          shopDomain: 'shop1.myshopify.com',
          modifications: [{ fieldType: 'product', fieldName: 'title', fieldValue: 'New Title' }],
          productIds: ['gid://shopify/Product/1'],
          options: { batchSize: 10, delayBetweenBatches: 1000 },
        },
      }

      await processorFunction(mockJob)

      expect(mockProcessJob).toHaveBeenCalledWith(mockJob.data)
    })
  })

  describe('Graceful Shutdown', () => {
    it('should close all queues on shutdown', async () => {
      const { setupJobProcessors, shutdown } = await import('../workers/worker.server')

      const shopDomains = ['shop1.myshopify.com', 'shop2.myshopify.com']
      await setupJobProcessors(shopDomains)

      await shutdown()

      // Should close all queues
      expect(mockQueue.close).toHaveBeenCalledTimes(2)
    })
  })

  describe('Error Handling', () => {
    it('should handle job processing errors gracefully', async () => {
      const processingError = new Error('Processing failed')
      mockProcessJob.mockRejectedValueOnce(processingError)
      mockShouldMoveToDeadLetter.mockReturnValue(false)
      mockClassifyError.mockReturnValue({
        category: 'UNKNOWN',
        isRetryable: false,
        shouldMoveToDeadLetter: false
      })

      const { setupJobProcessors } = await import('../workers/worker.server')

      const shopDomains = ['shop1.myshopify.com']
      await setupJobProcessors(shopDomains)

      const processorFunction = mockQueue.process.mock.calls[0][1]

      const mockJob = {
        id: 'bull-job-123',
        data: {
          jobId: 'job-123',
          shopDomain: 'shop1.myshopify.com',
          modifications: [],
          productIds: [],
          options: { batchSize: 10, delayBetweenBatches: 1000 },
        },
      }

      // Should not throw error, but handle it gracefully
      await expect(processorFunction(mockJob)).rejects.toThrow('Processing failed')
    })
  })
})
