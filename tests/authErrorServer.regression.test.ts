import { describe, it, expect, vi } from 'vitest'

// Mock the Shopify types and enums
const mockLoginErrorType = {
  MissingShop: 'MissingShop',
  InvalidShop: 'InvalidShop'
}

vi.mock('@shopify/shopify-app-remix/server', () => ({
  LoginErrorType: mockLoginErrorType
}))

describe('Auth Error Server Regression Tests', () => {
  describe('loginErrorMessage', () => {
    it('should return missing shop error message', async () => {
      const { loginErrorMessage } = await import('~/routes/auth.login/error.server')

      const loginError = {
        shop: mockLoginErrorType.MissingShop as any
      }

      const result = loginErrorMessage(loginError)

      expect(result).toEqual({
        shop: "Please enter your shop domain to log in"
      })
    })

    it('should return invalid shop error message', async () => {
      const { loginErrorMessage } = await import('~/routes/auth.login/error.server')

      const loginError = {
        shop: mockLoginErrorType.InvalidShop as any
      }

      const result = loginErrorMessage(loginError)

      expect(result).toEqual({
        shop: "Please enter a valid shop domain to log in"
      })
    })

    it('should return empty object for no errors', async () => {
      const { loginErrorMessage } = await import('~/routes/auth.login/error.server')
      
      const loginError = {}
      
      const result = loginErrorMessage(loginError)
      
      expect(result).toEqual({})
    })

    it('should return empty object for null/undefined errors', async () => {
      const { loginErrorMessage } = await import('~/routes/auth.login/error.server')
      
      const result1 = loginErrorMessage(null as any)
      const result2 = loginErrorMessage(undefined as any)
      
      expect(result1).toEqual({})
      expect(result2).toEqual({})
    })

    it('should return empty object for unknown error types', async () => {
      const { loginErrorMessage } = await import('~/routes/auth.login/error.server')

      const loginError = {
        shop: 'UnknownErrorType' as any
      }

      const result = loginErrorMessage(loginError)

      expect(result).toEqual({})
    })

    it('should handle errors with other properties', async () => {
      const { loginErrorMessage } = await import('~/routes/auth.login/error.server')

      const loginError = {
        shop: mockLoginErrorType.MissingShop as any,
        otherProperty: 'some value'
      }

      const result = loginErrorMessage(loginError)

      expect(result).toEqual({
        shop: "Please enter your shop domain to log in"
      })
    })

    it('should handle shop property with falsy values', async () => {
      const { loginErrorMessage } = await import('~/routes/auth.login/error.server')
      
      const testCases = [
        { shop: null },
        { shop: undefined },
        { shop: '' },
        { shop: 0 },
        { shop: false }
      ]
      
      testCases.forEach(loginError => {
        const result = loginErrorMessage(loginError as any)
        expect(result).toEqual({})
      })
    })

    it('should be case sensitive for error types', async () => {
      const { loginErrorMessage } = await import('~/routes/auth.login/error.server')
      
      const testCases = [
        { shop: 'missingshop' }, // lowercase
        { shop: 'MISSINGSHOP' }, // uppercase
        { shop: 'MissingShop ' }, // with space
        { shop: ' MissingShop' }, // with leading space
        { shop: 'invalidshop' },
        { shop: 'INVALIDSHOP' }
      ]
      
      testCases.forEach(loginError => {
        const result = loginErrorMessage(loginError as any)
        expect(result).toEqual({})
      })
    })

    it('should handle complex error objects', async () => {
      const { loginErrorMessage } = await import('~/routes/auth.login/error.server')
      
      const complexError = {
        shop: mockLoginErrorType.InvalidShop as any,
        nested: {
          property: 'value'
        },
        array: [1, 2, 3],
        timestamp: new Date(),
        metadata: {
          source: 'test',
          version: '1.0.0'
        }
      }

      const result = loginErrorMessage(complexError)
      
      expect(result).toEqual({
        shop: "Please enter a valid shop domain to log in"
      })
    })

    it('should validate return type structure', async () => {
      const { loginErrorMessage } = await import('~/routes/auth.login/error.server')
      
      const loginError = {
        shop: mockLoginErrorType.MissingShop as any
      }

      const result = loginErrorMessage(loginError)
      
      // Verify return type structure
      expect(typeof result).toBe('object')
      expect(result).not.toBeNull()
      expect(Array.isArray(result)).toBe(false)
      
      // Verify only expected properties
      const keys = Object.keys(result)
      expect(keys).toHaveLength(1)
      expect(keys[0]).toBe('shop')
      
      // Verify property type
      expect(typeof result.shop).toBe('string')
    })

    it('should handle edge case with shop property as object', async () => {
      const { loginErrorMessage } = await import('~/routes/auth.login/error.server')
      
      const loginError = {
        shop: { type: mockLoginErrorType.MissingShop }
      }
      
      const result = loginErrorMessage(loginError as any)
      
      expect(result).toEqual({})
    })

    it('should handle edge case with shop property as array', async () => {
      const { loginErrorMessage } = await import('~/routes/auth.login/error.server')
      
      const loginError = {
        shop: [mockLoginErrorType.InvalidShop]
      }
      
      const result = loginErrorMessage(loginError as any)
      
      expect(result).toEqual({})
    })

    it('should be consistent across multiple calls', async () => {
      const { loginErrorMessage } = await import('~/routes/auth.login/error.server')
      
      const loginError = {
        shop: mockLoginErrorType.MissingShop as any
      }

      const result1 = loginErrorMessage(loginError)
      const result2 = loginErrorMessage(loginError)
      const result3 = loginErrorMessage(loginError)
      
      expect(result1).toEqual(result2)
      expect(result2).toEqual(result3)
      expect(result1).toEqual({
        shop: "Please enter your shop domain to log in"
      })
    })

    it('should not mutate input object', async () => {
      const { loginErrorMessage } = await import('~/routes/auth.login/error.server')
      
      const originalError = {
        shop: mockLoginErrorType.InvalidShop as any,
        otherProp: 'original value'
      }

      const originalErrorCopy = { ...originalError }

      loginErrorMessage(originalError)
      
      expect(originalError).toEqual(originalErrorCopy)
    })
  })
})
