import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock the flatRoutes function
const mockFlatRoutes = vi.fn(() => ({
  'app._index': { file: 'app._index.tsx' },
  'app.jobs._index': { file: 'app.jobs._index.tsx' },
  'app.select-products': { file: 'app.select-products.tsx' },
  'app.define-modifications': { file: 'app.define-modifications.tsx' },
  'app.create-job': { file: 'app.create-job.tsx' },
  'api.jobs': { file: 'api.jobs.ts' },
  'api.stats': { file: 'api.stats.ts' }
}))

vi.mock('@remix-run/fs-routes', () => ({
  flatRoutes: mockFlatRoutes
}))

describe('Routes Configuration Regression Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.resetModules()
  })

  describe('route configuration', () => {
    it('should call flatRoutes function', async () => {
      await import('~/routes')
      
      expect(mockFlatRoutes).toHaveBeenCalledTimes(1)
      expect(mockFlatRoutes).toHaveBeenCalledWith()
    })

    it('should export the result of flatRoutes as default', async () => {
      const routes = await import('~/routes')
      
      expect(routes.default).toEqual({
        'app._index': { file: 'app._index.tsx' },
        'app.jobs._index': { file: 'app.jobs._index.tsx' },
        'app.select-products': { file: 'app.select-products.tsx' },
        'app.define-modifications': { file: 'app.define-modifications.tsx' },
        'app.create-job': { file: 'app.create-job.tsx' },
        'api.jobs': { file: 'api.jobs.ts' },
        'api.stats': { file: 'api.stats.ts' }
      })
    })

    it('should handle empty routes configuration', async () => {
      ;(mockFlatRoutes as any).mockReturnValueOnce({})

      vi.resetModules()

      const routes = await import('~/routes')

      expect(routes.default).toEqual({})
    })

    it('should handle complex route structures', async () => {
      const complexRoutes = {
        'app._index': {
          file: 'app._index.tsx',
          id: 'app._index',
          path: '/app'
        },
        'app.jobs._index': {
          file: 'app.jobs._index.tsx',
          id: 'app.jobs._index',
          path: '/app/jobs'
        },
        'app.jobs.$jobId': {
          file: 'app.jobs.$jobId.tsx',
          id: 'app.jobs.$jobId',
          path: '/app/jobs/:jobId'
        }
      }

      ;(mockFlatRoutes as any).mockReturnValueOnce(complexRoutes)
      
      vi.resetModules()
      
      const routes = await import('~/routes')
      
      expect(routes.default).toEqual(complexRoutes)
    })

    it('should maintain route configuration consistency', async () => {
      // Import multiple times to ensure consistency
      const routes1 = await import('~/routes')
      
      vi.resetModules()
      
      const routes2 = await import('~/routes')
      
      expect(routes1.default).toEqual(routes2.default)
      expect(mockFlatRoutes).toHaveBeenCalledTimes(2)
    })
  })

  describe('flatRoutes integration', () => {
    it('should pass no arguments to flatRoutes', async () => {
      await import('~/routes')
      
      expect(mockFlatRoutes).toHaveBeenCalledWith()
    })

    it('should handle flatRoutes throwing an error', async () => {
      mockFlatRoutes.mockImplementationOnce(() => {
        throw new Error('Route configuration error')
      })
      
      vi.resetModules()
      
      await expect(import('~/routes')).rejects.toThrow('Route configuration error')
    })

    it('should handle flatRoutes returning null', async () => {
      ;(mockFlatRoutes as any).mockReturnValueOnce(null)

      vi.resetModules()

      const routes = await import('~/routes')

      expect(routes.default).toBeNull()
    })

    it('should handle flatRoutes returning undefined', async () => {
      ;(mockFlatRoutes as any).mockReturnValueOnce(undefined)

      vi.resetModules()

      const routes = await import('~/routes')

      expect(routes.default).toBeUndefined()
    })
  })

  describe('module structure', () => {
    it('should export only default export', async () => {
      const routes = await import('~/routes')
      
      const exports = Object.keys(routes)
      expect(exports).toEqual(['default'])
    })

    it('should not have any named exports', async () => {
      const routes = await import('~/routes')
      
      // Should only have default export
      expect(Object.keys(routes).filter(key => key !== 'default')).toHaveLength(0)
    })
  })

  describe('route structure validation', () => {
    it('should handle typical Shopify app routes', async () => {
      const shopifyRoutes = {
        'app._index': { file: 'app._index.tsx' },
        'app.select-products': { file: 'app.select-products.tsx' },
        'app.define-modifications': { file: 'app.define-modifications.tsx' },
        'app.create-job': { file: 'app.create-job.tsx' },
        'app.jobs._index': { file: 'app.jobs._index.tsx' },
        'app.jobs.$jobId': { file: 'app.jobs.$jobId.tsx' },
        'api.jobs': { file: 'api.jobs.ts' },
        'api.stats': { file: 'api.stats.ts' },
        'auth.$': { file: 'auth.$.tsx' },
        'webhooks.app.uninstalled': { file: 'webhooks.app.uninstalled.tsx' },
        'webhooks.app.scopes_update': { file: 'webhooks.app.scopes_update.tsx' }
      }
      
      mockFlatRoutes.mockReturnValueOnce(shopifyRoutes)
      
      vi.resetModules()
      
      const routes = await import('~/routes')
      
      expect(routes.default).toEqual(shopifyRoutes)
      
      // Verify it includes expected route patterns
      expect(routes.default).toHaveProperty('app._index')
      expect(routes.default).toHaveProperty('api.jobs')
      expect(routes.default).toHaveProperty('auth.$')
      expect(routes.default).toHaveProperty('webhooks.app.uninstalled')
    })

    it('should handle nested route structures', async () => {
      const nestedRoutes = {
        'app': {
          file: 'app.tsx',
          children: {
            '_index': { file: 'app._index.tsx' },
            'jobs': {
              file: 'app.jobs.tsx',
              children: {
                '_index': { file: 'app.jobs._index.tsx' },
                '$jobId': { file: 'app.jobs.$jobId.tsx' }
              }
            }
          }
        }
      }
      
      ;(mockFlatRoutes as any).mockReturnValueOnce(nestedRoutes)

      vi.resetModules()

      const routes = await import('~/routes')

      expect(routes.default).toEqual(nestedRoutes)
    })

    it('should handle routes with special characters', async () => {
      const specialRoutes = {
        'app.jobs.$jobId': { file: 'app.jobs.$jobId.tsx' },
        'auth.$': { file: 'auth.$.tsx' },
        'webhooks.app.scopes_update': { file: 'webhooks.app.scopes_update.tsx' },
        'api.products._index': { file: 'api.products._index.ts' }
      }

      ;(mockFlatRoutes as any).mockReturnValueOnce(specialRoutes)
      
      vi.resetModules()
      
      const routes = await import('~/routes')
      
      expect(routes.default).toEqual(specialRoutes)
    })
  })

  describe('error scenarios', () => {
    it('should propagate flatRoutes errors', async () => {
      const error = new Error('File system error')
      mockFlatRoutes.mockImplementationOnce(() => {
        throw error
      })
      
      vi.resetModules()
      
      await expect(import('~/routes')).rejects.toThrow('File system error')
    })

    it('should handle flatRoutes returning invalid data types', async () => {
      // Test with string instead of object
      ;(mockFlatRoutes as any).mockReturnValueOnce('invalid-routes')

      vi.resetModules()

      const routes = await import('~/routes')

      expect(routes.default).toBe('invalid-routes')
    })

    it('should handle flatRoutes returning array', async () => {
      const arrayRoutes = ['route1', 'route2', 'route3']
      ;(mockFlatRoutes as any).mockReturnValueOnce(arrayRoutes)

      vi.resetModules()

      const routes = await import('~/routes')

      expect(routes.default).toEqual(arrayRoutes)
    })
  })

  describe('performance considerations', () => {
    it('should call flatRoutes only once per import', async () => {
      await import('~/routes')
      
      expect(mockFlatRoutes).toHaveBeenCalledTimes(1)
    })

    it('should not cache results across module resets', async () => {
      await import('~/routes')
      expect(mockFlatRoutes).toHaveBeenCalledTimes(1)
      
      vi.resetModules()
      
      await import('~/routes')
      expect(mockFlatRoutes).toHaveBeenCalledTimes(2)
    })
  })
})
