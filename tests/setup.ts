import { PrismaClient } from '@prisma/client'
import { randomBytes } from 'crypto'
import { execSync } from 'child_process'
import { readFileSync, writeFileSync, unlinkSync, mkdirSync, rmSync } from 'fs'
import { join, resolve } from 'path'
import { vi } from 'vitest'

// Mock React with all required types and exports
vi.mock('react', () => ({
  Component: class Component {
    props: any
    constructor(props: any) { this.props = props }
    render() { return null }
  },
  PureComponent: class PureComponent {
    props: any
    constructor(props: any) { this.props = props }
    render() { return null }
  },
  createContext: vi.fn(() => ({
    Provider: vi.fn(({ children }) => children),
    Consumer: vi.fn(({ children }) => children && children({})),
    displayName: 'MockContext'
  })),
  useContext: vi.fn(() => ({})),
  useState: vi.fn(() => [null, vi.fn()]),
  useEffect: vi.fn(),
  useLayoutEffect: vi.fn(),
  useCallback: vi.fn((fn) => fn),
  useMemo: vi.fn((fn) => fn()),
  useRef: vi.fn(() => ({ current: null })),
  useReducer: vi.fn(() => [{}, vi.fn()]),
  useImperativeHandle: vi.fn(),
  createElement: vi.fn((type, props, ...children) => ({ type, props, children })),
  cloneElement: vi.fn(),
  Fragment: 'Fragment',
  StrictMode: vi.fn(({ children }) => children),
  Suspense: vi.fn(({ children }) => children),
  memo: vi.fn((component) => component),
  forwardRef: vi.fn((component) => component),
  lazy: vi.fn(),
  isValidElement: vi.fn(() => true),
  Children: {
    map: vi.fn(),
    forEach: vi.fn(),
    count: vi.fn(),
    toArray: vi.fn(),
    only: vi.fn()
  },
  default: {
    createElement: vi.fn((type, props, ...children) => ({ type, props, children })),
    Component: class Component {},
    Fragment: 'Fragment'
  },
}))

// Mock ToastContext directly to avoid React mock issues
vi.mock('~/contexts/ToastContext', () => ({
  useToast: vi.fn(() => ({
    showSuccess: vi.fn(),
    showError: vi.fn(),
    showWarning: vi.fn(),
    dismissToast: vi.fn(),
    dismissAll: vi.fn(),
    toasts: [],
  })),
  ToastProvider: vi.fn(({ children }) => children),
  useJobToast: vi.fn(() => ({
    showJobStarted: vi.fn(),
    showJobCompleted: vi.fn(),
    showJobFailed: vi.fn(),
    showJobProgress: vi.fn(),
  })),
  getErrorMessage: vi.fn((error) => {
    if (typeof error === 'string') return error
    if (error instanceof Error) return error.message
    if (error && typeof error === 'object' && 'message' in error) return error.message
    return 'An unknown error occurred'
  }),
  isNetworkError: vi.fn((error) => {
    if (error instanceof Error) {
      const message = error.message.toLowerCase()
      return message.includes('network') || message.includes('fetch') || message.includes('connection')
    }
    if (error && typeof error === 'object' && 'message' in error) {
      const message = error.message.toLowerCase()
      return message.includes('network') || message.includes('fetch') || message.includes('connection')
    }
    return false
  }),
}))

// Mock Bull queue
vi.mock('bull', () => ({
  default: vi.fn(() => ({
    add: vi.fn().mockResolvedValue({ id: 'test-job-id' }),
    process: vi.fn(),
    on: vi.fn(),
    getJob: vi.fn(),
    getJobs: vi.fn(() => Promise.resolve([])),
    clean: vi.fn(),
    close: vi.fn(),
    ready: vi.fn().mockResolvedValue(true),
  })),
}))

// Mock Redis
vi.mock('ioredis', () => ({
  default: vi.fn(() => ({
    get: vi.fn(),
    set: vi.fn(),
    del: vi.fn(),
    exists: vi.fn(),
    expire: vi.fn(),
    disconnect: vi.fn(),
    on: vi.fn(),
    ready: true,
  })),
}))

// Mock Remix node functions
vi.mock('@remix-run/node', () => ({
  redirect: vi.fn((url) => new Response(null, { status: 302, headers: { Location: url } })),
  json: vi.fn((data) => new Response(JSON.stringify(data), { headers: { 'Content-Type': 'application/json' } })),
  defer: vi.fn((data) => data),
}))

// Test database directory (absolute path to avoid working directory issues)
const TEST_DB_DIR = resolve('./prisma/test-dbs')

// Set up global cleanup on process exit
let cleanupHandlerSet = false
function ensureCleanupHandler() {
  if (!cleanupHandlerSet) {
    const cleanup = () => {
      cleanupOldTestDatabases()
    }

    process.on('exit', cleanup)
    process.on('SIGINT', cleanup)
    process.on('SIGTERM', cleanup)
    process.on('beforeExit', cleanup)
    cleanupHandlerSet = true
  }
}

// Clean up all test databases by removing the entire test directory
export function cleanupOldTestDatabases() {
  try {
    rmSync(TEST_DB_DIR, { recursive: true, force: true })
    console.log('🧹 Cleaned up test database directory')
  } catch (e) {
    // Directory might not exist, ignore
  }
}

// Set up test environment variables
// Note: These only affect the test process, not your dev server
export function setupTestEnv() {
  // Set up cleanup handler
  ensureCleanupHandler()

  // Only set if not already in test mode to avoid conflicts
  if (process.env.NODE_ENV !== 'test') {
    process.env.NODE_ENV = 'test'
  }

  // Use test-specific values that won't interfere with dev
  process.env.SHOPIFY_API_KEY = 'test-api-key-for-testing'
  process.env.SHOPIFY_API_SECRET = 'test-api-secret-for-testing'
  process.env.SHOPIFY_APP_URL = 'http://localhost:3001' // Different port from dev
  process.env.SCOPES = 'write_products'

  // Ensure we don't accidentally connect to dev database
  // (This will be overridden per test anyway)
  process.env.DATABASE_URL = 'file:./test-should-not-be-used.sqlite'
}

// Create isolated test database for each test run
export async function setupTestDatabase() {
  // Set up environment first
  setupTestEnv()

  // Always clean up old test databases before creating new ones
  cleanupOldTestDatabases()

  // Ensure test directory exists
  try {
    mkdirSync(TEST_DB_DIR, { recursive: true })
  } catch (e) {
    // Directory might already exist, ignore
  }

  const testDbName = `test_${randomBytes(8).toString('hex')}.sqlite`
  const testDbPath = join(TEST_DB_DIR, testDbName)
  const testDbUrl = `file:${testDbPath}`

  // Set test database URL
  process.env.DATABASE_URL = testDbUrl

  // Clear global Prisma client to avoid conflicts
  if (global.prismaGlobal) {
    await global.prismaGlobal.$disconnect()
    ;(global as any).prismaGlobal = undefined
  }

  // Create a temporary schema file with our test database URL
  // This is the only reliable way to override Prisma's .env file behavior
  const originalSchema = readFileSync('./prisma/schema.prisma', 'utf8')
  const tempSchemaFile = `./prisma/schema.test.${randomBytes(4).toString('hex')}.prisma`

  // Replace the datasource URL in the schema
  const testSchema = originalSchema.replace(
    /url\s*=\s*env\("[^"]*"\)/,
    `url = "${testDbUrl}"`
  )

  writeFileSync(tempSchemaFile, testSchema)

  try {
    // Use the temporary schema file
    execSync(`npx prisma db push --force-reset --accept-data-loss --schema=${tempSchemaFile}`, {
      stdio: 'pipe',
      encoding: 'utf8'
    })
  } catch (error: any) {
    console.error('Failed to sync schema to test database:', error)
    console.error('DATABASE_URL:', testDbUrl)
    console.error('Error output:', error.stdout?.toString())
    console.error('Error stderr:', error.stderr?.toString())
    throw error
  } finally {
    // Clean up temporary schema file
    try {
      unlinkSync(tempSchemaFile)
    } catch (e) {
      // Ignore cleanup errors
    }
  }

  // Create Prisma client for test database
  const prisma = new PrismaClient({
    datasources: { db: { url: testDbUrl } }
  })

  // Override the global Prisma client to use the test database
  ;(global as any).prismaGlobal = prisma

  return { prisma, testDbName: testDbPath }
}

// Cleanup test database
export async function cleanupTestDatabase(prisma: PrismaClient, testDbName: string) {
  // Disconnect Prisma client first
  try {
    await prisma.$disconnect()
  } catch (e) {
    // Ignore disconnect errors
  }

  // Clear global Prisma client
  if (global.prismaGlobal) {
    try {
      await global.prismaGlobal.$disconnect()
    } catch (e) {
      // Ignore disconnect errors
    }
    ;(global as any).prismaGlobal = undefined
  }

  // Remove test database file and journal
  try {
    unlinkSync(testDbName)
  } catch (e) {
    // Ignore if file doesn't exist
  }

  // Also remove journal file if it exists
  try {
    unlinkSync(`${testDbName}-journal`)
  } catch (e) {
    // Ignore if file doesn't exist
  }
}
