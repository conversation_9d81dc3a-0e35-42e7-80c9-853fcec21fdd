import { describe, it, expect, vi, beforeEach } from 'vitest';
import { getProductsCountFromShopify } from '~/data/graphql/getProducts';

describe('getProductsCountFromShopify - Regression Tests for Product Count API', () => {
  let mockAdmin: any;
  let mockGraphql: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockGraphql = vi.fn();
    mockAdmin = {
      graphql: mockGraphql,
    };
  });

  describe('Input Validation (Admin Client Required)', () => {
    it('should throw error when admin client is not provided', async () => {
      await expect(
        getProductsCountFromShopify({
          admin: null,
          searchQuery: 'test',
        })
      ).rejects.toThrow('Admin GraphQL client is required');
    });

    it('should throw error when admin client is undefined', async () => {
      await expect(
        getProductsCountFromShopify({
          admin: undefined,
          searchQuery: 'test',
        })
      ).rejects.toThrow('Admin GraphQL client is required');
    });

    it('should accept valid admin client', async () => {
      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          data: {
            productsCount: {
              count: 10,
              precision: 'EXACT',
            },
          },
        }),
      });

      const result = await getProductsCountFromShopify({
        admin: mockAdmin,
        searchQuery: 'test',
      });

      expect(result).toEqual({
        count: 10,
        precision: 'EXACT',
      });
    });
  });

  describe('Query Sanitization (1000 Character Limit)', () => {
    it('should handle empty search query', async () => {
      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          data: {
            productsCount: {
              count: 100,
              precision: 'EXACT',
            },
          },
        }),
      });

      await getProductsCountFromShopify({
        admin: mockAdmin,
        searchQuery: '',
      });

      expect(mockGraphql).toHaveBeenCalledWith(
        expect.stringContaining('query getProductsCount'),
        {
          variables: {
            query: null, // Empty query should be null
          },
        }
      );
    });

    it('should handle undefined search query', async () => {
      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          data: {
            productsCount: {
              count: 100,
              precision: 'EXACT',
            },
          },
        }),
      });

      await getProductsCountFromShopify({
        admin: mockAdmin,
        // searchQuery not provided
      });

      expect(mockGraphql).toHaveBeenCalledWith(
        expect.stringContaining('query getProductsCount'),
        {
          variables: {
            query: null,
          },
        }
      );
    });

    it('should truncate search query to 1000 characters', async () => {
      const longQuery = 'a'.repeat(1500); // 1500 characters
      const expectedQuery = 'a'.repeat(1000); // Should be truncated to 1000

      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          data: {
            productsCount: {
              count: 5,
              precision: 'EXACT',
            },
          },
        }),
      });

      await getProductsCountFromShopify({
        admin: mockAdmin,
        searchQuery: longQuery,
      });

      expect(mockGraphql).toHaveBeenCalledWith(
        expect.stringContaining('query getProductsCount'),
        {
          variables: {
            query: expectedQuery,
          },
        }
      );
    });

    it('should handle exactly 1000 character query', async () => {
      const exactQuery = 'a'.repeat(1000);

      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          data: {
            productsCount: {
              count: 5,
              precision: 'EXACT',
            },
          },
        }),
      });

      await getProductsCountFromShopify({
        admin: mockAdmin,
        searchQuery: exactQuery,
      });

      expect(mockGraphql).toHaveBeenCalledWith(
        expect.stringContaining('query getProductsCount'),
        {
          variables: {
            query: exactQuery,
          },
        }
      );
    });

    it('should handle query under 1000 characters without truncation', async () => {
      const shortQuery = 'test product search';

      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          data: {
            productsCount: {
              count: 15,
              precision: 'EXACT',
            },
          },
        }),
      });

      await getProductsCountFromShopify({
        admin: mockAdmin,
        searchQuery: shortQuery,
      });

      expect(mockGraphql).toHaveBeenCalledWith(
        expect.stringContaining('query getProductsCount'),
        {
          variables: {
            query: shortQuery,
          },
        }
      );
    });
  });

  describe('Option Filtering Logic (optionName/optionValue)', () => {
    it('should handle option filtering with both name and value', async () => {
      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          data: {
            productsCount: {
              count: 25,
              precision: 'EXACT',
            },
          },
        }),
      });

      await getProductsCountFromShopify({
        admin: mockAdmin,
        searchQuery: 'shirt',
        optionName: 'Size',
        optionValue: 'Large',
      });

      expect(mockGraphql).toHaveBeenCalledWith(
        expect.stringContaining('query getProductsCount'),
        {
          variables: {
            query: 'shirt option:Size:Large',
          },
        }
      );
    });

    it('should ignore option filtering when only optionName is provided', async () => {
      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          data: {
            productsCount: {
              count: 50,
              precision: 'EXACT',
            },
          },
        }),
      });

      await getProductsCountFromShopify({
        admin: mockAdmin,
        searchQuery: 'shirt',
        optionName: 'Size',
        // optionValue not provided
      });

      expect(mockGraphql).toHaveBeenCalledWith(
        expect.stringContaining('query getProductsCount'),
        {
          variables: {
            query: 'shirt', // No option filtering
          },
        }
      );
    });

    it('should ignore option filtering when only optionValue is provided', async () => {
      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          data: {
            productsCount: {
              count: 50,
              precision: 'EXACT',
            },
          },
        }),
      });

      await getProductsCountFromShopify({
        admin: mockAdmin,
        searchQuery: 'shirt',
        // optionName not provided
        optionValue: 'Large',
      });

      expect(mockGraphql).toHaveBeenCalledWith(
        expect.stringContaining('query getProductsCount'),
        {
          variables: {
            query: 'shirt', // No option filtering
          },
        }
      );
    });

    it('should sanitize option name and value (100 character limit)', async () => {
      const longOptionName = 'a'.repeat(150);
      const longOptionValue = 'b'.repeat(150);
      const expectedOptionName = 'a'.repeat(100);
      const expectedOptionValue = 'b'.repeat(100);

      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          data: {
            productsCount: {
              count: 5,
              precision: 'EXACT',
            },
          },
        }),
      });

      await getProductsCountFromShopify({
        admin: mockAdmin,
        searchQuery: 'test',
        optionName: longOptionName,
        optionValue: longOptionValue,
      });

      expect(mockGraphql).toHaveBeenCalledWith(
        expect.stringContaining('query getProductsCount'),
        {
          variables: {
            query: `test option:${expectedOptionName}:${expectedOptionValue}`,
          },
        }
      );
    });

    it('should handle empty search query with option filtering', async () => {
      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          data: {
            productsCount: {
              count: 10,
              precision: 'EXACT',
            },
          },
        }),
      });

      await getProductsCountFromShopify({
        admin: mockAdmin,
        searchQuery: '',
        optionName: 'Color',
        optionValue: 'Red',
      });

      expect(mockGraphql).toHaveBeenCalledWith(
        expect.stringContaining('query getProductsCount'),
        {
          variables: {
            query: 'option:Color:Red', // Should trim leading space
          },
        }
      );
    });
  });

  describe('GraphQL Error Handling', () => {
    it('should throw error when GraphQL returns errors', async () => {
      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          errors: [
            {
              message: 'Invalid query syntax',
              locations: [{ line: 1, column: 1 }],
            },
          ],
        }),
      });

      await expect(
        getProductsCountFromShopify({
          admin: mockAdmin,
          searchQuery: 'invalid query',
        })
      ).rejects.toThrow('GraphQL Error: Invalid query syntax');
    });

    it('should handle multiple GraphQL errors', async () => {
      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          errors: [
            { message: 'First error' },
            { message: 'Second error' },
          ],
        }),
      });

      await expect(
        getProductsCountFromShopify({
          admin: mockAdmin,
          searchQuery: 'test',
        })
      ).rejects.toThrow('GraphQL Error: First error');
    });

    it('should handle GraphQL errors without message', async () => {
      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          errors: [{}], // Error without message
        }),
      });

      await expect(
        getProductsCountFromShopify({
          admin: mockAdmin,
          searchQuery: 'test',
        })
      ).rejects.toThrow('GraphQL Error: Unknown error');
    });

    it('should handle network/response errors', async () => {
      mockGraphql.mockRejectedValue(new Error('Network error'));

      await expect(
        getProductsCountFromShopify({
          admin: mockAdmin,
          searchQuery: 'test',
        })
      ).rejects.toThrow('Network error');
    });

    it('should handle JSON parsing errors', async () => {
      mockGraphql.mockResolvedValue({
        json: () => Promise.reject(new Error('Invalid JSON')),
      });

      await expect(
        getProductsCountFromShopify({
          admin: mockAdmin,
          searchQuery: 'test',
        })
      ).rejects.toThrow('Invalid JSON');
    });
  });

  describe('Response Parsing and Success Scenarios', () => {
    it('should return count and precision for successful response', async () => {
      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          data: {
            productsCount: {
              count: 42,
              precision: 'EXACT',
            },
          },
        }),
      });

      const result = await getProductsCountFromShopify({
        admin: mockAdmin,
        searchQuery: 'test',
      });

      expect(result).toEqual({
        count: 42,
        precision: 'EXACT',
      });
    });

    it('should handle approximate precision', async () => {
      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          data: {
            productsCount: {
              count: 1000,
              precision: 'APPROXIMATE',
            },
          },
        }),
      });

      const result = await getProductsCountFromShopify({
        admin: mockAdmin,
        searchQuery: 'popular',
      });

      expect(result).toEqual({
        count: 1000,
        precision: 'APPROXIMATE',
      });
    });

    it('should handle zero count', async () => {
      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          data: {
            productsCount: {
              count: 0,
              precision: 'EXACT',
            },
          },
        }),
      });

      const result = await getProductsCountFromShopify({
        admin: mockAdmin,
        searchQuery: 'nonexistent',
      });

      expect(result).toEqual({
        count: 0,
        precision: 'EXACT',
      });
    });
  });

  describe('Regression Prevention - Critical Scenarios', () => {
    it('should handle concurrent calls without interference', async () => {
      mockGraphql
        .mockResolvedValueOnce({
          json: () => Promise.resolve({
            data: { productsCount: { count: 10, precision: 'EXACT' } },
          }),
        })
        .mockResolvedValueOnce({
          json: () => Promise.resolve({
            data: { productsCount: { count: 20, precision: 'EXACT' } },
          }),
        });

      const [result1, result2] = await Promise.all([
        getProductsCountFromShopify({
          admin: mockAdmin,
          searchQuery: 'query1',
        }),
        getProductsCountFromShopify({
          admin: mockAdmin,
          searchQuery: 'query2',
        }),
      ]);

      expect(result1.count).toBe(10);
      expect(result2.count).toBe(20);
      expect(mockGraphql).toHaveBeenCalledTimes(2);
    });

    it('should handle special characters in search query', async () => {
      const specialQuery = 'test "quoted" & <script> 中文 emoji 🎉';

      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          data: {
            productsCount: {
              count: 5,
              precision: 'EXACT',
            },
          },
        }),
      });

      const result = await getProductsCountFromShopify({
        admin: mockAdmin,
        searchQuery: specialQuery,
      });

      expect(result.count).toBe(5);
      expect(mockGraphql).toHaveBeenCalledWith(
        expect.stringContaining('query getProductsCount'),
        {
          variables: {
            query: specialQuery,
          },
        }
      );
    });

    it('should handle special characters in option values', async () => {
      const specialOptionValue = 'Size "Large" & <Medium>';

      mockGraphql.mockResolvedValue({
        json: () => Promise.resolve({
          data: {
            productsCount: {
              count: 3,
              precision: 'EXACT',
            },
          },
        }),
      });

      const result = await getProductsCountFromShopify({
        admin: mockAdmin,
        searchQuery: 'shirt',
        optionName: 'Size',
        optionValue: specialOptionValue,
      });

      expect(result.count).toBe(3);
      expect(mockGraphql).toHaveBeenCalledWith(
        expect.stringContaining('query getProductsCount'),
        {
          variables: {
            query: `shirt option:Size:${specialOptionValue}`,
          },
        }
      );
    });
  });
});
