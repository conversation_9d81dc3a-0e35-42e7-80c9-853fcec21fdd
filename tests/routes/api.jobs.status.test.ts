import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { setupTestEnv } from '../setup'

// Use vi.hoisted to ensure proper mock hoisting
const {
  mockAuthenticate,
  mockGetJobById,
} = vi.hoisted(() => {
  const mockAuthenticate = {
    admin: vi.fn(),
  }

  const mockGetJobById = vi.fn()

  return {
    mockAuthenticate,
    mockGetJobById,
  }
})

vi.mock('~/shopify.server', () => ({
  authenticate: mockAuthenticate,
}))

vi.mock('~/utils/jobManager.server', () => ({
  getJobById: mockGetJobById,
}))

// Import the loader function after mocks are set up
import { loader } from '~/routes/api.jobs.$jobId.status'

describe('Job Status API Route', () => {
  beforeEach(() => {
    setupTestEnv()
    vi.clearAllMocks()

    // Setup default successful mocks
    mockAuthenticate.admin.mockResolvedValue({
      session: { shop: 'test-shop.myshopify.com' },
    })
    mockGetJobById.mockResolvedValue({
      id: 'job-123',
      status: 'IN_PROGRESS',
      processedProducts: 5,
      totalProducts: 10,
      successfulUpdates: 4,
      failedUpdates: 1,
      updatedAt: '2024-01-01T12:00:00Z',
    })
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('Authentication and Authorization', () => {
    it('should authenticate using session tokens', async () => {
      const request = new Request('http://localhost/api/jobs/job-123/status')

      await loader({ request, params: { jobId: 'job-123' }, context: {} as any })

      expect(mockAuthenticate.admin).toHaveBeenCalledWith(request)
    })
  })

  describe('Job Status Response', () => {
    it('should return optimized job status for active job', async () => {
      const request = new Request('http://localhost/api/jobs/job-123/status')

      const response = await loader({ request, params: { jobId: 'job-123' }, context: {} as any })

      expect(response.status).toBe(200)
      const result = await response.json()

      expect(result).toEqual({
        id: 'job-123',
        status: 'IN_PROGRESS',
        processedProducts: 5,
        totalProducts: 10,
        successfulUpdates: 4,
        failedUpdates: 1,
        updatedAt: '2024-01-01T12:00:00Z',
      })
    })

    it('should include completion details for completed job', async () => {
      const mockJob = {
        id: 'job-123',
        status: 'COMPLETED',
        processedProducts: 10,
        totalProducts: 10,
        successfulUpdates: 10,
        failedUpdates: 0,
        updatedAt: '2024-01-01T12:00:00Z',
        completedAt: '2024-01-01T12:05:00Z',
      }

      mockGetJobById.mockResolvedValue(mockJob)

      const request = new Request('http://localhost/api/jobs/job-123/status')

      const response = await loader({ request, params: { jobId: 'job-123' }, context: {} as any })

      const result = await response.json()
      expect(result.completedAt).toBe('2024-01-01T12:05:00Z')
    })

    it('should include error details for failed job', async () => {
      const mockJob = {
        id: 'job-123',
        status: 'FAILED',
        processedProducts: 3,
        totalProducts: 10,
        successfulUpdates: 2,
        failedUpdates: 1,
        updatedAt: '2024-01-01T12:00:00Z',
        failedAt: '2024-01-01T12:03:00Z',
        errorMessage: 'API rate limit exceeded',
      }

      mockGetJobById.mockResolvedValue(mockJob)

      const request = new Request('http://localhost/api/jobs/job-123/status')

      const response = await loader({ request, params: { jobId: 'job-123' }, context: {} as any })

      const result = await response.json()
      expect(result.failedAt).toBe('2024-01-01T12:03:00Z')
      expect(result.errorMessage).toBe('API rate limit exceeded')
    })
  })

  describe('Basic Functionality', () => {
    it('should handle conditional requests with ETag', async () => {
      const request = new Request('http://localhost/api/jobs/job-123/status', {
        headers: {
          'If-None-Match': '"job-123-2024-01-01T12:00:00Z"',
        },
      })

      const response = await loader({ request, params: { jobId: 'job-123' }, context: {} as any })

      expect(response.status).toBe(304) // Not Modified
    })

    it('should include proper content type headers', async () => {
      const request = new Request('http://localhost/api/jobs/job-123/status')

      const response = await loader({ request, params: { jobId: 'job-123' }, context: {} as any })

      expect(response.headers.get('Content-Type')).toBe('application/json')
    })
  })
})
