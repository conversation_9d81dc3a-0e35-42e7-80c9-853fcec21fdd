import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { setupTestEnv } from '../setup'

// Use vi.hoisted to ensure proper mock hoisting
const {
  mockEnqueueBulkUpdateJob,
  mockGetShopQueue,
  mockQueue,
  mockGetJobById,
  mockUpdateJobStatus,
  mockAuthenticate
} = vi.hoisted(() => {
  const mockEnqueueBulkUpdateJob = vi.fn()
  const mockGetShopQueue = vi.fn()
  const mockQueue = {
    getJob: vi.fn(),
    removeJobs: vi.fn(),
    clean: vi.fn(),
  }

  const mockGetJobById = vi.fn()
  const mockUpdateJobStatus = vi.fn()

  const mockAuthenticate = {
    admin: vi.fn(),
  }

  return {
    mockEnqueueBulkUpdateJob,
    mockGetShopQueue,
    mockQueue,
    mockGetJobById,
    mockUpdateJobStatus,
    mockAuthenticate
  }
})

vi.mock('~/services/jobQueue.server', () => ({
  enqueueBulkUpdateJob: mockEnqueueBulkUpdateJob,
  getShopQueue: mockGetShopQueue,
}))

vi.mock('~/utils/jobManager.server', () => ({
  getJobById: mockGetJobById,
  updateJobStatus: mockUpdateJobStatus,
}))

vi.mock('~/shopify.server', () => ({
  authenticate: mockAuthenticate,
}))

// Import the action functions after mocks are set up
// Using dynamic import to avoid TypeScript module resolution issues
let action: any

describe('Job Action API Routes', () => {
  beforeEach(async () => {
    setupTestEnv()
    vi.clearAllMocks()

    // Dynamic import to avoid TypeScript module resolution issues
    const module = await import('~/routes/api.jobs.$jobId.actions')
    action = module.action

    // Setup default mock returns
    mockAuthenticate.admin.mockResolvedValue({
      session: { shop: 'test-shop.myshopify.com' },
    })
    mockGetShopQueue.mockReturnValue(mockQueue)
    mockEnqueueBulkUpdateJob.mockResolvedValue({
      bullJobId: 'bull-job-123',
      jobId: 'job-123',
      queueName: 'bulk-update-test-shop-myshopify-com',
    })
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('POST /api/jobs/{jobId}/run', () => {
    it('should enqueue a scheduled job for immediate processing', async () => {
      // Mock job data
      const mockJob = {
        id: 'job-123',
        title: 'Test Job',
        shopId: 'test-shop.myshopify.com',
        status: 'SCHEDULED',
        modifications: [
          { fieldType: 'product', fieldName: 'title', fieldValue: 'New Title' }
        ],
        unselectedIds: [],
        filterCriteria: null,
      }

      mockGetJobById.mockResolvedValue(mockJob)

      const request = new Request('http://localhost/api/jobs/job-123/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'run' }),
      })

      const response = await action({ request, params: { jobId: 'job-123' }, context: {} as any })
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(result.message).toBe('Job enqueued successfully')
      expect(mockEnqueueBulkUpdateJob).toHaveBeenCalledWith({
        jobId: 'job-123',
        shopDomain: 'test-shop.myshopify.com',
        modifications: [
          { fieldType: 'product', fieldName: 'title', fieldValue: 'New Title' }
        ],
        productIds: [],
        options: {
          batchSize: 10,
          delayBetweenBatches: 1000,
        },
      })
      expect(mockUpdateJobStatus).toHaveBeenCalledWith('job-123', 'IN_PROGRESS', 'test-shop.myshopify.com')
    })

    it('should return error for non-existent job', async () => {
      mockGetJobById.mockResolvedValue(null)

      const request = new Request('http://localhost/api/jobs/invalid-job/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'run' }),
      })

      const response = await action({ request, params: { jobId: 'invalid-job' }, context: {} as any })
      const result = await response.json()

      expect(response.status).toBe(404)
      expect(result.success).toBe(false)
      expect(result.error).toBe('Job not found')
    })

    it('should return error for job that is already running', async () => {
      const mockJob = {
        id: 'job-123',
        status: 'IN_PROGRESS',
        shopId: 'test-shop.myshopify.com',
      }

      mockGetJobById.mockResolvedValue(mockJob)

      const request = new Request('http://localhost/api/jobs/job-123/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'run' }),
      })

      const response = await action({ request, params: { jobId: 'job-123' }, context: {} as any })
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.success).toBe(false)
      expect(result.error).toBe('Job is already running')
    })

    it('should return error for job with no modifications', async () => {
      const mockJob = {
        id: 'job-123',
        status: 'SCHEDULED',
        shopId: 'test-shop.myshopify.com',
        modifications: [], // Empty modifications array
        unselectedIds: [],
        filterCriteria: null,
      }

      mockGetJobById.mockResolvedValue(mockJob)

      const request = new Request('http://localhost/api/jobs/job-123/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'run' }),
      })

      const response = await action({ request, params: { jobId: 'job-123' }, context: {} as any })
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.success).toBe(false)
      expect(result.error).toBe('Job has no modifications to apply')
    })
  })

  describe('POST /api/jobs/{jobId}/stop', () => {
    it('should stop a running job', async () => {
      const mockJob = {
        id: 'job-123',
        status: 'IN_PROGRESS',
        shopId: 'test-shop.myshopify.com',
      }

      mockGetJobById.mockResolvedValue(mockJob)
      mockQueue.getJob.mockResolvedValue({ id: 'bull-job-123', remove: vi.fn() })

      const request = new Request('http://localhost/api/jobs/job-123/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'stop' }),
      })

      const response = await action({ request, params: { jobId: 'job-123' }, context: {} as any })
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(result.message).toBe('Job stopped successfully')
      expect(mockUpdateJobStatus).toHaveBeenCalledWith('job-123', 'CANCELLED', 'test-shop.myshopify.com')
    })

    it('should stop a scheduled job', async () => {
      const mockJob = {
        id: 'job-123',
        status: 'SCHEDULED',
        shopId: 'test-shop.myshopify.com',
      }

      mockGetJobById.mockResolvedValue(mockJob)
      mockQueue.getJob.mockResolvedValue({ id: 'bull-job-123', remove: vi.fn() })

      const request = new Request('http://localhost/api/jobs/job-123/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'stop' }),
      })

      const response = await action({ request, params: { jobId: 'job-123' }, context: {} as any })
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(result.message).toBe('Job stopped successfully')
      expect(mockUpdateJobStatus).toHaveBeenCalledWith('job-123', 'CANCELLED', 'test-shop.myshopify.com')
    })

    it('should return error for job that is not running', async () => {
      const mockJob = {
        id: 'job-123',
        status: 'COMPLETED',
        shopId: 'test-shop.myshopify.com',
      }

      mockGetJobById.mockResolvedValue(mockJob)

      const request = new Request('http://localhost/api/jobs/job-123/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'stop' }),
      })

      const response = await action({ request, params: { jobId: 'job-123' }, context: {} as any })
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.success).toBe(false)
      expect(result.error).toBe('Job has already completed')
    })
  })

  describe('Error handling', () => {
    it('should handle invalid action types', async () => {
      const request = new Request('http://localhost/api/jobs/job-123/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'invalid' }),
      })

      const response = await action({ request, params: { jobId: 'job-123' }, context: {} as any })
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.success).toBe(false)
      expect(result.error).toBe('Invalid action. Must be "run" or "stop"')
    })

    it('should handle missing action in request body', async () => {
      const request = new Request('http://localhost/api/jobs/job-123/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({}),
      })

      const response = await action({ request, params: { jobId: 'job-123' }, context: {} as any })
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.success).toBe(false)
      expect(result.error).toBe('Action is required')
    })

    it('should handle job enqueueing failures', async () => {
      const mockJob = {
        id: 'job-123',
        status: 'SCHEDULED',
        shopId: 'test-shop.myshopify.com',
        modifications: [
          { fieldType: 'product', fieldName: 'title', fieldValue: 'New Title' }
        ], // Valid modifications to pass validation
        unselectedIds: [],
        filterCriteria: null,
      }

      mockGetJobById.mockResolvedValue(mockJob)
      mockEnqueueBulkUpdateJob.mockRejectedValue(new Error('Queue connection failed'))

      const request = new Request('http://localhost/api/jobs/job-123/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'run' }),
      })

      const response = await action({ request, params: { jobId: 'job-123' }, context: {} as any })
      const result = await response.json()

      expect(response.status).toBe(500)
      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to enqueue job: Queue connection failed')
    })

    it('should handle malformed JSON in request body', async () => {
      const request = new Request('http://localhost/api/jobs/job-123/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid json{',
      })

      const response = await action({ request, params: { jobId: 'job-123' }, context: {} as any })
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.success).toBe(false)
      expect(result.error).toBe('Invalid JSON in request body')
    })

    it('should handle missing jobId parameter', async () => {
      const request = new Request('http://localhost/api/jobs//actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'run' }),
      })

      const response = await action({ request, params: {}, context: {} as any })
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.success).toBe(false)
      expect(result.error).toBe('Job ID is required')
    })

    it('should handle authentication failures', async () => {
      mockAuthenticate.admin.mockRejectedValue(new Error('Authentication failed'))

      const request = new Request('http://localhost/api/jobs/job-123/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'run' }),
      })

      const response = await action({ request, params: { jobId: 'job-123' }, context: {} as any })
      const result = await response.json()

      expect(response.status).toBe(500)
      expect(result.success).toBe(false)
      expect(result.error).toBe('Internal server error')
    })
  })
})
