import { describe, it, expect, beforeAll, beforeEach, afterAll } from 'vitest'
import { setupSharedCleanDb, cleanupSharedResources } from './test-db-strategies'
import { PrismaClient } from '@prisma/client'

/**
 * FAST TESTS: Shared Database with Table Truncation
 * - All tests share one database
 * - Tables are cleaned between tests
 * - Perfect for most unit/integration tests
 * - Expected time: ~1-2 seconds for entire suite
 */

describe('Fast Database Tests (Shared DB)', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    // Set up shared database once for all tests
    const setup = await setupSharedCleanDb()
    prisma = setup.prisma
  })

  beforeEach(async () => {
    // Clean tables before each test
    await setupSharedCleanDb()
  })

  afterAll(async () => {
    // Clean up shared resources
    await cleanupSharedResources()
  })

  it('should create and read session data', async () => {
    // Create test data
    const session = await prisma.session.create({
      data: {
        id: 'test-session-1',
        shop: 'test-shop.myshopify.com',
        state: 'test-state',
        isOnline: false,
        accessToken: 'test-token',
        accountOwner: true
      }
    })

    expect(session.id).toBe('test-session-1')
    expect(session.shop).toBe('test-shop.myshopify.com')
  })

  it('should handle temp selections', async () => {
    // This test runs on clean tables (previous test data is gone)
    const tempSelection = await prisma.tempSelection.create({
      data: {
        id: 'temp-1',
        sessionKey: 'session-key-1',
        shopId: 'shop-1',
        data: JSON.stringify({ products: ['1', '2', '3'] }),
        expiresAt: new Date(Date.now() + 3600000) // 1 hour from now
      }
    })

    expect(tempSelection.sessionKey).toBe('session-key-1')
    
    // Verify previous test data is not present
    const sessions = await prisma.session.findMany()
    expect(sessions).toHaveLength(0) // Clean slate
  })

  it('should handle job creation', async () => {
    const job = await prisma.job.create({
      data: {
        id: 'job-1',
        title: 'Test Job',
        description: 'A test job for bulk product updates',
        shopId: 'shop-1',
        status: 'SCHEDULED',
        totalProducts: 100,
        processedProducts: 0
      }
    })

    expect(job.status).toBe('SCHEDULED')
    expect(job.totalProducts).toBe(100)
    expect(job.title).toBe('Test Job')
  })

  it('should verify database isolation between tests', async () => {
    // Verify that previous tests' data is not present
    const allSessions = await prisma.session.findMany()
    const allTempSelections = await prisma.tempSelection.findMany()
    const allJobs = await prisma.job.findMany()

    expect(allSessions).toHaveLength(0)
    expect(allTempSelections).toHaveLength(0)
    expect(allJobs).toHaveLength(0)
  })
})
