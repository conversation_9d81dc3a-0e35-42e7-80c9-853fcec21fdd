import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { setupTestEnv } from '../setup'
import type { JobDetail } from '~/types/models'
import { sanitizeText } from '~/utils/sanitization'

// Mock Polaris components
vi.mock('@shopify/polaris', () => ({
  Button: vi.fn(),
  InlineStack: vi.fn(),
  Modal: vi.fn(),
  Text: vi.fn(),
  BlockStack: vi.fn(),
}))

// Mock Remix hooks
const mockNavigate = vi.fn()
const mockFetcher = {
  submit: vi.fn(),
  state: 'idle' as 'idle' | 'submitting' | 'loading',
  data: null as any,
  formData: null as FormData | null,
}

vi.mock('@remix-run/react', () => ({
  useFetcher: () => mockFetcher,
  useNavigate: () => mockNavigate,
}))

// Mock icons
vi.mock('@shopify/polaris-icons', () => ({
  PlayIcon: 'PlayIcon',
}))

// Mock ToastContext
vi.mock('~/contexts/ToastContext', () => ({
  useJobToast: () => ({
    showJobStarted: vi.fn(),
    showJobStopped: vi.fn(),
    showJobCompleted: vi.fn(),
    showJobFailed: vi.fn(),
    showJobError: vi.fn(),
    showNetworkError: vi.fn(),
    showGenericSuccess: vi.fn(),
  }),
  getErrorMessage: vi.fn((error) => String(error)),
  isNetworkError: vi.fn(() => false),
}))

// React hooks are mocked globally in setup.ts

describe('JobControlButtons Component Logic', () => {
  beforeEach(() => {
    setupTestEnv()
    vi.clearAllMocks()
    mockFetcher.state = 'idle'
    mockFetcher.data = null
    mockFetcher.formData = null
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  const createMockJob = (overrides: Partial<JobDetail> = {}): JobDetail => ({
    id: 'job-123',
    title: 'Test Job',
    description: 'Test job description',
    shopId: 'test-shop.myshopify.com',
    status: 'SCHEDULED',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    totalProducts: 100,
    processedProducts: 0,
    successfulUpdates: 0,
    failedUpdates: 0,
    modifications: [
      {
        id: 'mod-1',
        fieldType: 'product',
        fieldName: 'title',
        fieldValue: 'New Title',
      },
    ],
    productVariants: [],
    ...overrides,
  })

  describe('Component Import and Structure', () => {
    it('should be importable without errors', async () => {
      await expect(import('~/components/JobControlButtons')).resolves.toBeDefined()
    })

    it('should export default component', async () => {
      const module = await import('~/components/JobControlButtons')
      expect(module.default).toBeDefined()
      expect(typeof module.default).toBe('function')
    })
  })

  describe('Job Status Logic', () => {
    it('should identify scheduled jobs as runnable', () => {
      const job = createMockJob({ status: 'SCHEDULED' })
      expect(job.status).toBe('SCHEDULED')
    })

    it('should identify in-progress jobs as stoppable', () => {
      const job = createMockJob({ status: 'IN_PROGRESS' })
      expect(job.status).toBe('IN_PROGRESS')
    })

    it('should identify completed jobs as non-actionable', () => {
      const job = createMockJob({ status: 'COMPLETED' })
      expect(job.status).toBe('COMPLETED')
    })

    it('should identify failed jobs as non-actionable', () => {
      const job = createMockJob({ status: 'FAILED' })
      expect(job.status).toBe('FAILED')
    })

    it('should identify cancelled jobs as non-actionable', () => {
      const job = createMockJob({ status: 'CANCELLED' })
      expect(job.status).toBe('CANCELLED')
    })
  })

  describe('Fetcher State Logic', () => {
    it('should detect run action loading state', () => {
      mockFetcher.state = 'submitting'
      mockFetcher.formData = new FormData()
      mockFetcher.formData.set('action', 'run')

      const isRunning = mockFetcher.state === 'submitting' &&
        mockFetcher.formData?.get('action') === 'run'

      expect(isRunning).toBe(true)
    })

    it('should detect stop action loading state', () => {
      mockFetcher.state = 'submitting'
      mockFetcher.formData = new FormData()
      mockFetcher.formData.set('action', 'stop')

      const isStopping = mockFetcher.state === 'submitting' &&
        mockFetcher.formData?.get('action') === 'stop'

      expect(isStopping).toBe(true)
    })

    it('should not detect loading for different action', () => {
      mockFetcher.state = 'submitting'
      mockFetcher.formData = new FormData()
      mockFetcher.formData.set('action', 'stop')

      const isRunning = mockFetcher.state === 'submitting' &&
        mockFetcher.formData?.get('action') === 'run'

      expect(isRunning).toBe(false)
    })

    it('should not detect loading when idle', () => {
      mockFetcher.state = 'idle'
      mockFetcher.formData = null

      // Test the logic that would be used in the component
      // TypeScript warning is expected here - we're testing that 'idle' !== 'submitting'
      const isSubmitting = (mockFetcher.state as string) === 'submitting'
      const hasFormData = mockFetcher.formData !== null

      expect(isSubmitting).toBe(false)
      expect(hasFormData).toBe(false)
    })
  })

  describe('API Integration Logic', () => {
    it('should generate correct run action API call parameters', () => {
      const job = createMockJob({ status: 'SCHEDULED' })

      const expectedParams = {
        action: 'run'
      }
      const expectedOptions = {
        method: 'POST',
        action: `/api/jobs/${job.id}/actions`,
      }

      expect(expectedParams.action).toBe('run')
      expect(expectedOptions.method).toBe('POST')
      expect(expectedOptions.action).toBe(`/api/jobs/${job.id}/actions`)
    })

    it('should generate correct stop action API call parameters', () => {
      const job = createMockJob({ status: 'IN_PROGRESS' })

      const expectedParams = {
        action: 'stop'
      }
      const expectedOptions = {
        method: 'POST',
        action: `/api/jobs/${job.id}/actions`,
      }

      expect(expectedParams.action).toBe('stop')
      expect(expectedOptions.method).toBe('POST')
      expect(expectedOptions.action).toBe(`/api/jobs/${job.id}/actions`)
    })

    it('should handle API endpoint construction', () => {
      const jobId = 'test-job-123'
      const endpoint = `/api/jobs/${jobId}/actions`

      expect(endpoint).toBe('/api/jobs/test-job-123/actions')
    })
  })

  describe('Response Handling Logic', () => {
    it('should identify successful response', () => {
      const successResponse = { success: true, message: 'Job started successfully' }

      expect(successResponse.success).toBe(true)
      expect(successResponse.message).toBe('Job started successfully')
    })

    it('should identify error response', () => {
      const errorResponse = { success: false, error: 'Job failed to start' }

      expect(errorResponse.success).toBe(false)
      expect(errorResponse.error).toBe('Job failed to start')
    })

    it('should handle success response for run action', () => {
      const response = { success: true, message: 'Job started successfully' }

      if (response.success && response.message) {
        expect(response.message).toBe('Job started successfully')
      }
    })

    it('should handle success response for stop action', () => {
      const response = { success: true, message: 'Job stopped successfully' }

      if (response.success && response.message) {
        expect(response.message).toBe('Job stopped successfully')
      }
    })

    it('should handle error response', () => {
      const response = { success: false, error: 'Job failed to start' }

      if (!response.success && response.error) {
        expect(response.error).toBe('Job failed to start')
      }
    })

    it('should handle timeout logic for auto-dismiss', () => {
      vi.useFakeTimers()

      let dismissed = false
      const dismissAfterTimeout = () => {
        setTimeout(() => {
          dismissed = true
        }, 3000)
      }

      dismissAfterTimeout()

      expect(dismissed).toBe(false)
      vi.advanceTimersByTime(3000)
      expect(dismissed).toBe(true)

      vi.useRealTimers()
    })
  })

  describe('Edge Cases and Validation', () => {
    it('should handle job with no modifications', () => {
      const job = createMockJob({
        status: 'SCHEDULED',
        modifications: []
      })

      expect(job.modifications).toEqual([])
      expect(job.status).toBe('SCHEDULED')
    })

    it('should handle undefined job', () => {
      const job = undefined

      expect(job).toBeUndefined()
    })

    it('should handle null job', () => {
      const job = null

      expect(job).toBeNull()
    })

    it('should handle job with invalid status', () => {
      const job = createMockJob({ status: 'INVALID_STATUS' as any })

      expect(job.status).toBe('INVALID_STATUS')
    })

    it('should handle fetcher response with null error', () => {
      const response = { success: false, error: null }

      expect(response.success).toBe(false)
      expect(response.error).toBeNull()
    })

    it('should validate job status for actionability', () => {
      const actionableStatuses = ['SCHEDULED', 'IN_PROGRESS']
      const nonActionableStatuses = ['COMPLETED', 'FAILED', 'CANCELLED']

      actionableStatuses.forEach(status => {
        const job = createMockJob({ status: status as any })
        const isActionable = job.status === 'SCHEDULED' || job.status === 'IN_PROGRESS'
        expect(isActionable).toBe(true)
      })

      nonActionableStatuses.forEach(status => {
        const job = createMockJob({ status: status as any })
        const isActionable = job.status === 'SCHEDULED' || job.status === 'IN_PROGRESS'
        expect(isActionable).toBe(false)
      })
    })
  })

  describe('Accessibility Logic', () => {
    it('should generate proper ARIA label for run button', () => {
      const job = createMockJob({ status: 'SCHEDULED' })
      const ariaLabel = `Run job: ${job.title}`

      expect(ariaLabel).toBe('Run job: Test Job')
    })

    it('should generate proper ARIA label for stop button', () => {
      const job = createMockJob({ status: 'IN_PROGRESS' })
      const ariaLabel = `Stop job: ${job.title}`

      expect(ariaLabel).toBe('Stop job: Test Job')
    })

    it('should sanitize job titles for security', () => {
      const maliciousTitle = '<script>alert("xss")</script>Test Job'
      const sanitizedTitle = sanitizeText(maliciousTitle)

      expect(sanitizedTitle).toBe('scriptalert(xss)/scriptTest Job')
      expect(sanitizedTitle).not.toContain('<script>')
      expect(sanitizedTitle).not.toContain('>')
      expect(sanitizedTitle).not.toContain('<')
      expect(sanitizedTitle).not.toContain('"')
    })

    it('should handle various malicious characters in job titles', () => {
      const testCases = [
        { input: 'Normal Job Title', expected: 'Normal Job Title' },
        { input: 'Job & Title', expected: 'Job  Title' },
        { input: 'Job "Title"', expected: 'Job Title' },
        { input: "Job 'Title'", expected: 'Job Title' },
        { input: 'Job<script>Title', expected: 'JobscriptTitle' },
        { input: 'Job\x00Title', expected: 'JobTitle' },
      ]

      testCases.forEach(({ input, expected }) => {
        expect(sanitizeText(input)).toBe(expected)
      })
    })

    it('should determine aria-busy state for loading', () => {
      mockFetcher.state = 'submitting'
      mockFetcher.formData = new FormData()
      mockFetcher.formData.set('action', 'run')

      const isRunning = mockFetcher.state === 'submitting' &&
        mockFetcher.formData?.get('action') === 'run'

      expect(isRunning).toBe(true)
    })

    it('should validate modal accessibility properties', () => {
      const modalProps = {
        role: 'dialog',
        'aria-modal': 'true',
        'aria-labelledby': 'stop-job-modal-title'
      }

      expect(modalProps.role).toBe('dialog')
      expect(modalProps['aria-modal']).toBe('true')
      expect(modalProps['aria-labelledby']).toBe('stop-job-modal-title')
    })
  })

  describe('Component Props and Variants Logic', () => {
    it('should determine button size based on variant', () => {
      const getButtonSize = (variant: string) => variant === 'compact' ? 'slim' : 'medium'

      expect(getButtonSize('compact')).toBe('slim')
      expect(getButtonSize('default')).toBe('medium')
      expect(getButtonSize('inline')).toBe('medium')
    })

    it('should determine button variant based on props', () => {
      const getButtonVariant = (variant: string) => variant === 'inline' ? 'tertiary' : 'primary'

      expect(getButtonVariant('inline')).toBe('tertiary')
      expect(getButtonVariant('default')).toBe('primary')
      expect(getButtonVariant('compact')).toBe('primary')
    })

    it('should handle callback invocation logic', () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      // Simulate success callback
      const successMessage = 'Job started successfully'
      onSuccess(successMessage)

      // Simulate error callback
      const errorMessage = 'Job failed to start'
      onError(errorMessage)

      expect(onSuccess).toHaveBeenCalledWith(successMessage)
      expect(onError).toHaveBeenCalledWith(errorMessage)
    })

    it('should validate variant data attributes', () => {
      const variants = ['default', 'compact', 'inline']

      variants.forEach(variant => {
        const dataVariant = variant
        expect(['default', 'compact', 'inline']).toContain(dataVariant)
      })
    })
  })

  describe('Button Tone Logic', () => {
    it('should use success tone for run button', () => {
      const runButtonTone = 'success'
      expect(runButtonTone).toBe('success')
    })

    it('should use critical tone for stop button', () => {
      const stopButtonTone = 'critical'
      expect(stopButtonTone).toBe('critical')
    })
  })
})
