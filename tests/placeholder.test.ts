import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { setupTestDatabase, cleanupTestDatabase } from './setup'
import { PrismaClient } from '@prisma/client'

describe('Test Setup Verification', () => {
  let prisma: PrismaClient
  let testDbName: string

  beforeEach(async () => {
    const setup = await setupTestDatabase()
    prisma = setup.prisma
    testDbName = setup.testDbName
  })

  afterEach(async () => {
    await cleanupTestDatabase(prisma, testDbName)
    // Also clean up any other leftover test files
    const { cleanupOldTestDatabases } = await import('./setup')
    cleanupOldTestDatabases()
  })

  it('should create and connect to test database', async () => {
    // Test that we can connect to the database
    expect(prisma).toBeDefined()

    // Test that we can perform a basic query
    const result = await prisma.$queryRaw`SELECT 1 as test`
    expect(result).toBeDefined()
  })

  it('should have proper test environment variables set', () => {
    expect(process.env.NODE_ENV).toBe('test')
    expect(process.env.SHOPIFY_API_KEY).toBe('test-api-key-for-testing')
    expect(process.env.SHOPIFY_API_SECRET).toBe('test-api-secret-for-testing')
    expect(process.env.SHOPIFY_APP_URL).toBe('http://localhost:3001')
    expect(process.env.SCOPES).toBe('write_products')
  })

  it('should create unique test database names', async () => {
    // Create another setup to verify uniqueness
    const setup2 = await setupTestDatabase()

    expect(setup2.testDbName).toBeDefined()
    expect(setup2.testDbName).not.toBe(testDbName)
    // Check that the filename part matches the expected pattern
    const filename = setup2.testDbName.split('/').pop() || ''
    expect(filename).toMatch(/^test_[a-f0-9]{16}\.sqlite$/)

    // Clean up the second setup
    await cleanupTestDatabase(setup2.prisma, setup2.testDbName)
  })

  it('should have database tables from migrations', async () => {
    // Test that our database has the expected tables
    // This verifies that migrations ran successfully
    const tables = await prisma.$queryRaw`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
    ` as Array<{ name: string }>

    const tableNames = tables.map(t => t.name)

    // Check for key tables that should exist after schema creation
    // Note: SQLite table names are case-sensitive
    expect(tableNames.length).toBeGreaterThan(0)

    // Check for all tables from our schema
    expect(tableNames).toContain('Session')
    expect(tableNames).toContain('TempSelection')
    expect(tableNames).toContain('Job')
    expect(tableNames).toContain('JobModification')
    expect(tableNames).toContain('JobProductVariant')

    // Verify we have the expected number of tables
    expect(tableNames.length).toBe(5)
  })
})
