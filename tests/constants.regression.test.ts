import { describe, it, expect, beforeEach, vi } from 'vitest'
import { generateShortKey, extractIdFromGid, getStoreHandle, appName, modificationFields } from '~/utils/constants'
import { SegmentType } from '~/components/PopoverWithSearchableList'

// Mock window object for getStoreHandle tests
const mockWindow = {
  location: {
    pathname: ''
  }
}

// Mock global window
Object.defineProperty(global, 'window', {
  value: mockWindow,
  writable: true
})

// Mock crypto for generateShortKey
const mockCrypto = {
  getRandomValues: vi.fn()
}

Object.defineProperty(global, 'crypto', {
  value: mockCrypto,
  writable: true
})

describe('Constants Regression Tests', () => {
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()
    mockWindow.location.pathname = ''
  })

  describe('generateShortKey', () => {
    it('should generate key with default length of 6', () => {
      // Mock crypto.getRandomValues to return predictable values
      mockCrypto.getRandomValues.mockImplementation((array) => {
        for (let i = 0; i < array.length; i++) {
          array[i] = i * 10 // Predictable values
        }
        return array
      })

      const key = generateShortKey()
      
      expect(key).toBeDefined()
      expect(typeof key).toBe('string')
      expect(key.length).toBe(6)
      expect(mockCrypto.getRandomValues).toHaveBeenCalledTimes(1)
    })

    it('should generate key with custom length', () => {
      mockCrypto.getRandomValues.mockImplementation((array) => {
        for (let i = 0; i < array.length; i++) {
          array[i] = i * 5
        }
        return array
      })

      const key = generateShortKey(10)
      
      expect(key.length).toBe(10)
      expect(mockCrypto.getRandomValues).toHaveBeenCalledWith(expect.any(Uint8Array))
      
      // Verify the array passed has correct length
      const callArgs = mockCrypto.getRandomValues.mock.calls[0][0]
      expect(callArgs.length).toBe(10)
    })

    it('should generate different keys on multiple calls', () => {
      let callCount = 0
      mockCrypto.getRandomValues.mockImplementation((array) => {
        for (let i = 0; i < array.length; i++) {
          array[i] = (i + callCount) * 7 // Different values each call
        }
        callCount++
        return array
      })

      const key1 = generateShortKey()
      const key2 = generateShortKey()
      
      expect(key1).not.toBe(key2)
      expect(mockCrypto.getRandomValues).toHaveBeenCalledTimes(2)
    })

    it('should handle edge case of length 1', () => {
      mockCrypto.getRandomValues.mockImplementation((array) => {
        array[0] = 25
        return array
      })

      const key = generateShortKey(1)
      
      expect(key.length).toBe(1)
      expect(typeof key).toBe('string')
    })

    it('should use only valid charset characters', () => {
      // Mock to return values that test charset boundaries
      mockCrypto.getRandomValues.mockImplementation((array) => {
        array[0] = 0   // Should map to 'A'
        array[1] = 25  // Should map to 'Z'
        array[2] = 26  // Should map to 'a'
        array[3] = 51  // Should map to 'z'
        array[4] = 52  // Should map to '0'
        array[5] = 61  // Should map to '9'
        return array
      })

      const key = generateShortKey(6)
      
      // Verify all characters are from expected charset
      const validChars = /^[A-Za-z0-9]+$/
      expect(key).toMatch(validChars)
    })
  })

  describe('extractIdFromGid', () => {
    it('should extract numeric ID from Shopify Product GID', () => {
      const gid = 'gid://shopify/Product/123456789'
      const result = extractIdFromGid(gid)
      
      expect(result).toBe('123456789')
    })

    it('should extract numeric ID from Shopify ProductVariant GID', () => {
      const gid = 'gid://shopify/ProductVariant/987654321'
      const result = extractIdFromGid(gid)
      
      expect(result).toBe('987654321')
    })

    it('should handle GID with multiple slashes', () => {
      const gid = 'gid://shopify/Collection/Category/SubCategory/456'
      const result = extractIdFromGid(gid)
      
      expect(result).toBe('456')
    })

    it('should handle simple ID without GID format', () => {
      const gid = '123'
      const result = extractIdFromGid(gid)
      
      expect(result).toBe('123')
    })

    it('should handle empty string', () => {
      const gid = ''
      const result = extractIdFromGid(gid)
      
      expect(result).toBe('')
    })

    it('should handle GID ending with slash', () => {
      const gid = 'gid://shopify/Product/123/'
      const result = extractIdFromGid(gid)
      
      expect(result).toBe('')
    })

    it('should handle complex Shopify GIDs', () => {
      const testCases = [
        { gid: 'gid://shopify/Order/1234567890', expected: '1234567890' },
        { gid: 'gid://shopify/Customer/9876543210', expected: '9876543210' },
        { gid: 'gid://shopify/Metafield/555', expected: '555' }
      ]

      testCases.forEach(({ gid, expected }) => {
        expect(extractIdFromGid(gid)).toBe(expected)
      })
    })
  })

  describe('getStoreHandle', () => {
    it('should extract store handle from pathname', () => {
      mockWindow.location.pathname = '/store/test-shop/products'
      
      const handle = getStoreHandle()
      
      expect(handle).toBe('test-shop')
    })

    it('should extract store handle from root store path', () => {
      mockWindow.location.pathname = '/store/my-awesome-store'
      
      const handle = getStoreHandle()
      
      expect(handle).toBe('my-awesome-store')
    })

    it('should handle store handle with hyphens and numbers', () => {
      mockWindow.location.pathname = '/store/shop-123-test/dashboard'
      
      const handle = getStoreHandle()
      
      expect(handle).toBe('shop-123-test')
    })

    it('should return undefined for non-store paths', () => {
      mockWindow.location.pathname = '/admin/products'
      
      const handle = getStoreHandle()
      
      expect(handle).toBeUndefined()
    })

    it('should return undefined for root path', () => {
      mockWindow.location.pathname = '/'
      
      const handle = getStoreHandle()
      
      expect(handle).toBeUndefined()
    })

    it('should return undefined for empty pathname', () => {
      mockWindow.location.pathname = ''
      
      const handle = getStoreHandle()
      
      expect(handle).toBeUndefined()
    })

    it('should handle malformed store paths', () => {
      mockWindow.location.pathname = '/store/'

      const handle = getStoreHandle()

      expect(handle).toBeUndefined()
    })

    it('should return undefined during SSR (no window)', () => {
      // Temporarily set window to undefined to simulate SSR
      const originalWindow = global.window
      ;(global as any).window = undefined

      const handle = getStoreHandle()
      expect(handle).toBeUndefined()

      // Restore window
      global.window = originalWindow
    })
  })

  describe('appName constant', () => {
    it('should have correct app name', () => {
      expect(appName).toBe('Bulk Product Editor')
      expect(typeof appName).toBe('string')
    })
  })

  describe('modificationFields constant', () => {
    it('should have correct structure and required fields', () => {
      expect(Array.isArray(modificationFields)).toBe(true)
      expect(modificationFields.length).toBeGreaterThan(0)
      
      // Check that all fields have required properties
      modificationFields.forEach(field => {
        expect(field).toHaveProperty('label')
        expect(field).toHaveProperty('id')
        expect(field).toHaveProperty('value')
        expect(field).toHaveProperty('type')
        expect(typeof field.label).toBe('string')
        expect(typeof field.id).toBe('string')
        expect(typeof field.value).toBe('string')
      })
    })

    it('should contain expected product and variant fields', () => {
      const fieldIds = modificationFields.map(f => f.id)
      
      // Check for expected field categories
      expect(fieldIds).toContain('product_fields')
      expect(fieldIds).toContain('variant_fields')
      
      // Check for specific fields
      expect(fieldIds).toContain('category')
      expect(fieldIds).toContain('description')
      expect(fieldIds).toContain('price')
      expect(fieldIds).toContain('sku')
    })

    it('should have proper field types (HEADER vs ITEM)', () => {
      const headers = modificationFields.filter(f => f.type === SegmentType.HEADER)
      const items = modificationFields.filter(f => f.type === SegmentType.ITEM)

      expect(headers.length).toBeGreaterThan(0)
      expect(items.length).toBeGreaterThan(0)

      // Headers should be organizational
      expect(headers.some(h => h.id === 'product_fields')).toBe(true)
      expect(headers.some(h => h.id === 'variant_fields')).toBe(true)
    })

    it('should have unique IDs and values', () => {
      const ids = modificationFields.map(f => f.id)
      const values = modificationFields.map(f => f.value)
      
      expect(new Set(ids).size).toBe(ids.length) // All IDs unique
      expect(new Set(values).size).toBe(values.length) // All values unique
    })
  })
})
