#!/usr/bin/env tsx

import { rmSync, statSync, readdirSync } from 'fs'
import { resolve } from 'path'

/**
 * Cleanup script to remove test database directory
 * Run with: npx tsx tests/cleanup.ts
 */

const TEST_DB_DIR = resolve('./prisma/test-dbs')

function cleanupTestDatabases() {
  console.log('🧹 Cleaning up test database files...')

  try {
    // Check if test directory exists and get its size
    let totalSize = 0
    let fileCount = 0

    try {
      const files = readdirSync(TEST_DB_DIR)
      console.log(`Found test database directory with ${files.length} files:`)

      for (const file of files) {
        try {
          const filePath = `${TEST_DB_DIR}/${file}`
          const stats = statSync(filePath)
          const sizeKB = Math.round(stats.size / 1024)
          totalSize += stats.size
          fileCount++
          console.log(`  - ${file} (${sizeKB} KB)`)
        } catch (e) {
          // Ignore individual file errors
        }
      }
    } catch (e) {
      console.log('✅ No test database directory found to clean up')
      return
    }

    if (fileCount === 0) {
      console.log('✅ Test database directory is already empty')
      return
    }

    // Remove entire test directory
    rmSync(TEST_DB_DIR, { recursive: true, force: true })

    const totalSizeKB = Math.round(totalSize / 1024)
    console.log(`\n✅ Cleaned up test database directory (${fileCount} files, ${totalSizeKB} KB freed)`)

  } catch (e) {
    console.error('❌ Error during cleanup:', e)
  }
}

// Run cleanup if this script is executed directly
// In ES modules, we check if the script is the main module differently
if (import.meta.url === `file://${process.argv[1]}`) {
  cleanupTestDatabases()
}

export { cleanupTestDatabases }
