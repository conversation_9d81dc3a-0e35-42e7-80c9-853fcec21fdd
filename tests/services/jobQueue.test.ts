import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { setupTestEnv } from '../setup'

// Mock Bull queue using vi.hoisted to ensure proper hoisting
const { mockQueue, mockBull } = vi.hoisted(() => {
  const mockQueue = {
    add: vi.fn().mockResolvedValue({ id: 'mock-bull-job-id' }),
    process: vi.fn(),
    close: vi.fn(),
    clean: vi.fn(),
    getJobs: vi.fn(),
    getJob: vi.fn(),
    on: vi.fn(),
  }

  const mockBull = vi.fn(() => mockQueue)

  return { mockQueue, mockBull }
})

vi.mock('bull', () => ({
  default: mockBull,
}))

// Import after mocks are set up
import { getShopQueue, enqueueBulkUpdateJob, getQueueName, clearQueueCache } from '~/services/jobQueue.server'

// Mock Redis
const mockRedis = vi.fn(() => ({
  set: vi.fn(),
  get: vi.fn(),
  del: vi.fn(),
  disconnect: vi.fn(),
}))

vi.mock('ioredis', () => ({
  default: mockRedis,
}))

describe('Job Queue Service', () => {
  beforeEach(async () => {
    setupTestEnv()
    vi.clearAllMocks()

    // Reset mock to default behavior
    mockQueue.add.mockResolvedValue({ id: 'mock-bull-job-id' })

    // Clear queue cache for each test
    clearQueueCache()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('Shop-specific queue creation', () => {
    it('should create a queue with shop-specific name', async () => {
      // This test will fail until we implement the service
      
      const shopDomain = 'test-shop.myshopify.com'
      const queue = getShopQueue(shopDomain)
      
      expect(mockBull).toHaveBeenCalledWith(
        'bpe-testshopmyshopifycom',
        expect.objectContaining({
          redis: expect.objectContaining({
            host: expect.any(String),
            port: expect.any(Number),
          }),
          defaultJobOptions: expect.objectContaining({
            removeOnComplete: expect.any(Number),
            removeOnFail: expect.any(Number),
            attempts: expect.any(Number),
            backoff: expect.objectContaining({
              type: expect.any(String),
              delay: expect.any(Number),
            }),
          }),
        })
      )
      
      expect(queue).toBe(mockQueue)
    })

    it('should handle shop domains with dots correctly', async () => {
      
      const shopDomain = 'my.test.shop.myshopify.com'
      getShopQueue(shopDomain)
      
      expect(mockBull).toHaveBeenCalledWith(
        'bpe-mytestshopmyshopifycom',
        expect.any(Object)
      )
    })

    it('should use environment variables for Redis configuration', async () => {
      process.env.REDIS_HOST = 'custom-redis-host'
      process.env.REDIS_PORT = '6380'
      
      
      getShopQueue('test-shop.myshopify.com')
      
      expect(mockBull).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          redis: {
            host: 'custom-redis-host',
            port: 6380,
          },
        })
      )
    })
  })

  describe('Job enqueueing functions', () => {
    it('should enqueue a bulk update job', async () => {
      
      const jobData = {
        jobId: 'job-123',
        shopDomain: 'test-shop.myshopify.com',
        modifications: [
          { fieldType: 'PRODUCT', fieldName: 'title', fieldValue: 'New Title' }
        ],
        productIds: ['product-1', 'product-2'],
        options: {
          batchSize: 10,
          delayBetweenBatches: 1000,
        },
      }
      
      await enqueueBulkUpdateJob(jobData)
      
      expect(mockQueue.add).toHaveBeenCalledWith(
        'bulk-update',
        jobData,
        expect.objectContaining({
          jobId: 'job-123',
          removeOnComplete: true,
          removeOnFail: false,
        })
      )
    })

    it('should handle job enqueueing errors gracefully', async () => {
      mockQueue.add.mockRejectedValueOnce(new Error('Redis connection failed'))
      
      
      const jobData = {
        jobId: 'job-123',
        shopDomain: 'test-shop.myshopify.com',
        modifications: [],
        productIds: [],
        options: { batchSize: 10, delayBetweenBatches: 1000 },
      }
      
      await expect(enqueueBulkUpdateJob(jobData)).rejects.toThrow('Redis connection failed')
    })

    it('should return job information after enqueueing', async () => {
      const mockJob = { id: 'bull-job-id', data: { jobId: 'job-123' } }
      mockQueue.add.mockResolvedValueOnce(mockJob)
      
      
      const jobData = {
        jobId: 'job-123',
        shopDomain: 'test-shop.myshopify.com',
        modifications: [],
        productIds: [],
        options: { batchSize: 10, delayBetweenBatches: 1000 },
      }
      
      const result = await enqueueBulkUpdateJob(jobData)
      
      expect(result).toEqual({
        bullJobId: 'bull-job-id',
        jobId: 'job-123',
        queueName: 'bpe-testshopmyshopifycom',
      })
    })
  })

  describe('Utility functions', () => {
    it('should generate correct queue names', async () => {

      expect(getQueueName('test-shop.myshopify.com')).toBe('bpe-testshopmyshopifycom')
      expect(getQueueName('my.test.shop.myshopify.com')).toBe('bpe-mytestshopmyshopifycom')
      expect(getQueueName('simple-shop')).toBe('bpe-simpleshop')
    })
  })
})
