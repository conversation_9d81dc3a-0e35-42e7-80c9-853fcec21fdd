import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { setupTestEnv } from '../setup'

// Mock shopify.server to avoid environment issues
vi.mock('~/shopify.server', () => ({
  apiVersion: '2023-10',
}))

// Mock limiter for rate limiting
const mockRateLimiter = {
  removeTokens: vi.fn().mockResolvedValue(true),
}

vi.mock('limiter', () => ({
  RateLimiter: vi.fn(() => mockRateLimiter),
}))

// Mock fetch for GraphQL requests
const mockFetch = vi.fn()
global.fetch = mockFetch

// Import after mocks are set up
import { ShopifyApiService } from '~/services/shopifyApi.server'

describe('Shopify API Service', () => {
  beforeEach(async () => {
    setupTestEnv()
    vi.clearAllMocks()
    
    // Reset rate limiter mock
    mockRateLimiter.removeTokens.mockResolvedValue(true)
    
    // Reset fetch mock with default success response
    mockFetch.mockResolvedValue({
      ok: true,
      status: 200,
      json: vi.fn().mockResolvedValue({
        data: {
          productUpdate: {
            product: { id: 'gid://shopify/Product/123' },
            userErrors: []
          }
        }
      })
    })
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('ShopifyApiService initialization', () => {
    it('should create service with shop domain and access token', () => {
      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')

      expect(service).toBeDefined()
      expect(service.shopDomain).toBe('test-shop.myshopify.com')
      expect(service.accessToken).toBe('test-access-token')
    })

    it('should initialize rate limiter with correct limits', async () => {
      const { RateLimiter } = await import('limiter')

      new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')

      // Should create rate limiter with 10 requests per second (conservative for GraphQL)
      expect(RateLimiter).toHaveBeenCalledWith({ tokensPerInterval: 10, interval: 'second' })
    })
  })

  describe('GraphQL request handling', () => {
    it('should make GraphQL requests with proper headers', async () => {
      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')

      const mutation = `
        mutation productUpdate($input: ProductInput!) {
          productUpdate(input: $input) {
            product { id }
            userErrors { field message }
          }
        }
      `
      const variables = { input: { id: 'gid://shopify/Product/123', title: 'New Title' } }
      
      await service.graphqlRequest(mutation, variables)
      
      expect(mockFetch).toHaveBeenCalledWith(
        'https://test-shop.myshopify.com/admin/api/2023-10/graphql.json',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': 'test-access-token',
          },
          body: JSON.stringify({
            query: mutation,
            variables,
          }),
        }
      )
    })

    it('should respect rate limiting before making requests', async () => {
      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')
      
      const mutation = 'mutation { productUpdate(input: {}) { product { id } } }'
      
      await service.graphqlRequest(mutation, {})
      
      expect(mockRateLimiter.removeTokens).toHaveBeenCalledWith(1)
      expect(mockRateLimiter.removeTokens).toHaveBeenCalledBefore(mockFetch as any)
    })

    it('should handle GraphQL errors properly', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: vi.fn().mockResolvedValue({
          errors: [
            { message: 'Product not found', locations: [{ line: 2, column: 3 }] }
          ]
        })
      })

      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')
      
      await expect(service.graphqlRequest('mutation {}', {}))
        .rejects.toThrow('GraphQL Error: Product not found')
    })

    it('should handle HTTP errors properly', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests',
        text: vi.fn().mockResolvedValue('Rate limit exceeded')
      })

      
      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')
      
      await expect(service.graphqlRequest('mutation {}', {}))
        .rejects.toThrow('HTTP Error 429: Too Many Requests')
    })
  })

  describe('Product update mutations', () => {
    it('should update product with correct mutation', async () => {

      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')

      const productData = {
        id: 'gid://shopify/Product/123',
        title: 'Updated Product Title',
        vendor: 'New Vendor'
      }

      await service.updateProduct(productData)

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: expect.stringContaining('productUpdate'),
        })
      )

      const callBody = JSON.parse(mockFetch.mock.calls[0][1].body)
      expect(callBody.query).toContain('productUpdate')
      expect(callBody.variables.input).toEqual(productData)
    })

    it('should update product variant with correct mutation', async () => {
      // Mock the getProductIdFromVariant call (first call)
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          data: {
            productVariant: {
              product: {
                id: 'gid://shopify/Product/123'
              }
            }
          }
        })
      })

      // Mock the productVariantsBulkUpdate call (second call)
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          data: {
            productVariantsBulkUpdate: {
              productVariants: [{
                id: 'gid://shopify/ProductVariant/456',
                price: '29.99',
                sku: 'NEW-SKU-123'
              }],
              userErrors: []
            }
          }
        })
      })

      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')

      const variantData = {
        id: 'gid://shopify/ProductVariant/456',
        price: '29.99',
        sku: 'NEW-SKU-123'
      }

      await service.updateProductVariant(variantData)

      // Verify getProductIdFromVariant call
      const firstCallBody = JSON.parse(mockFetch.mock.calls[0][1].body)
      expect(firstCallBody.query).toContain('getVariantProduct')
      expect(firstCallBody.variables.id).toBe('gid://shopify/ProductVariant/456')

      // Verify productVariantsBulkUpdate call
      const secondCallBody = JSON.parse(mockFetch.mock.calls[1][1].body)
      expect(secondCallBody.query).toContain('productVariantsBulkUpdate')
      expect(secondCallBody.variables.variants).toEqual([variantData])
    })

    it('should handle bulk variant updates efficiently with updateProductVariants', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: vi.fn().mockResolvedValue({
          data: {
            productVariantsBulkUpdate: {
              productVariants: [
                { id: 'gid://shopify/ProductVariant/1' },
                { id: 'gid://shopify/ProductVariant/2' }
              ],
              userErrors: []
            }
          }
        })
      })
      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')

      const productId = 'gid://shopify/Product/123'
      const variants = [
        { id: 'gid://shopify/ProductVariant/1', price: '19.99' },
        { id: 'gid://shopify/ProductVariant/2', price: '29.99' }
      ]

      await service.updateProductVariants(productId, variants)

      const callBody = JSON.parse(mockFetch.mock.calls[0][1].body)
      expect(callBody.query).toContain('productVariantsBulkUpdate')
      expect(callBody.variables.productId).toBe(productId)
      expect(callBody.variables.variants).toEqual(variants)
    })

    it('should handle bulk variant updates efficiently with updateProductVariantsBulk (alias)', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: vi.fn().mockResolvedValue({
          data: {
            productVariantsBulkUpdate: {
              productVariants: [
                { id: 'gid://shopify/ProductVariant/1' },
                { id: 'gid://shopify/ProductVariant/2' }
              ],
              userErrors: []
            }
          }
        })
      })
      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')

      const productId = 'gid://shopify/Product/123'
      const variants = [
        { id: 'gid://shopify/ProductVariant/1', price: '19.99' },
        { id: 'gid://shopify/ProductVariant/2', price: '29.99' }
      ]

      await service.updateProductVariantsBulk(productId, variants)

      const callBody = JSON.parse(mockFetch.mock.calls[0][1].body)
      expect(callBody.query).toContain('productVariantsBulkUpdate')
      expect(callBody.variables.productId).toBe(productId)
      expect(callBody.variables.variants).toEqual(variants)
    })

    it('should enforce 100 variant limit for bulk updates', async () => {

      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')

      const productId = 'gid://shopify/Product/123'
      const variants = Array.from({ length: 101 }, (_, i) => ({
        id: `gid://shopify/ProductVariant/${i + 1}`,
        price: '19.99'
      }))

      await expect(service.updateProductVariants(productId, variants))
        .rejects.toThrow('Maximum 100 variants allowed per bulk update')
    })
  })

  describe('Error handling and validation', () => {
    it('should handle user errors from Shopify API', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: vi.fn().mockResolvedValue({
          data: {
            productUpdate: {
              product: null,
              userErrors: [
                { field: ['title'], message: 'Title cannot be blank' },
                { field: ['vendor'], message: 'Vendor is invalid' }
              ]
            }
          }
        })
      })
      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')

      await expect(service.updateProduct({ id: 'gid://shopify/Product/123', title: '' }))
        .rejects.toThrow('Shopify API Error: Title cannot be blank, Vendor is invalid')
    })

    it('should handle rate limiting with proper error message', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests',
        text: vi.fn().mockResolvedValue('Exceeded 2 calls per second for api client. Reduce request rates to resume uninterrupted service.')
      })
      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')

      await expect(service.updateProduct({ id: 'gid://shopify/Product/123' }))
        .rejects.toThrow('HTTP Error 429: Too Many Requests')
    })

    it('should validate required parameters', async () => {

      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')

      await expect(service.updateProduct({} as any))
        .rejects.toThrow('Product ID is required')

      await expect(service.updateProductVariant({} as any))
        .rejects.toThrow('Product variant ID is required')
    })

    it('should handle network errors gracefully', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error: Connection timeout'))
      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')

      await expect(service.updateProduct({ id: 'gid://shopify/Product/123' }))
        .rejects.toThrow('Network error: Connection timeout')
    })
  })

  describe('Rate limiting behavior', () => {
    it('should wait for rate limiter before each request', async () => {
      // Mock responses for getProductIdFromVariant and productVariantsBulkUpdate
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => ({
          data: {
            productVariant: {
              product: { id: 'gid://shopify/Product/123' }
            },
            productUpdate: { product: { id: 'gid://shopify/Product/1' } },
            productVariantsBulkUpdate: {
              productVariants: [{ id: 'gid://shopify/ProductVariant/1' }],
              userErrors: []
            }
          }
        })
      })

      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')

      // Make multiple requests
      await service.updateProduct({ id: 'gid://shopify/Product/1' })
      await service.updateProduct({ id: 'gid://shopify/Product/2' })
      await service.updateProductVariant({ id: 'gid://shopify/ProductVariant/1' })

      // Should call removeTokens for each GraphQL request
      // updateProduct: 1 call each (2 total)
      // updateProductVariant: 2 calls (getProductIdFromVariant + productVariantsBulkUpdate)
      // Total: 4 calls
      expect(mockRateLimiter.removeTokens).toHaveBeenCalledTimes(4)
      expect(mockRateLimiter.removeTokens).toHaveBeenCalledWith(1)
    })

    it('should handle rate limiter failures', async () => {
      mockRateLimiter.removeTokens.mockRejectedValueOnce(new Error('Rate limiter error'))
      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')

      await expect(service.updateProduct({ id: 'gid://shopify/Product/123' }))
        .rejects.toThrow('Rate limiter error')
    })
  })

  describe('Validation and error handling', () => {
    it('should validate shop domain format in constructor', async () => {

      expect(() => new ShopifyApiService('invalid-domain', 'test-token'))
        .toThrow('Invalid shop domain format: invalid-domain')

      expect(() => new ShopifyApiService('test-shop.com', 'test-token'))
        .toThrow('Invalid shop domain format: test-shop.com')
    })

    it('should validate access token format in constructor', async () => {

      expect(() => new ShopifyApiService('test-shop.myshopify.com', ''))
        .toThrow('Invalid access token format')

      expect(() => new ShopifyApiService('test-shop.myshopify.com', 'invalid token with spaces'))
        .toThrow('Invalid access token format')
    })
  })

  describe('Batch operations', () => {
    it('should process multiple products with batch update', async () => {

      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')

      const products = [
        { id: 'gid://shopify/Product/1', title: 'Product 1' },
        { id: 'gid://shopify/Product/2', title: 'Product 2' }
      ]

      const result = await service.batchUpdateProducts(products)

      expect(result.success).toBe(true)
      expect(result.updatedCount).toBe(2)
      expect(result.errors).toHaveLength(0)
      expect(mockFetch).toHaveBeenCalledTimes(2)
    })

    it('should handle partial failures in batch operations', async () => {
      // Mock one success and one failure
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: vi.fn().mockResolvedValue({
            data: { productUpdate: { product: { id: 'gid://shopify/Product/1' }, userErrors: [] } }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: vi.fn().mockResolvedValue({
            data: { productUpdate: { product: null, userErrors: [{ message: 'Product not found' }] } }
          })
        })
      const service = new ShopifyApiService('test-shop.myshopify.com', 'test-access-token')

      const products = [
        { id: 'gid://shopify/Product/1', title: 'Product 1' },
        { id: 'gid://shopify/Product/2', title: 'Product 2' }
      ]

      const result = await service.batchUpdateProducts(products)

      expect(result.success).toBe(false)
      expect(result.updatedCount).toBe(1)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0]).toContain('Product gid://shopify/Product/2')
    })
  })
})
