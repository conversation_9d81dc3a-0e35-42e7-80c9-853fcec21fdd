import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock environment variables before importing
const mockEnv = {
  SHOPIFY_API_KEY: 'test-api-key',
  SHOPIFY_API_SECRET: 'test-api-secret',
  SCOPES: 'read_products,write_products,read_orders',
  SHOPIFY_APP_URL: 'https://test-app.ngrok.io',
  SHOP_CUSTOM_DOMAIN: 'custom-domain.com'
}

// Mock process.env
Object.defineProperty(process, 'env', {
  value: { ...process.env, ...mockEnv },
  writable: true
})

// Mock Shopify dependencies
vi.mock('@shopify/shopify-app-remix/server', () => ({
  ApiVersion: {
    January25: '2025-01'
  },
  AppDistribution: {
    AppStore: 'app_store'
  },
  shopifyApp: vi.fn((config) => ({
    addDocumentResponseHeaders: vi.fn(),
    authenticate: {
      admin: vi.fn(),
      webhook: vi.fn()
    },
    unauthenticated: vi.fn(),
    login: vi.fn(),
    registerWebhooks: vi.fn(),
    sessionStorage: 'mocked-session-storage'
  }))
}))

vi.mock('@shopify/shopify-app-session-storage-prisma', () => ({
  PrismaSessionStorage: vi.fn().mockImplementation(() => 'mocked-prisma-storage')
}))

vi.mock('~/db.server', () => ({
  default: 'mocked-prisma-client'
}))

describe('Shopify Server Configuration Regression Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.resetModules()
  })

  describe('shopifyApp configuration', () => {
    it('should configure shopifyApp with correct API credentials', async () => {
      const { shopifyApp } = await import('@shopify/shopify-app-remix/server')

      // Import the module to trigger configuration
      await import('~/shopify.server')

      expect(shopifyApp).toHaveBeenCalledTimes(1)

      const config = (shopifyApp as any).mock.calls[0][0]

      expect(config.apiKey).toBe('test-api-key')
      expect(config.apiSecretKey).toBe('test-api-secret')
      expect(config.appUrl).toBe('https://test-app.ngrok.io')
    })

    it('should configure correct API version', async () => {
      const { shopifyApp } = await import('@shopify/shopify-app-remix/server')
      
      await import('~/shopify.server')
      
      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.apiVersion).toBe('2025-01')
    })

    it('should configure correct scopes from environment', async () => {
      const { shopifyApp } = await import('@shopify/shopify-app-remix/server')
      
      await import('~/shopify.server')
      
      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.scopes).toEqual(['read_products', 'write_products', 'read_orders'])
    })

    it('should configure auth path prefix', async () => {
      const { shopifyApp } = await import('@shopify/shopify-app-remix/server')
      
      await import('~/shopify.server')
      
      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.authPathPrefix).toBe('/auth')
    })

    it('should configure app store distribution', async () => {
      const { shopifyApp } = await import('@shopify/shopify-app-remix/server')
      
      await import('~/shopify.server')
      
      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.distribution).toBe('app_store')
    })

    it('should configure Prisma session storage', async () => {
      const { PrismaSessionStorage } = await import('@shopify/shopify-app-session-storage-prisma')
      const { shopifyApp } = await import('@shopify/shopify-app-remix/server')

      await import('~/shopify.server')

      expect(PrismaSessionStorage).toHaveBeenCalledWith('mocked-prisma-client')

      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.sessionStorage).toBeDefined()
    })

    it('should configure future flags correctly', async () => {
      const { shopifyApp } = await import('@shopify/shopify-app-remix/server')
      
      await import('~/shopify.server')
      
      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.future).toEqual({
        unstable_newEmbeddedAuthStrategy: true,
        removeRest: true
      })
    })

    it('should include custom shop domain when provided', async () => {
      const { shopifyApp } = await import('@shopify/shopify-app-remix/server')
      
      await import('~/shopify.server')
      
      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.customShopDomains).toEqual(['custom-domain.com'])
    })

    it('should handle missing custom shop domain', async () => {
      // Reset environment without custom domain
      process.env.SHOP_CUSTOM_DOMAIN = ''
      
      // Clear module cache to force re-import
      vi.resetModules()
      
      const { shopifyApp } = await import('@shopify/shopify-app-remix/server')
      
      await import('~/shopify.server')
      
      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.customShopDomains).toBeUndefined()
    })

    it('should handle empty API secret gracefully', async () => {
      // Reset environment with empty secret
      process.env.SHOPIFY_API_SECRET = ''
      
      vi.resetModules()
      
      const { shopifyApp } = await import('@shopify/shopify-app-remix/server')
      
      await import('~/shopify.server')
      
      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.apiSecretKey).toBe('')
    })

    it('should handle empty app URL gracefully', async () => {
      // Reset environment with empty URL
      process.env.SHOPIFY_APP_URL = ''
      
      vi.resetModules()
      
      const { shopifyApp } = await import('@shopify/shopify-app-remix/server')
      
      await import('~/shopify.server')
      
      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.appUrl).toBe('')
    })

    it('should handle missing scopes gracefully', async () => {
      // Reset environment without scopes
      delete process.env.SCOPES
      
      vi.resetModules()
      
      const { shopifyApp } = await import('@shopify/shopify-app-remix/server')
      
      await import('~/shopify.server')
      
      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.scopes).toBeUndefined()
    })
  })

  describe('exported functions and constants', () => {
    it('should export correct API version constant', async () => {
      vi.resetModules()
      
      const shopifyServer = await import('~/shopify.server')
      
      expect(shopifyServer.apiVersion).toBe('2025-01')
    })

    it('should export all required authentication functions', async () => {
      vi.resetModules()
      
      const shopifyServer = await import('~/shopify.server')
      
      expect(shopifyServer.authenticate).toBeDefined()
      expect(shopifyServer.unauthenticated).toBeDefined()
      expect(shopifyServer.login).toBeDefined()
      expect(shopifyServer.registerWebhooks).toBeDefined()
      expect(shopifyServer.sessionStorage).toBeDefined()
      expect(shopifyServer.addDocumentResponseHeaders).toBeDefined()
    })

    it('should export default shopify app instance', async () => {
      vi.resetModules()
      
      const shopifyServer = await import('~/shopify.server')
      
      expect(shopifyServer.default).toBeDefined()
      expect(typeof shopifyServer.default).toBe('object')
    })

    it('should maintain function references from shopify app', async () => {
      vi.resetModules()
      
      const shopifyServer = await import('~/shopify.server')
      
      // These should be references to the same functions from the shopify app instance
      expect(shopifyServer.authenticate).toBe(shopifyServer.default.authenticate)
      expect(shopifyServer.sessionStorage).toBe(shopifyServer.default.sessionStorage)
      expect(shopifyServer.addDocumentResponseHeaders).toBe(shopifyServer.default.addDocumentResponseHeaders)
    })
  })

  describe('configuration validation', () => {
    it('should require API key for proper configuration', async () => {
      // Test with missing API key
      delete process.env.SHOPIFY_API_KEY
      
      vi.resetModules()
      
      const { shopifyApp } = await import('@shopify/shopify-app-remix/server')
      
      await import('~/shopify.server')
      
      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.apiKey).toBeUndefined()
    })

    it('should handle complex scopes configuration', async () => {
      // Test with complex scopes
      process.env.SCOPES = 'read_products,write_products,read_orders,write_orders,read_customers'
      
      vi.resetModules()
      
      const { shopifyApp } = await import('@shopify/shopify-app-remix/server')
      
      await import('~/shopify.server')
      
      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.scopes).toEqual([
        'read_products',
        'write_products', 
        'read_orders',
        'write_orders',
        'read_customers'
      ])
    })

    it('should handle single scope configuration', async () => {
      // Test with single scope
      process.env.SCOPES = 'read_products'
      
      vi.resetModules()
      
      const { shopifyApp } = await import('@shopify/shopify-app-remix/server')
      
      await import('~/shopify.server')
      
      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.scopes).toEqual(['read_products'])
    })

    it('should handle scopes with whitespace', async () => {
      // Test with scopes containing whitespace
      process.env.SCOPES = ' read_products , write_products , read_orders '
      
      vi.resetModules()
      
      const { shopifyApp } = await import('@shopify/shopify-app-remix/server')
      
      await import('~/shopify.server')
      
      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.scopes).toEqual([' read_products ', ' write_products ', ' read_orders '])
    })
  })

  describe('integration with dependencies', () => {
    it('should properly integrate with Prisma client', async () => {
      const { PrismaSessionStorage } = await import('@shopify/shopify-app-session-storage-prisma')
      
      vi.resetModules()
      
      await import('~/shopify.server')
      
      expect(PrismaSessionStorage).toHaveBeenCalledWith('mocked-prisma-client')
    })

    it('should use correct Shopify API version enum', async () => {
      const { shopifyApp, ApiVersion } = await import('@shopify/shopify-app-remix/server')
      
      vi.resetModules()
      
      await import('~/shopify.server')
      
      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.apiVersion).toBe(ApiVersion.January25)
    })

    it('should use correct app distribution enum', async () => {
      const { shopifyApp, AppDistribution } = await import('@shopify/shopify-app-remix/server')
      
      vi.resetModules()
      
      await import('~/shopify.server')
      
      const config = (shopifyApp as any).mock.calls[0][0]
      expect(config.distribution).toBe(AppDistribution.AppStore)
    })
  })
})
