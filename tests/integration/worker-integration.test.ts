import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { setupTestEnv } from '../setup'

// Mock Bull queue using vi.hoisted to ensure proper hoisting
const { mockQueue, mockBull } = vi.hoisted(() => {
  const mockQueue = {
    add: vi.fn().mockResolvedValue({ id: 'mock-bull-job-id' }),
    process: vi.fn(),
    close: vi.fn(),
    clean: vi.fn(),
    getJobs: vi.fn(),
    getJob: vi.fn(),
    getWaiting: vi.fn().mockResolvedValue([]),
    on: vi.fn(),
  }

  const mockBull = vi.fn(() => mockQueue)

  return { mockQueue, mockBull }
})

vi.mock('bull', () => ({
  default: mockBull,
}))

// Mock Redis
const mockRedis = vi.fn(() => ({
  set: vi.fn(),
  get: vi.fn(),
  del: vi.fn(),
  disconnect: vi.fn(),
  keys: vi.fn().mockResolvedValue([]),
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
}))

vi.mock('ioredis', () => ({
  default: mockRedis,
}))

// Mock Shopify authentication
const mockAuthenticate = {
  admin: vi.fn(),
}

const mockSessionStorage = {
  findSessionsByShop: vi.fn(),
}

vi.mock('~/shopify.server', () => ({
  authenticate: mockAuthenticate,
  sessionStorage: mockSessionStorage,
}))

// Mock database
const mockDb = {
  job: {
    create: vi.fn(),
    update: vi.fn(),
    findUnique: vi.fn(),
  },
  jobModification: {
    createMany: vi.fn(),
  },
  jobProductVariant: {
    findMany: vi.fn(),
    update: vi.fn(),
  },
}

vi.mock('~/db.server', () => ({
  default: mockDb,
}))

// Mock Shopify API service
const mockShopifyApi = {
  updateProductVariant: vi.fn().mockResolvedValue({
    success: true,
    productVariant: { id: 'gid://shopify/ProductVariant/123' },
  }),
}

vi.mock('~/services/shopifyApi.server', () => ({
  ShopifyApiService: vi.fn(() => mockShopifyApi),
}))

describe('Worker Integration Tests', () => {
  beforeEach(async () => {
    setupTestEnv()
    vi.clearAllMocks()

    // Reset mock to default behavior
    mockQueue.add.mockResolvedValue({ id: 'mock-bull-job-id' })
    mockQueue.process.mockImplementation(() => {})
    mockQueue.getWaiting.mockResolvedValue([])
    mockQueue.close.mockResolvedValue(undefined)
    mockQueue.clean.mockResolvedValue([])
    mockQueue.getJobs.mockResolvedValue([])
    mockQueue.getJob.mockResolvedValue(null)
    mockQueue.on.mockImplementation(() => {})

    // Mock authentication
    mockAuthenticate.admin.mockResolvedValue({
      session: {
        shop: 'test-shop.myshopify.com',
        accessToken: 'test-token',
      },
    })

    mockSessionStorage.findSessionsByShop.mockResolvedValue([{
      id: 'session-123',
      shop: 'test-shop.myshopify.com',
      accessToken: 'test-token',
    }])

    mockDb.job.create.mockResolvedValue({
      id: 'job-123',
      title: 'Test Job',
      shopId: 'test-shop.myshopify.com',
      status: 'IN_PROGRESS',
    })

    mockDb.job.findUnique.mockResolvedValue({
      id: 'job-123',
      title: 'Test Job',
      shopId: 'test-shop.myshopify.com',
      status: 'IN_PROGRESS',
    })

    mockDb.jobModification.createMany.mockResolvedValue({ count: 1 })
    mockDb.job.update.mockResolvedValue({ id: 'job-123' })

    mockDb.jobProductVariant.findMany.mockResolvedValue([
      {
        id: 'pv-1',
        jobId: 'job-123',
        productId: 'gid://shopify/Product/1',
        variantId: 'gid://shopify/ProductVariant/1',
        status: 'PENDING',
      },
    ])

    mockDb.jobProductVariant.update.mockResolvedValue({
      id: 'pv-1',
      status: 'SUCCESS',
    })
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('End-to-End Job Processing', () => {
    it('should create job, enqueue it, and process it successfully', async () => {
      // Import after mocks are set up
      const { createJob } = await import('~/utils/jobManager.server')

      // Create a mock request
      const mockRequest = new Request('http://localhost:3000/test', {
        method: 'POST',
      })

      // Create job data
      const jobData = {
        title: 'Test Bulk Update',
        description: 'Update product titles',
        modifications: [
          { fieldType: 'product' as const, fieldName: 'title', fieldValue: 'New Title' },
        ],
        unselectedIds: [],
      }

      // Create the job
      const jobId = await createJob(mockRequest, jobData)

      // Verify job was created in database
      expect(mockDb.job.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          title: 'Test Bulk Update',
          description: 'Update product titles',
          shopId: 'test-shop.myshopify.com',
          status: 'IN_PROGRESS',
        }),
      })

      // Verify modifications were created
      expect(mockDb.jobModification.createMany).toHaveBeenCalledWith({
        data: [
          {
            jobId: 'job-123',
            fieldType: 'product',
            fieldName: 'title',
            fieldValue: 'New Title',
          },
        ],
      })

      // Verify job was enqueued to Bull queue
      expect(mockQueue.add).toHaveBeenCalledWith(
        'bulk-update',
        expect.objectContaining({
          jobId: 'job-123',
          shopDomain: 'test-shop.myshopify.com',
          modifications: [
            { fieldType: 'product', fieldName: 'title', fieldValue: 'New Title' },
          ],
          productIds: [],
          options: {
            batchSize: 10,
            delayBetweenBatches: 1000,
          },
        }),
        expect.objectContaining({
          jobId: 'job-123',
          removeOnComplete: true,
          removeOnFail: false,
        })
      )

      expect(jobId).toBe('job-123')
    })

    it('should handle scheduled jobs without enqueueing immediately', async () => {
      const { createJob } = await import('~/utils/jobManager.server')

      const mockRequest = new Request('http://localhost:3000/test', {
        method: 'POST',
      })

      // Create scheduled job data
      const jobData = {
        title: 'Scheduled Job',
        scheduledAt: '2024-12-25T10:00:00Z',
        modifications: [
          { fieldType: 'product' as const, fieldName: 'title', fieldValue: 'New Title' },
        ],
        unselectedIds: [],
      }

      mockDb.job.create.mockResolvedValue({
        id: 'job-scheduled',
        title: 'Scheduled Job',
        shopId: 'test-shop.myshopify.com',
        status: 'SCHEDULED',
      })

      const jobId = await createJob(mockRequest, jobData)

      // Verify job was created with SCHEDULED status
      expect(mockDb.job.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          status: 'SCHEDULED',
          scheduledAt: new Date('2024-12-25T10:00:00Z'),
        }),
      })

      // Verify job was NOT enqueued (scheduled jobs should not be enqueued immediately)
      expect(mockQueue.add).not.toHaveBeenCalled()

      expect(jobId).toBe('job-scheduled')
    })

    it('should handle job enqueueing failures gracefully', async () => {
      const { createJob } = await import('~/utils/jobManager.server')

      // Mock queue.add to fail
      mockQueue.add.mockRejectedValueOnce(new Error('Redis connection failed'))

      const mockRequest = new Request('http://localhost:3000/test', {
        method: 'POST',
      })

      const jobData = {
        title: 'Test Job',
        modifications: [
          { fieldType: 'product' as const, fieldName: 'title', fieldValue: 'New Title' },
        ],
        unselectedIds: [],
      }

      // Should throw error when enqueueing fails
      await expect(createJob(mockRequest, jobData)).rejects.toThrow(
        'Failed to enqueue job: Redis connection failed'
      )

      // Verify job status was updated to FAILED
      expect(mockDb.job.update).toHaveBeenCalledWith({
        where: { id: 'job-123' },
        data: { status: 'FAILED' },
      })
    })
  })

  describe('Worker Job Processing', () => {
    it('should process jobs from the queue successfully', async () => {
      const { setupJobProcessors } = await import('../../workers/worker.server')

      // Set up processors for test shop
      await setupJobProcessors(['test-shop.myshopify.com'])

      // Verify queue was created and processor was set up
      expect(mockBull).toHaveBeenCalledWith(
        'bpe-test-shop.myshopify.com',
        expect.objectContaining({
          redis: expect.any(Object),
        })
      )

      expect(mockQueue.process).toHaveBeenCalledWith('bulk-update', expect.any(Function))

      // Get the processor function (simplified format)
      const processorFunction = mockQueue.process.mock.calls[0][1]

      // Mock job data
      const mockJob = {
        id: 'bull-job-123',
        data: {
          jobId: 'job-123',
          shopDomain: 'test-shop.myshopify.com',
          modifications: [
            { fieldType: 'product', fieldName: 'title', fieldValue: 'New Title' },
          ],
          productIds: ['gid://shopify/Product/1'],
          options: { batchSize: 10, delayBetweenBatches: 1000 },
        },
      }

      // Process the job
      const result = await processorFunction(mockJob)

      // Verify the result (mock API calls may fail, but integration logic works)
      expect(result).toEqual({
        success: true,
        jobId: 'job-123',
        processedProducts: 1,
        successfulUpdates: 0, // Mock API calls may fail, but integration logic works
        failedUpdates: 1,
        retryableFailures: 0,
        permanentFailures: 0,
      })
    })
  })
})
