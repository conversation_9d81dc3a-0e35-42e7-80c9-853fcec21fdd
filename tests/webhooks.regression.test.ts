import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock the authenticate function
const mockAuthenticate = {
  webhook: vi.fn()
}

// Mock the database operations
const mockDb = {
  session: {
    deleteMany: vi.fn(),
    update: vi.fn(),
    create: vi.fn(),
    findMany: vi.fn(),
    findUnique: vi.fn()
  }
}

vi.mock('~/shopify.server', () => ({
  authenticate: mockAuthenticate
}))

vi.mock('~/db.server', () => ({
  default: mockDb
}))

describe('Webhook Handlers Regression Tests', () => {
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()
    vi.resetModules()
  })

  describe('webhooks.app.uninstalled', () => {
    it('should delete sessions when app is uninstalled with valid session', async () => {
      // Mock authenticate.webhook to return valid session
      mockAuthenticate.webhook.mockResolvedValue({
        shop: 'test-shop.myshopify.com',
        session: { id: 'test-session-1' },
        topic: 'app/uninstalled'
      })

      // Mock database deleteMany operation
      mockDb.session.deleteMany.mockResolvedValue({ count: 2 })

      const mockRequest = new Request('http://localhost/webhooks/app/uninstalled', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ shop_domain: 'test-shop.myshopify.com' })
      })

      // Import and call the action
      const { action } = await import('~/routes/webhooks.app.uninstalled')
      const response = await action({ request: mockRequest, params: {}, context: {} })

      // Verify response
      expect(response).toBeInstanceOf(Response)
      expect(response.status).toBe(200)

      // Verify sessions deleteMany was called with correct shop
      expect(mockDb.session.deleteMany).toHaveBeenCalledWith({
        where: { shop: 'test-shop.myshopify.com' }
      })

      // Verify authenticate.webhook was called
      expect(mockAuthenticate.webhook).toHaveBeenCalledWith(mockRequest)
    })

    it('should handle uninstall webhook when session is null', async () => {
      // Mock authenticate.webhook to return null session (already uninstalled)
      mockAuthenticate.webhook.mockResolvedValue({
        shop: 'test-shop.myshopify.com',
        session: null,
        topic: 'app/uninstalled'
      })

      const mockRequest = new Request('http://localhost/webhooks/app/uninstalled', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ shop_domain: 'test-shop.myshopify.com' })
      })

      const { action } = await import('~/routes/webhooks.app.uninstalled')
      const response = await action({ request: mockRequest, params: {}, context: {} })

      // Verify response
      expect(response).toBeInstanceOf(Response)
      expect(response.status).toBe(200)

      // Verify sessions deleteMany was NOT called (since session was null)
      expect(mockDb.session.deleteMany).not.toHaveBeenCalled()

      expect(mockAuthenticate.webhook).toHaveBeenCalledWith(mockRequest)
    })

    it('should handle multiple shops correctly', async () => {
      // Mock uninstall for shop1 only
      mockAuthenticate.webhook.mockResolvedValue({
        shop: 'shop1.myshopify.com',
        session: { id: 'shop1-session' },
        topic: 'app/uninstalled'
      })

      mockDb.session.deleteMany.mockResolvedValue({ count: 1 })

      const mockRequest = new Request('http://localhost/webhooks/app/uninstalled', {
        method: 'POST'
      })

      const { action } = await import('~/routes/webhooks.app.uninstalled')
      await action({ request: mockRequest, params: {}, context: {} })

      // Verify only shop1 sessions were targeted for deletion
      expect(mockDb.session.deleteMany).toHaveBeenCalledWith({
        where: { shop: 'shop1.myshopify.com' }
      })
    })

    it('should handle authentication errors gracefully', async () => {
      mockAuthenticate.webhook.mockRejectedValue(new Error('Authentication failed'))

      const mockRequest = new Request('http://localhost/webhooks/app/uninstalled', {
        method: 'POST'
      })

      const { action } = await import('~/routes/webhooks.app.uninstalled')

      await expect(action({
        request: mockRequest,
        params: {},
        context: {}
      })).rejects.toThrow('Authentication failed')
    })

    it('should handle database errors gracefully', async () => {
      mockAuthenticate.webhook.mockResolvedValue({
        shop: 'test-shop.myshopify.com',
        session: { id: 'test-session' },
        topic: 'app/uninstalled'
      })

      // Mock database error
      mockDb.session.deleteMany.mockRejectedValue(new Error('Database error'))

      const mockRequest = new Request('http://localhost/webhooks/app/uninstalled', {
        method: 'POST'
      })

      const { action } = await import('~/routes/webhooks.app.uninstalled')

      await expect(action({
        request: mockRequest,
        params: {},
        context: {}
      })).rejects.toThrow('Database error')
    })
  })

  describe('webhooks.app.scopes_update', () => {
    it('should update session scope when scopes are updated', async () => {
      // Mock authenticate.webhook with updated scopes
      mockAuthenticate.webhook.mockResolvedValue({
        shop: 'test-shop.myshopify.com',
        session: { id: 'test-session-1' },
        topic: 'app/scopes_update',
        payload: {
          current: ['read_products', 'write_products', 'read_orders']
        }
      })

      mockDb.session.update.mockResolvedValue({
        id: 'test-session-1',
        scope: 'read_products,write_products,read_orders'
      })

      const mockRequest = new Request('http://localhost/webhooks/app/scopes_update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          current: ['read_products', 'write_products', 'read_orders']
        })
      })

      const { action } = await import('~/routes/webhooks.app.scopes_update')
      const response = await action({ request: mockRequest, params: {}, context: {} })

      // Verify response
      expect(response).toBeInstanceOf(Response)
      expect(response.status).toBe(200)

      // Verify session update was called with correct data
      expect(mockDb.session.update).toHaveBeenCalledWith({
        where: { id: 'test-session-1' },
        data: { scope: 'read_products,write_products,read_orders' }
      })
      expect(mockAuthenticate.webhook).toHaveBeenCalledWith(mockRequest)
    })

    it('should handle scopes update when session is null', async () => {
      // Mock authenticate.webhook with null session
      mockAuthenticate.webhook.mockResolvedValue({
        shop: 'test-shop.myshopify.com',
        session: null,
        topic: 'app/scopes_update',
        payload: {
          current: ['read_products', 'write_products']
        }
      })

      const mockRequest = new Request('http://localhost/webhooks/app/scopes_update', {
        method: 'POST'
      })

      const { action } = await import('~/routes/webhooks.app.scopes_update')
      const response = await action({ request: mockRequest, params: {}, context: {} })

      // Should complete without error even with null session
      expect(response).toBeInstanceOf(Response)
      expect(response.status).toBe(200)
      expect(mockDb.session.update).not.toHaveBeenCalled()
      expect(mockAuthenticate.webhook).toHaveBeenCalledWith(mockRequest)
    })

    it('should handle single scope update', async () => {
      mockAuthenticate.webhook.mockResolvedValue({
        shop: 'test-shop.myshopify.com',
        session: { id: 'test-session-2' },
        topic: 'app/scopes_update',
        payload: {
          current: ['read_products']
        }
      })

      mockDb.session.update.mockResolvedValue({
        id: 'test-session-2',
        scope: 'read_products'
      })

      const mockRequest = new Request('http://localhost/webhooks/app/scopes_update', {
        method: 'POST'
      })

      const { action } = await import('~/routes/webhooks.app.scopes_update')
      await action({ request: mockRequest, params: {}, context: {} })

      expect(mockDb.session.update).toHaveBeenCalledWith({
        where: { id: 'test-session-2' },
        data: { scope: 'read_products' }
      })
    })

    it('should handle empty scopes array', async () => {
      mockAuthenticate.webhook.mockResolvedValue({
        shop: 'test-shop.myshopify.com',
        session: { id: 'test-session-3' },
        topic: 'app/scopes_update',
        payload: {
          current: []
        }
      })

      mockDb.session.update.mockResolvedValue({
        id: 'test-session-3',
        scope: ''
      })

      const mockRequest = new Request('http://localhost/webhooks/app/scopes_update', {
        method: 'POST'
      })

      const { action } = await import('~/routes/webhooks.app.scopes_update')
      await action({ request: mockRequest, params: {}, context: {} })

      expect(mockDb.session.update).toHaveBeenCalledWith({
        where: { id: 'test-session-3' },
        data: { scope: '' }
      })
    })

    it('should handle authentication errors gracefully', async () => {
      mockAuthenticate.webhook.mockRejectedValue(new Error('Webhook authentication failed'))

      const mockRequest = new Request('http://localhost/webhooks/app/scopes_update', {
        method: 'POST'
      })

      const { action } = await import('~/routes/webhooks.app.scopes_update')

      await expect(action({
        request: mockRequest,
        params: {},
        context: {}
      })).rejects.toThrow('Webhook authentication failed')
    })

    it('should handle database update errors gracefully', async () => {
      mockAuthenticate.webhook.mockResolvedValue({
        shop: 'test-shop.myshopify.com',
        session: { id: 'test-session-4' },
        topic: 'app/scopes_update',
        payload: {
          current: ['read_products', 'write_products']
        }
      })

      // Mock database error
      mockDb.session.update.mockRejectedValue(new Error('Database update failed'))

      const mockRequest = new Request('http://localhost/webhooks/app/scopes_update', {
        method: 'POST'
      })

      const { action } = await import('~/routes/webhooks.app.scopes_update')

      await expect(action({
        request: mockRequest,
        params: {},
        context: {}
      })).rejects.toThrow('Database update failed')
    })

    it('should handle complex scope arrays with special characters', async () => {
      mockAuthenticate.webhook.mockResolvedValue({
        shop: 'test-shop.myshopify.com',
        session: { id: 'test-session-5' },
        topic: 'app/scopes_update',
        payload: {
          current: ['read_products', 'write_products', 'read_customers', 'write_orders']
        }
      })

      mockDb.session.update.mockResolvedValue({
        id: 'test-session-5',
        scope: 'read_products,write_products,read_customers,write_orders'
      })

      const mockRequest = new Request('http://localhost/webhooks/app/scopes_update', {
        method: 'POST'
      })

      const { action } = await import('~/routes/webhooks.app.scopes_update')
      await action({ request: mockRequest, params: {}, context: {} })

      expect(mockDb.session.update).toHaveBeenCalledWith({
        where: { id: 'test-session-5' },
        data: { scope: 'read_products,write_products,read_customers,write_orders' }
      })
    })
  })
})
