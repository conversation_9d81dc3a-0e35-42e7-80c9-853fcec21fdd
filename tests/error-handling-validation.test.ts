/**
 * Validation test for enhanced error handling and recovery
 */

import { describe, it, expect, vi, beforeAll } from 'vitest'

// Set up environment variables before importing modules
process.env.SHOPIFY_API_KEY = 'test-api-key'
process.env.SHOPIFY_API_SECRET = 'test-api-secret'
process.env.SHOPIFY_APP_URL = 'https://test-app.ngrok.io'
process.env.SCOPES = 'read_products,write_products'
process.env.DATABASE_URL = 'file:./test.db'
process.env.NODE_ENV = 'test'

// Mock Shopify app to avoid configuration issues
vi.mock('../app/shopify.server', () => ({
  default: {
    authenticate: {
      admin: vi.fn(),
    },
    sessionStorage: {
      findSessionsByShop: vi.fn(),
    },
  },
}))

import { classifyError, shouldMoveToDeadLetter, retryWithBackoff } from '../app/services/jobProcessor.server'

describe('Enhanced Error Handling', () => {
  describe('classifyError', () => {
    it('should classify rate limit errors as retryable', () => {
      const error = new Error('Rate limit exceeded')
      const classification = classifyError(error)
      
      expect(classification.isRetryable).toBe(true)
      expect(classification.category).toBe('RATE_LIMIT')
      expect(classification.retryAfter).toBe(60)
    })

    it('should classify network errors as retryable', () => {
      const error = new Error('Network timeout occurred')
      const classification = classifyError(error)
      
      expect(classification.isRetryable).toBe(true)
      expect(classification.category).toBe('NETWORK')
      expect(classification.retryAfter).toBe(30)
    })

    it('should classify validation errors as permanent', () => {
      const error = new Error('Invalid product data')
      const classification = classifyError(error)
      
      expect(classification.isRetryable).toBe(false)
      expect(classification.category).toBe('VALIDATION')
    })

    it('should classify permission errors as permanent', () => {
      const error = new Error('Unauthorized access')
      const classification = classifyError(error)
      
      expect(classification.isRetryable).toBe(false)
      expect(classification.category).toBe('PERMISSION')
    })

    it('should classify unknown errors as permanent by default', () => {
      const error = new Error('Something weird happened')
      const classification = classifyError(error)
      
      expect(classification.isRetryable).toBe(false)
      expect(classification.category).toBe('UNKNOWN')
    })
  })

  describe('shouldMoveToDeadLetter', () => {
    it('should move permanent errors to dead letter immediately', () => {
      const error = new Error('Invalid data')
      const shouldMove = shouldMoveToDeadLetter(error, 1, 3)
      
      expect(shouldMove).toBe(true)
    })

    it('should move retryable errors to dead letter after max attempts', () => {
      const error = new Error('Network timeout')
      const shouldMove = shouldMoveToDeadLetter(error, 3, 3)
      
      expect(shouldMove).toBe(true)
    })

    it('should not move retryable errors before max attempts', () => {
      const error = new Error('Rate limit exceeded')
      const shouldMove = shouldMoveToDeadLetter(error, 1, 3)
      
      expect(shouldMove).toBe(false)
    })
  })

  describe('retryWithBackoff', () => {
    it('should retry retryable errors with mocked delays', async () => {
      // Mock the delay function to avoid real delays in tests
      const originalSetTimeout = global.setTimeout
      global.setTimeout = vi.fn((callback) => {
        callback()
        return 1 as any
      }) as any

      let attempts = 0
      const mockFn = vi.fn().mockImplementation(() => {
        attempts++
        if (attempts < 3) {
          throw new Error('Network error')
        }
        return 'success'
      })

      const result = await retryWithBackoff(mockFn, 3, 10, classifyError)

      expect(result).toBe('success')
      expect(attempts).toBe(3)

      // Restore original setTimeout
      global.setTimeout = originalSetTimeout
    })

    it('should not retry permanent errors', async () => {
      let attempts = 0
      const mockFn = vi.fn().mockImplementation(() => {
        attempts++
        throw new Error('Invalid data')
      })

      await expect(retryWithBackoff(mockFn, 3, 10, classifyError)).rejects.toThrow('Invalid data')
      expect(attempts).toBe(1)
    })

    it('should succeed on first try when no error', async () => {
      let attempts = 0
      const mockFn = vi.fn().mockImplementation(() => {
        attempts++
        return 'immediate success'
      })

      const result = await retryWithBackoff(mockFn, 3, 10, classifyError)

      expect(result).toBe('immediate success')
      expect(attempts).toBe(1)
    })
  })
})

describe('Error Handling Integration', () => {
  it('should validate enhanced error handling is integrated', () => {
    // Read the jobProcessor file to verify integration
    const fs = require('fs')
    const path = require('path')
    const filePath = path.join(__dirname, '../app/services/jobProcessor.server.ts')
    const fileContent = fs.readFileSync(filePath, 'utf8')
    
    // Verify error classification is integrated
    expect(fileContent).toContain('classifyError')
    expect(fileContent).toContain('retryWithBackoff')
    expect(fileContent).toContain('handlePartialJobRecovery')
    expect(fileContent).toContain('handleDeadLetterJob')
    expect(fileContent).toContain('ErrorClassification')
    
    console.log('✅ Enhanced error handling successfully integrated')
  })
})
