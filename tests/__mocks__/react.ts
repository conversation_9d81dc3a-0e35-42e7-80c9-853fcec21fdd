import { vi } from 'vitest'

// Global React mock for all tests
export const createContext = vi.fn(() => ({ Provider: vi.fn(), Consumer: vi.fn() }))
export const useContext = vi.fn()
export const useState = vi.fn()
export const useEffect = vi.fn()
export const useCallback = vi.fn((fn) => fn)
export const useMemo = vi.fn((fn) => fn())
export const useRef = vi.fn(() => ({ current: null }))
export const Component = vi.fn()
export const createElement = vi.fn()

export default {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  Component,
  createElement,
}
