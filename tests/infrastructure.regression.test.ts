import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { exec } from 'child_process'
import { promisify } from 'util'
import net from 'net'

const execAsync = promisify(exec)

describe('Infrastructure Regression Tests', () => {
  describe('Docker Compose Configuration', () => {
    it('should have valid docker-compose.yml file', async () => {
      try {
        const { stdout } = await execAsync('docker-compose config')
        expect(stdout).toContain('redis')
        expect(stdout).toContain('redis-commander')
      } catch (error) {
        throw new Error(`docker-compose.yml validation failed: ${error}`)
      }
    })

    it('should define correct Redis service configuration', async () => {
      const { stdout } = await execAsync('docker-compose config')
      expect(stdout).toContain('image: redis:7-alpine')
      expect(stdout).toContain('published: "6379"')
      expect(stdout).toContain('container_name: bpe-redis')
    })

    it('should define correct Redis Commander configuration', async () => {
      const { stdout } = await execAsync('docker-compose config')
      expect(stdout).toContain('image: rediscommander/redis-commander')
      expect(stdout).toContain('published: "8081"')
      expect(stdout).toContain('container_name: bpe-redis-commander')
    })
  })

  describe('NPM Scripts Validation', () => {
    it('should have all required dependency management scripts', async () => {
      const { stdout } = await execAsync('cat package.json')
      const packageJson = JSON.parse(stdout)

      expect(packageJson.scripts).toHaveProperty('deps:start')
      expect(packageJson.scripts).toHaveProperty('deps:stop')
      expect(packageJson.scripts).toHaveProperty('deps:check')
    })

    it('should have Redis monitoring scripts', async () => {
      const { stdout } = await execAsync('cat package.json')
      const packageJson = JSON.parse(stdout)

      expect(packageJson.scripts).toHaveProperty('redis:logs')
      expect(packageJson.scripts).toHaveProperty('redis:commander')
    })

    it('should have worker scripts', async () => {
      const { stdout } = await execAsync('cat package.json')
      const packageJson = JSON.parse(stdout)

      expect(packageJson.scripts).toHaveProperty('worker')
      expect(packageJson.scripts).toHaveProperty('worker:dev')
    })

    it('should have development scripts', async () => {
      const { stdout } = await execAsync('cat package.json')
      const packageJson = JSON.parse(stdout)

      expect(packageJson.scripts).toHaveProperty('dev:full')
      expect(packageJson.scripts).toHaveProperty('dev')
    })
  })

  describe('Redis Connection Testing', () => {
    let redisStarted = false
    let redisAvailable = false

    beforeAll(async () => {
      // First check if Redis is already running
      try {
        const Redis = (await import('ioredis')).default
        const testClient = new Redis({
          host: 'localhost',
          port: 6379,
          connectTimeout: 2000,
          lazyConnect: true
        })

        await testClient.connect()
        await testClient.ping()
        redisAvailable = true
        await testClient.disconnect()
        console.log('✅ Redis already running')
      } catch (error) {
        console.log('⚠️ Redis not running, attempting to start...')

        // Try to start Redis if not already running
        try {
          await execAsync('npm run deps:start', { timeout: 15000 })
          redisStarted = true

          // Wait for Redis to be ready with timeout
          const maxAttempts = 10
          let attempts = 0

          while (attempts < maxAttempts && !redisAvailable) {
            try {
              const Redis = (await import('ioredis')).default
              const testClient = new Redis({
                host: 'localhost',
                port: 6379,
                connectTimeout: 1000,
                lazyConnect: true
              })

              await testClient.connect()
              await testClient.ping()
              redisAvailable = true
              await testClient.disconnect()
              console.log('✅ Redis started successfully')
              break
            } catch (retryError) {
              attempts++
              if (attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 1000))
              }
            }
          }

          if (!redisAvailable) {
            console.warn('⚠️ Redis failed to start after multiple attempts')
          }
        } catch (startError) {
          console.warn('⚠️ Could not start Redis for testing:', startError)
        }
      }
    }, 60000) // Increase timeout to 60 seconds for slow CI environments

    afterAll(async () => {
      // Clean up only if we started Redis
      if (redisStarted) {
        try {
          await execAsync('npm run deps:stop', { timeout: 10000 })
          console.log('✅ Redis stopped')
        } catch (error) {
          console.warn('⚠️ Could not stop Redis:', error)
        }
      }
    }, 15000)

    it('should be able to connect to Redis on port 6379', async () => {
      if (!redisAvailable) {
        console.warn('⚠️ Skipping Redis connection test - Redis not available')
        return
      }

      const canConnect = await new Promise<boolean>((resolve) => {
        const client = net.createConnection(6379, 'localhost', () => {
          client.end()
          resolve(true)
        })
        client.on('error', () => {
          resolve(false)
        })
        client.setTimeout(5000, () => {
          client.destroy()
          resolve(false)
        })
      })

      expect(canConnect).toBe(true)
      console.log('✅ Redis connection test passed')
    })

    it('should have Redis Commander accessible on port 8081', async () => {
      if (!redisAvailable) {
        console.warn('⚠️ Skipping Redis Commander test - Redis not available')
        return
      }

      const canConnect = await new Promise<boolean>((resolve) => {
        const client = net.createConnection(8081, 'localhost', () => {
          client.end()
          resolve(true)
        })
        client.on('error', () => {
          resolve(false)
        })
        client.setTimeout(5000, () => {
          client.destroy()
          resolve(false)
        })
      })

      if (canConnect) {
        expect(canConnect).toBe(true)
        console.log('✅ Redis Commander connection test passed')
      } else {
        console.warn('⚠️ Redis Commander not accessible on port 8081 - this is optional for development')
        // Don't fail the test, Redis Commander is optional
      }
    })

    it('deps:check script should work correctly', async () => {
      try {
        const { stdout } = await execAsync('npm run deps:check', { timeout: 10000 })
        if (redisAvailable) {
          expect(stdout).toContain('✅ Redis is running')
          console.log('✅ deps:check script passed')
        } else {
          expect(stdout).toContain('⚠️ Redis not running')
          console.log('⚠️ deps:check correctly detected Redis not running')
        }
      } catch (error) {
        // If Redis is not running, deps:check should exit with code 1
        const errorMessage = (error as Error).message
        if (errorMessage.includes('⚠️ Redis not running') || errorMessage.includes('ECONNREFUSED')) {
          console.warn('⚠️ deps:check failed as expected - Redis not running')
          expect(errorMessage).toMatch(/Redis not running|ECONNREFUSED/)
        } else {
          throw error
        }
      }
    })
  })

  describe('Worker Script Validation', () => {
    it('should have TypeScript worker file', async () => {
      try {
        await execAsync('ls workers/worker.server.ts')
      } catch (error) {
        throw new Error('workers/worker.server.ts file not found')
      }
    })

    it('TypeScript worker should be valid', async () => {
      try {
        // Test that TypeScript worker compiles without errors
        const { stderr } = await execAsync('npx tsc --noEmit workers/worker.server.ts')
        // If no errors, stdout/stderr should be empty or contain no error messages
        expect(stderr).not.toContain('error')
      } catch (error) {
        const errorMessage = (error as Error).message
        // Should not have TypeScript compilation errors
        expect(errorMessage).not.toContain('error TS')
      }
    })
  })

  describe('Environment Configuration', () => {
    it('should have .env.example file', async () => {
      try {
        await execAsync('ls .env.example')
      } catch (error) {
        throw new Error('.env.example file not found')
      }
    })

    it('.env.example should contain Redis configuration', async () => {
      const { stdout } = await execAsync('cat .env.example')
      expect(stdout).toContain('REDIS_URL')
      expect(stdout).toContain('REDIS_HOST')
      expect(stdout).toContain('REDIS_PORT')
      expect(stdout).toContain('BULL_REDIS_URL')
    })
  })

  describe('Documentation Consistency', () => {
    it('DEVELOPMENT.md should reference existing scripts', async () => {
      const { stdout: devDoc } = await execAsync('cat DEVELOPMENT.md')
      const { stdout: packageJsonStr } = await execAsync('cat package.json')

      const packageJson = JSON.parse(packageJsonStr)
      const availableScripts = Object.keys(packageJson.scripts)

      // Check that DEVELOPMENT.md only references scripts that exist
      const scriptReferences = devDoc.match(/npm run [\w:]+/g) || []

      for (const ref of scriptReferences) {
        const scriptName = ref.replace('npm run ', '')
        if (!['test', 'build', 'start'].includes(scriptName)) { // Skip common scripts
          expect(availableScripts).toContain(scriptName)
        }
      }
    })

    it('README.md should reference existing scripts', async () => {
      const { stdout: readme } = await execAsync('cat README.md')
      const { stdout: packageJsonStr } = await execAsync('cat package.json')

      const packageJson = JSON.parse(packageJsonStr)
      const availableScripts = Object.keys(packageJson.scripts)

      // Check that README.md only references scripts that exist
      const scriptReferences = readme.match(/npm run [\w:]+/g) || []

      for (const ref of scriptReferences) {
        const scriptName = ref.replace('npm run ', '')
        if (!['test', 'build', 'start'].includes(scriptName)) {
          expect(availableScripts).toContain(scriptName)
        }
      }
    })
  })

  describe('Bull Dependencies', () => {
    it('should have Bull and Redis dependencies installed', async () => {
      const { stdout } = await execAsync('cat package.json')
      const packageJson = JSON.parse(stdout)
      
      expect(packageJson.dependencies).toHaveProperty('bull')
      expect(packageJson.dependencies).toHaveProperty('redis')
      expect(packageJson.dependencies).toHaveProperty('ioredis')
      expect(packageJson.devDependencies).toHaveProperty('@types/bull')
    })

    it('should be able to import Bull without errors', async () => {
      try {
        await execAsync('node -e "const Bull = require(\'bull\'); console.log(\'Bull imported successfully\')"')
      } catch (error) {
        throw new Error(`Bull import failed: ${error}`)
      }
    })

    it('should be able to import Redis clients without errors', async () => {
      try {
        await execAsync('node -e "const redis = require(\'redis\'); const Redis = require(\'ioredis\'); console.log(\'Redis clients imported successfully\')"')
      } catch (error) {
        throw new Error(`Redis clients import failed: ${error}`)
      }
    })
  })
})
