import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { setupIsolatedDb } from './test-db-strategies'
import { PrismaClient } from '@prisma/client'
import { getProductsFromShopify } from '~/data/graphql/getProducts'

// Mock Shopify admin GraphQL response
const mockGraphQLResponse = {
  json: vi.fn()
}

const mockAdmin = {
  graphql: vi.fn().mockResolvedValue(mockGraphQLResponse)
}

describe('GetProducts Regression Tests', () => {
  let prisma: PrismaClient
  let cleanup: () => Promise<void>

  beforeEach(async () => {
    const setup = await setupIsolatedDb()
    prisma = setup.prisma
    cleanup = setup.cleanup

    // Reset mocks completely
    vi.clearAllMocks()
    mockAdmin.graphql.mockClear()
    mockGraphQLResponse.json.mockClear()

    // Reset to default successful behavior
    mockAdmin.graphql.mockResolvedValue(mockGraphQLResponse)
    mockGraphQLResponse.json.mockResolvedValue({
      data: { products: { pageInfo: {}, nodes: [] } }
    })
  })

  afterEach(async () => {
    await cleanup()
  })

  describe('getProductsFromShopify', () => {
    it('should build correct GraphQL query with basic search', async () => {
      const mockProducts = {
        pageInfo: {
          hasNextPage: true,
          hasPreviousPage: false,
          startCursor: 'start123',
          endCursor: 'end123'
        },
        nodes: [
          {
            id: 'gid://shopify/Product/123',
            title: 'Test Product',
            status: 'ACTIVE',
            totalInventory: 10,
            vendor: 'Test Vendor',
            category: { name: 'Electronics' },
            images: { edges: [] },
            variants: { 
              nodes: [
                {
                  id: 'gid://shopify/ProductVariant/456',
                  title: 'Default Title',
                  sku: 'TEST-SKU',
                  price: '19.99',
                  inventoryQuantity: 10,
                  selectedOptions: []
                }
              ],
              pageInfo: { hasNextPage: false, hasPreviousPage: false, startCursor: null, endCursor: null }
            }
          }
        ]
      }

      mockGraphQLResponse.json.mockResolvedValue({
        data: { products: mockProducts }
      })

      const result = await getProductsFromShopify({
        admin: mockAdmin,
        searchQuery: 'test product',
        pageSize: 10
      })

      // Verify GraphQL was called with correct query
      expect(mockAdmin.graphql).toHaveBeenCalledTimes(1)
      const [query, options] = mockAdmin.graphql.mock.calls[0]
      
      // Check query structure
      expect(query).toContain('query getProducts($query: String, $first: Int!, $after: String)')
      expect(query).toContain('products(first: $first, after: $after, query: $query)')
      expect(query).toContain('pageInfo')
      expect(query).toContain('nodes')
      expect(query).toContain('variants(first: 50)')

      // Check variables
      expect(options.variables).toEqual({
        after: null,
        query: 'test product',
        first: 10
      })

      // Check result
      expect(result).toEqual(mockProducts)
    })

    it('should build query with option filters', async () => {
      const mockProducts = {
        pageInfo: { hasNextPage: false, hasPreviousPage: false, startCursor: null, endCursor: null },
        nodes: []
      }

      mockGraphQLResponse.json.mockResolvedValue({
        data: { products: mockProducts }
      })

      await getProductsFromShopify({
        admin: mockAdmin,
        searchQuery: 'shirt',
        optionName: 'Size',
        optionValue: 'Large',
        pageSize: 5
      })

      const [, options] = mockAdmin.graphql.mock.calls[0]
      
      // Should combine search query with option filter
      expect(options.variables.query).toBe('shirt option:Size:Large')
      expect(options.variables.first).toBe(5)
    })

    it('should handle option filters without search query', async () => {
      const mockProducts = {
        pageInfo: { hasNextPage: false, hasPreviousPage: false, startCursor: null, endCursor: null },
        nodes: []
      }

      mockGraphQLResponse.json.mockResolvedValue({
        data: { products: mockProducts }
      })

      await getProductsFromShopify({
        admin: mockAdmin,
        optionName: 'Color',
        optionValue: 'Red'
      })

      const [, options] = mockAdmin.graphql.mock.calls[0]
      expect(options.variables.query).toBe('option:Color:Red')
    })

    it('should handle pagination with cursor', async () => {
      const mockProducts = {
        pageInfo: { hasNextPage: false, hasPreviousPage: true, startCursor: 'prev123', endCursor: 'next123' },
        nodes: []
      }

      mockGraphQLResponse.json.mockResolvedValue({
        data: { products: mockProducts }
      })

      await getProductsFromShopify({
        admin: mockAdmin,
        cursor: 'cursor123',
        pageSize: 20
      })

      const [, options] = mockAdmin.graphql.mock.calls[0]
      expect(options.variables.after).toBe('cursor123')
      expect(options.variables.first).toBe(20)
    })

    it('should use default values when no parameters provided', async () => {
      const mockProducts = {
        pageInfo: { hasNextPage: false, hasPreviousPage: false, startCursor: null, endCursor: null },
        nodes: []
      }

      mockGraphQLResponse.json.mockResolvedValue({
        data: { products: mockProducts }
      })

      await getProductsFromShopify({
        admin: mockAdmin
      })

      const [, options] = mockAdmin.graphql.mock.calls[0]
      expect(options.variables).toEqual({
        after: null,
        query: '',
        first: 10
      })
    })

    it('should handle GraphQL errors gracefully', async () => {
      mockGraphQLResponse.json.mockResolvedValue({
        errors: [{ message: 'GraphQL Error' }]
      })

      await expect(getProductsFromShopify({
        admin: mockAdmin,
        searchQuery: 'test'
      })).rejects.toThrow('GraphQL Error')
    })

    it('should handle network errors gracefully', async () => {
      mockAdmin.graphql.mockRejectedValue(new Error('Network Error'))

      await expect(getProductsFromShopify({
        admin: mockAdmin,
        searchQuery: 'test'
      })).rejects.toThrow('Network Error')
    })

    it('should handle malformed JSON response', async () => {
      mockGraphQLResponse.json.mockRejectedValue(new Error('Invalid JSON'))

      await expect(getProductsFromShopify({
        admin: mockAdmin,
        searchQuery: 'test'
      })).rejects.toThrow('Invalid JSON')
    })

    it('should trim whitespace from combined queries', async () => {
      const mockProducts = {
        pageInfo: { hasNextPage: false, hasPreviousPage: false, startCursor: null, endCursor: null },
        nodes: []
      }

      mockGraphQLResponse.json.mockResolvedValue({
        data: { products: mockProducts }
      })

      await getProductsFromShopify({
        admin: mockAdmin,
        searchQuery: '  ',
        optionName: 'Size',
        optionValue: 'Medium'
      })

      const [, options] = mockAdmin.graphql.mock.calls[0]
      expect(options.variables.query).toBe('option:Size:Medium')
    })

    it('should handle empty option values', async () => {
      const mockProducts = {
        pageInfo: { hasNextPage: false, hasPreviousPage: false, startCursor: null, endCursor: null },
        nodes: []
      }

      mockGraphQLResponse.json.mockResolvedValue({
        data: { products: mockProducts }
      })

      await getProductsFromShopify({
        admin: mockAdmin,
        searchQuery: 'test',
        optionName: 'Size',
        optionValue: ''
      })

      const [, options] = mockAdmin.graphql.mock.calls[0]
      expect(options.variables.query).toBe('test')
    })

    it('should verify all required GraphQL fields are requested', async () => {
      const mockProducts = {
        pageInfo: { hasNextPage: false, hasPreviousPage: false, startCursor: null, endCursor: null },
        nodes: []
      }

      mockGraphQLResponse.json.mockResolvedValue({
        data: { products: mockProducts }
      })

      await getProductsFromShopify({
        admin: mockAdmin
      })

      const [query] = mockAdmin.graphql.mock.calls[0]
      
      // Verify all critical fields are requested
      expect(query).toContain('id')
      expect(query).toContain('title')
      expect(query).toContain('status')
      expect(query).toContain('totalInventory')
      expect(query).toContain('vendor')
      expect(query).toContain('category')
      expect(query).toContain('images(first: 1)')
      expect(query).toContain('variants(first: 50)')
      expect(query).toContain('selectedOptions')
      expect(query).toContain('sku')
      expect(query).toContain('price')
      expect(query).toContain('inventoryQuantity')
    })
  })
})
