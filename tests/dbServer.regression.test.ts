import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'

// Mock PrismaClient before importing
const mockPrismaClient = {
  $connect: vi.fn(),
  $disconnect: vi.fn(),
  $executeRaw: vi.fn(),
  $executeRawUnsafe: vi.fn(),
  $queryRaw: vi.fn(),
  $queryRawUnsafe: vi.fn(),
  $transaction: vi.fn(),
  $on: vi.fn(),
  $use: vi.fn(),
  $extends: vi.fn(),
  user: { findMany: vi.fn() },
  job: { findMany: vi.fn() },
  tempSelection: { findMany: vi.fn() }
} as any

vi.mock('@prisma/client', () => ({
  PrismaClient: vi.fn().mockImplementation(() => mockPrismaClient)
}))

/**
 * DATABASE SERVER REGRESSION TESTS
 *
 * These tests verify the Proxy pattern implementation in app/db.server.ts
 *
 * TESTING STRATEGY:
 * - Tests focus on BEHAVIOR, not implementation details
 * - Function calls are verified rather than reference equality
 * - This approach is more maintainable and works with the Proxy pattern
 *
 * WHY BEHAVIOR TESTING:
 * - The Proxy creates bound functions, so reference equality fails
 * - Behavior testing verifies the Proxy actually calls the underlying client
 * - More resilient to refactoring and implementation changes
 *
 * 📚 **See**: docs/current-status-and-next-steps.md - "Database Testing Strategy" section
 */
describe('Database Server Configuration Regression Tests', () => {
  let originalNodeEnv: string | undefined
  let originalGlobal: any

  beforeEach(() => {
    // Store original values
    originalNodeEnv = process.env.NODE_ENV
    originalGlobal = (global as any).prismaGlobal

    // Clear global prisma
    ;(global as any).prismaGlobal = undefined

    // Clear module cache to ensure fresh imports
    vi.resetModules()
    vi.clearAllMocks()
  })

  afterEach(() => {
    // Restore original values
    process.env.NODE_ENV = originalNodeEnv as any
    ;(global as any).prismaGlobal = originalGlobal
  })

  describe('development environment behavior', () => {
    it('should create global prisma client in development', async () => {
      process.env.NODE_ENV = 'development'

      const { PrismaClient } = await import('@prisma/client')

      // Import db.server to trigger initialization
      const db = await import('~/db.server')

      // Should create a new PrismaClient
      expect(PrismaClient).toHaveBeenCalledTimes(1)
      // The proxy should delegate to the mock client - test functionality, not reference equality
      expect(typeof db.default.$connect).toBe('function')
      expect((global as any).prismaGlobal).toBe(mockPrismaClient)

      // Test that the proxy actually calls the underlying client
      await db.default.$connect()
      expect(mockPrismaClient.$connect).toHaveBeenCalled()
    })

    it('should reuse existing global prisma client in development', async () => {
      process.env.NODE_ENV = 'development'

      // Set up existing global client
      ;(global as any).prismaGlobal = mockPrismaClient

      const { PrismaClient } = await import('@prisma/client')

      // Import db.server
      const db = await import('~/db.server')

      // Should NOT create a new PrismaClient, should reuse existing
      expect(PrismaClient).not.toHaveBeenCalled()
      // The proxy should delegate to the existing mock client - test functionality
      expect(typeof db.default.$connect).toBe('function')
      expect((global as any).prismaGlobal).toBe(mockPrismaClient)

      // Test that the proxy actually calls the underlying client
      await db.default.$connect()
      expect(mockPrismaClient.$connect).toHaveBeenCalled()
    })

    it('should handle test environment like development', async () => {
      process.env.NODE_ENV = 'test'

      const { PrismaClient } = await import('@prisma/client')

      const db = await import('~/db.server')

      expect(PrismaClient).toHaveBeenCalledTimes(1)
      // The proxy should delegate to the mock client - test functionality
      expect(typeof db.default.$connect).toBe('function')
      expect((global as any).prismaGlobal).toBe(mockPrismaClient)

      // Test that the proxy actually calls the underlying client
      await db.default.$connect()
      expect(mockPrismaClient.$connect).toHaveBeenCalled()
    })
  })

  describe('production environment behavior', () => {
    it('should create new prisma client in production', async () => {
      process.env.NODE_ENV = 'production'

      const { PrismaClient } = await import('@prisma/client')

      const db = await import('~/db.server')

      // In production, the proxy creates a new client when accessed
      // Access a property to trigger the proxy
      expect(typeof db.default.$connect).toBe('function')
      expect(PrismaClient).toHaveBeenCalledTimes(1)

      // Test that the proxy actually calls the underlying client
      await db.default.$connect()
      expect(mockPrismaClient.$connect).toHaveBeenCalled()

      // Should NOT set global in production
      expect((global as any).prismaGlobal).toBeUndefined()
    })

    it('should create new client in production without setting global', async () => {
      process.env.NODE_ENV = 'production'

      const { PrismaClient } = await import('@prisma/client')

      const db = await import('~/db.server')

      // Should create a new PrismaClient when accessed
      expect(typeof db.default.$connect).toBe('function')
      expect(PrismaClient).toHaveBeenCalled()

      // Test that the proxy actually calls the underlying client
      await db.default.$connect()
      expect(mockPrismaClient.$connect).toHaveBeenCalled()
    })
  })

  describe('global type declaration', () => {
    it('should properly extend global namespace', async () => {
      // This test verifies the TypeScript global declaration works
      process.env.NODE_ENV = 'development'
      
      await import('~/db.server')
      
      // Should be able to access global.prismaGlobal without TypeScript errors
      expect(typeof (global as any).prismaGlobal).toBe('object')
    })

    it('should handle undefined global prisma gracefully', async () => {
      process.env.NODE_ENV = 'production'

      // Ensure global is undefined
      ;(global as any).prismaGlobal = undefined

      const db = await import('~/db.server')

      // Should still work and create new client when accessed
      expect(typeof db.default.$connect).toBe('function')

      // Test that the proxy actually calls the underlying client
      await db.default.$connect()
      expect(mockPrismaClient.$connect).toHaveBeenCalled()
    })
  })

  describe('client initialization', () => {
    it('should export prisma client as default', async () => {
      const db = await import('~/db.server')
      
      expect(db.default).toBeDefined()
      expect(typeof db.default).toBe('object')
    })

    it('should create PrismaClient with no arguments', async () => {
      const { PrismaClient } = await import('@prisma/client')
      
      await import('~/db.server')
      
      expect(PrismaClient).toHaveBeenCalledWith()
    })

    it('should maintain singleton pattern in development', async () => {
      process.env.NODE_ENV = 'development'

      // Import multiple times
      const db1 = await import('~/db.server')

      vi.resetModules()

      // Set the global that was created by first import
      ;(global as any).prismaGlobal = mockPrismaClient

      const db2 = await import('~/db.server')

      // Both proxies should delegate to the same underlying client
      // Test functionality rather than reference equality
      expect(typeof db1.default.$connect).toBe('function')
      expect(typeof db2.default.$connect).toBe('function')

      // Test that both proxies call the same underlying client
      await db1.default.$connect()
      await db2.default.$connect()
      expect(mockPrismaClient.$connect).toHaveBeenCalledTimes(2)
    })
  })

  describe('error handling', () => {
    it('should handle PrismaClient constructor errors', async () => {
      const { PrismaClient } = await import('@prisma/client')
      
      // Mock PrismaClient to throw an error
      ;(PrismaClient as any).mockImplementationOnce(() => {
        throw new Error('Database connection failed')
      })
      
      // Should throw the error when importing
      await expect(import('~/db.server')).rejects.toThrow('Database connection failed')
    })

    it('should handle missing global gracefully', async () => {
      process.env.NODE_ENV = 'development'

      // Clear global prisma to test creation
      ;(global as any).prismaGlobal = undefined

      const { PrismaClient } = await import('@prisma/client')

      const db = await import('~/db.server')

      // Should create new client and set global when accessed
      expect(typeof db.default.$connect).toBe('function')
      expect(PrismaClient).toHaveBeenCalled()

      // Test that the proxy actually calls the underlying client
      await db.default.$connect()
      expect(mockPrismaClient.$connect).toHaveBeenCalled()
      expect((global as any).prismaGlobal).toBe(mockPrismaClient)
    })
  })

  describe('environment edge cases', () => {
    it('should handle undefined NODE_ENV', async () => {
      ;(process.env as any).NODE_ENV = undefined

      const { PrismaClient } = await import('@prisma/client')

      await import('~/db.server')

      // Should behave like development (create global)
      expect(PrismaClient).toHaveBeenCalledTimes(1)
      expect((global as any).prismaGlobal).toBe(mockPrismaClient)
    })

    it('should handle empty NODE_ENV', async () => {
      ;(process.env as any).NODE_ENV = ''

      const { PrismaClient } = await import('@prisma/client')

      await import('~/db.server')

      // Should behave like development (create global)
      expect(PrismaClient).toHaveBeenCalledTimes(1)
      expect((global as any).prismaGlobal).toBe(mockPrismaClient)
    })

    it('should handle custom NODE_ENV values', async () => {
      ;(process.env as any).NODE_ENV = 'staging'

      const { PrismaClient } = await import('@prisma/client')

      await import('~/db.server')

      // Should behave like development (not production)
      expect(PrismaClient).toHaveBeenCalledTimes(1)
      expect((global as any).prismaGlobal).toBe(mockPrismaClient)
    })
  })

  describe('module exports', () => {
    it('should export only default export', async () => {
      const db = await import('~/db.server')
      
      const exports = Object.keys(db)
      expect(exports).toEqual(['default'])
    })

    it('should export PrismaClient instance', async () => {
      const db = await import('~/db.server')

      // Should have typical PrismaClient properties/methods
      // The proxy should delegate to the mock client - test functionality
      expect(typeof db.default.$connect).toBe('function')
      expect(typeof db.default).toBe('object')

      // Test that the proxy actually calls the underlying client
      await db.default.$connect()
      expect(mockPrismaClient.$connect).toHaveBeenCalled()
    })
  })

  describe('memory management', () => {
    it('should not create multiple global clients in development', async () => {
      process.env.NODE_ENV = 'development'
      
      const { PrismaClient } = await import('@prisma/client')
      
      // First import
      await import('~/db.server')
      expect(PrismaClient).toHaveBeenCalledTimes(1)
      
      // Reset modules but keep global
      const savedGlobal = (global as any).prismaGlobal
      vi.resetModules()
      ;(global as any).prismaGlobal = savedGlobal
      
      // Second import should reuse
      await import('~/db.server')
      expect(PrismaClient).toHaveBeenCalledTimes(1) // Still only 1 call
    })

    it('should create new client each time in production', async () => {
      process.env.NODE_ENV = 'production'

      const { PrismaClient } = await import('@prisma/client')

      // First import - access a property to trigger client creation
      const db1 = await import('~/db.server')
      db1.default.$connect // Access to trigger creation
      expect(PrismaClient).toHaveBeenCalledTimes(1)

      vi.resetModules()

      // Second import should create new client when accessed
      const db2 = await import('~/db.server')
      db2.default.$connect // Access to trigger creation
      expect(PrismaClient).toHaveBeenCalledTimes(2)
    })
  })
})
