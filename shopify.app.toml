# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "18d03f0098582a9cfa94f98086213e79"
name = "bpe-remix"
handle = "bpe-remix"
application_url = "https://cp-fwd-syntax-tribal.trycloudflare.com"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products"

[auth]
redirect_urls = ["https://cp-fwd-syntax-tribal.trycloudflare.com/auth/callback", "https://cp-fwd-syntax-tribal.trycloudflare.com/auth/shopify/callback", "https://cp-fwd-syntax-tribal.trycloudflare.com/api/auth/callback"]

[pos]
embedded = false
