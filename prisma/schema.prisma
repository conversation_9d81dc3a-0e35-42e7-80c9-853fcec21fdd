// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

// Note that some adapters may set a maximum length for the String type by default, please ensure your strings are long
// enough when changing adapters.
// See https://www.prisma.io/docs/orm/reference/prisma-schema-reference#string for more information
datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      Boolean   @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  Boolean   @default(false)
  locale        String?
  collaborator  Boolean?  @default(false)
  emailVerified Boolean?  @default(false)
}

model TempSelection {
  id         String   @id @default(cuid())
  sessionKey String   @unique
  shopId     String
  data       String   // JSON stringified unselectedIds
  createdAt  DateTime @default(now())
  expiresAt  DateTime
}

model Job {
  id                String              @id @default(cuid())
  title             String
  description       String?
  shopId            String
  status            String              @default("SCHEDULED") // SCHEDULED, IN_PROGRESS, COMPLETED, FAILED, CANCELLED
  scheduledAt       DateTime?
  startedAt         DateTime?
  completedAt       DateTime?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Store filter criteria used to select products
  filterCriteria    String?             // JSON stringified filter data
  unselectedIds     String?             // JSON stringified array of unselected product/variant IDs

  // Progress tracking
  totalProducts     Int                 @default(0)
  processedProducts Int                 @default(0)
  successfulUpdates Int                 @default(0)
  failedUpdates     Int                 @default(0)

  // Relations
  modifications     JobModification[]
  productVariants   JobProductVariant[]
}

model JobModification {
  id          String @id @default(cuid())
  jobId       String
  fieldType   String // "product" or "variant"
  fieldName   String // "title", "description", "price", "sku", etc.
  fieldValue  String // The new value to set
  createdAt   DateTime @default(now())

  job         Job    @relation(fields: [jobId], references: [id], onDelete: Cascade)
}

model JobProductVariant {
  id                String   @id @default(cuid())
  jobId             String
  productId         String
  variantId         String?  // null for product-level changes
  status            String   @default("PENDING") // PENDING, SUCCESS, FAILED
  errorMessage      String?
  originalValues    String?  // JSON stringified original field values
  newValues         String?  // JSON stringified new field values
  processedAt       DateTime?
  createdAt         DateTime @default(now())

  job               Job      @relation(fields: [jobId], references: [id], onDelete: Cascade)
}
