# Development Guide

This guide covers how to develop the Shopify Bulk Product Editor app with all dependencies.

## 🚀 Quick Start (Recommended)

### Option 1: Full Development Setup (One Command)
```bash
npm run dev:full
```
This will:
1. Start Redis + Redis Commander
2. Check all dependencies are running
3. Start the Shopify app in development mode

### Option 2: Manual Step-by-Step
```bash
# 1. Start dependencies
npm run deps:start

# 2. Start the app
npm run dev

# 3. In another terminal, start the worker (optional)
npm run worker:dev
```

## 📋 Available Commands

### 🎯 **Primary Development Commands**

**What each command does:**
- `dev:full` = `deps:start` + `deps:check` + `shopify app dev`
- `dev` = `deps:check` + `shopify app dev`
- `worker` = `deps:check` + `node worker.js`
- `worker:dev` = `deps:start` + `deps:check` + `node worker.js`
```bash
# Start everything (dependencies + app) - RECOMMENDED
npm run dev:full

# Start app only (with dependency check)
npm run dev

# Start worker (with dependency check)
npm run worker

# Start worker with dependencies
npm run worker:dev
```

### 🔧 **Dependency Management**
```bash
# Start all dependencies (Redis + Redis Commander)
npm run deps:start
# → Runs: docker-compose up -d

# Stop all dependencies
npm run deps:stop
# → Runs: docker-compose down

# Check if dependencies are running
npm run deps:check
# → Tests Redis connection on localhost:6379
```

### 🔍 **Redis Inspection**
```bash
# Show Redis Commander URL
npm run redis:commander
# → Output: Redis Commander available at http://localhost:8081 (no login required)

# View Redis logs in real-time
npm run redis:logs
# → Runs: docker-compose logs -f redis
```

## 🏗️ **Development Workflow**

### 1. **First Time Setup**
```bash
# Install dependencies
npm install

# Set up environment
cp .env.example .env
# Edit .env with your Shopify app credentials

# Start full development environment
npm run dev:full
```

### 2. **Daily Development**
```bash
# Start everything
npm run dev:full

# Open Redis Commander to monitor job queues
# Visit: http://localhost:8081
```

### 3. **Working with Job Queues**
```bash
# In one terminal: Start the app
npm run dev:full

# In another terminal: Start the worker
npm run worker:dev

# Monitor jobs in Redis Commander
# Visit: http://localhost:8081
# Look for keys starting with "bull:"
```

## 🔍 **Monitoring & Debugging**

### Redis Job Queue Monitoring
1. **Open Redis Commander**: http://localhost:8081
2. **Browse shop-specific job keys**: Look for `bull:bulk-update-{shop-domain}:*`
   - Example: `bull:bulk-update-myshop-myshopify-com:waiting`
3. **Monitor job states per shop**:
   - `waiting` - Jobs queued for processing
   - `active` - Currently running jobs
   - `completed` - Successfully finished jobs
   - `failed` - Jobs that encountered errors
4. **Shop Isolation**: Each shop has its own queue for isolated processing
5. **Order Guarantee**: Jobs process in FIFO order within each shop

### Dependency Health Checks
```bash
# Check if all dependencies are running
npm run deps:check

# Check specific services
docker ps --filter name=bpe-redis
docker ps --filter name=bpe-redis-commander
```

## 🛠️ **Troubleshooting**

### Dependencies Not Running
```bash
# If you see: "⚠️ Redis not running"
npm run deps:start

# Check what's running
docker ps

# View logs if there are issues
npm run redis:logs
```

### Port Conflicts
If you get port conflicts:
- **Redis**: Port 6379 (change in docker-compose.yml)
- **Redis Commander**: Port 8081 (change in docker-compose.yml)
- **Shopify App**: Port varies (managed by Shopify CLI)

### Worker Issues
```bash
# Make sure Redis is running first
npm run deps:check

# Start worker with full logging
npm run worker:dev

# Check worker logs and Redis Commander for job status
# Worker auto-discovers existing shop queues on startup
```

## 📁 **Project Structure**

```
bpe-remix/
├── app/                    # Remix app code
├── worker.js              # Background job worker
├── docker-compose.yml     # Redis services
├── .env                   # Environment variables
├── package.json           # Scripts and dependencies
├── DEVELOPMENT.md         # This file
└── REDIS_INSPECTION.md    # Redis monitoring guide
```

## 🔄 **Development Lifecycle**

### Starting Development
1. `npm run dev:full` - Starts everything
2. Visit your Shopify app URL (shown in terminal)
3. Visit http://localhost:8081 for Redis monitoring

### Making Changes
1. App code changes auto-reload (Remix HMR)
2. Worker changes require restart: `Ctrl+C` then `npm run worker`
3. Redis data persists between restarts

### Stopping Development
```bash
# Stop the app: Ctrl+C in the dev terminal
# Stop dependencies:
npm run deps:stop
```

## 🧪 **Testing**

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- tests/specific-test.test.ts

# Run regression tests only
npm test -- tests/components/ProductVariantsTable.regression.test.ts tests/components/AdvancedFilterBuilder.regression.test.ts tests/data/getProductsCountFromShopify.test.ts tests/services/jobCancellationProgress.test.ts
```

### Regression Test Coverage

The project includes comprehensive regression tests to prevent the recurrence of production issues:

- **Data Layer Tests** (`tests/data/getProductsCountFromShopify.test.ts`) - 24 tests covering GraphQL error handling and network failures
- **Service Layer Tests** (`tests/services/jobCancellationProgress.test.ts`) - 18 tests covering job cancellation logic and race condition prevention
- **Component Logic Tests** - 42 tests covering advanced filter logic and product variant display enhancements
- **Total Regression Coverage** - 84 tests protecting critical production fixes

## 🎯 **Best Practices**

1. **Always use `npm run dev:full`** for new development sessions
2. **Monitor job queues** in Redis Commander during testing
3. **Check dependency health** if you encounter issues
4. **Use separate terminals** for app and worker when debugging
5. **Keep Redis Commander open** to watch job processing in real-time

## 🚨 **Common Issues**

### "Redis not running" Error
**Solution**: Run `npm run deps:start` before starting the app

### Jobs Not Processing
**Solution**: 
1. Check if worker is running: `npm run worker`
2. Check Redis Commander for job status
3. Look for error messages in worker logs

### Port Already in Use
**Solution**: 
1. Check what's using the port: `lsof -i :8081`
2. Stop conflicting services or change ports in docker-compose.yml

### Docker Issues
**Solution**:
```bash
# Reset Docker state
npm run deps:stop
docker system prune -f
npm run deps:start
```

## 📋 **Quick Reference**

### Essential Commands
```bash
npm run dev:full      # Start everything (recommended)
npm run deps:start    # Start Redis + Redis Commander
npm run deps:check    # Test Redis connection
npm run worker        # Start job worker
npm run redis:logs    # View Redis logs
```

### URLs
- **App**: Shown in terminal after `npm run dev:full`
- **Redis Commander**: http://localhost:8081
- **Prisma Studio**: `npx prisma studio`

This development setup ensures you never forget to start dependencies and provides a smooth development experience! 🚀
