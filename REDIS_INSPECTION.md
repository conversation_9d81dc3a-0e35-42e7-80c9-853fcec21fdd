# Redis Inspection Tool

This document outlines how to inspect and monitor Redis for your Shopify Bulk Product Editor app.

## 🎯 Redis Commander (GUI Tool) - **RECOMMENDED** ⭐

Redis Commander is a web-based Redis management tool that automatically connects to your Redis instance.

### Access
- **URL**: http://localhost:8081
- **Login**: No authentication required
- **Setup**: Fully automated - no configuration needed!

### Features
- **Automatic Connection**: Pre-configured to connect to your Redis instance
- **Real-time Browsing**: Navigate through keys and data structures
- **Command Interface**: Execute Redis commands directly
- **Data Editing**: Edit values inline
- **Export/Import**: Backup and restore data
- **Tree View**: Organized key browsing with namespace support

### Useful for Job Queue Monitoring
- View shop-specific Bull queue keys: `bull:bulk-update-{shop-domain}:*`
- Monitor job states per shop: `bull:bulk-update-myshop-myshopify-com:waiting`
- Check job data and progress for each shop
- Monitor failed jobs and error messages
- View Redis locks: `job-lock:*` (prevents duplicate processing)
- Shop isolation: Each shop has its own queue namespace

## 🖥️ Command Line Tools

### 1. Redis CLI (Direct Connection)
```bash
# Connect to Redis
docker exec -it bpe-redis redis-cli

# Or from host (if redis-cli is installed)
redis-cli -h localhost -p 6379
```

### 2. Basic Redis Commands
```bash
# List all keys
KEYS *

# List Bull queue keys
KEYS bull:*

# Get queue statistics
LLEN bull:bulk-update-jobs:waiting
LLEN bull:bulk-update-jobs:active
LLEN bull:bulk-update-jobs:completed
LLEN bull:bulk-update-jobs:failed

# View job data
HGETALL bull:bulk-update-jobs:1
HGETALL bull:bulk-update-jobs:2

# Monitor real-time commands
MONITOR

# Get Redis info
INFO
INFO memory
INFO stats

# Check memory usage
MEMORY USAGE <key>
```

### 3. Useful NPM Scripts
```bash
# Start Redis and Redis Commander
npm run deps:start

# Stop Redis services
npm run deps:stop

# View Redis logs
npm run redis:logs

# Show Redis Commander URL
npm run redis:commander
```

## 📊 Monitoring Job Queues

### Bull Queue Key Patterns (Shop-Specific)
- `bull:bulk-update-{shop-domain}:waiting` - Jobs waiting to be processed per shop
- `bull:bulk-update-{shop-domain}:active` - Currently processing jobs per shop
- `bull:bulk-update-{shop-domain}:completed` - Successfully completed jobs per shop
- `bull:bulk-update-{shop-domain}:failed` - Failed jobs per shop
- `bull:bulk-update-{shop-domain}:delayed` - Scheduled jobs per shop
- `bull:bulk-update-{shop-domain}:id` - Job ID counter per shop
- `bull:bulk-update-{shop-domain}:meta` - Queue metadata per shop
- `job-lock:*` - Redis locks preventing duplicate job processing

### Job Data Structure
Each job is stored as a hash with keys like:
- `data` - Job payload (JSON)
- `opts` - Job options
- `progress` - Job progress (0-100)
- `delay` - Delay before processing
- `timestamp` - Creation timestamp
- `attemptsMade` - Number of attempts
- `failedReason` - Error message (if failed)

### Example Queries
```bash
# Count jobs in each state for a specific shop
LLEN bull:bulk-update-myshop-myshopify-com:waiting
LLEN bull:bulk-update-myshop-myshopify-com:active
LLEN bull:bulk-update-myshop-myshopify-com:completed
LLEN bull:bulk-update-myshop-myshopify-com:failed

# View latest job for a shop
HGETALL bull:bulk-update-myshop-myshopify-com:1

# Get all job IDs for a shop
SMEMBERS bull:bulk-update-myshop-myshopify-com:id

# View all shop queues
KEYS bull:bulk-update-*

# Check Redis locks
KEYS job-lock:*

# Monitor queue activity
MONITOR
```

## 🔧 Alternative Tools

### 1. Bull Dashboard (Web UI for Bull Queues)
If you want a specialized Bull queue dashboard:
```bash
npm install bull-board
```

### 2. RedisInsight Desktop App
Download from: https://redis.com/redis-enterprise/redis-insight/

### 3. Redis CLI (Command Line)
For advanced users who prefer command line access

## 🚀 Quick Start Commands

```bash
# Start Redis and Redis Commander
npm run deps:start

# View Redis Commander
npm run redis:commander
# Output: Redis Commander available at http://localhost:8081 (no login required)

# Start worker to see jobs in action
npm run worker

# In another terminal, create some test jobs
# (through your app UI or test scripts)

# Monitor in Redis Commander:
# 1. Visit http://localhost:8081
# 2. Instantly see your Redis data (no login needed)
# 3. Browse keys starting with "bull:"
# 4. View job data and queue states in real-time
```

## 📝 Production Notes

For production environments:
- Use Redis Cloud, AWS ElastiCache, or similar managed Redis
- Set up proper authentication and SSL
- Configure Redis Insight with production credentials
- Use Redis monitoring tools like RedisInsight, Datadog, or New Relic
- Set up alerts for queue depth and failed jobs

## 🔍 Troubleshooting

### Redis Connection Issues
```bash
# Check if Redis is running
docker ps | grep redis

# Check Redis logs
docker logs bpe-redis

# Test connection
docker exec -it bpe-redis redis-cli ping
```

### Redis Commander Issues
```bash
# Check Redis Commander logs
docker logs bpe-redis-commander

# Restart Redis Commander
docker restart bpe-redis-commander
```

### Queue Issues
```bash
# Clear all queues (DANGER: Only for development)
redis-cli FLUSHALL

# Clear specific queue
redis-cli DEL bull:bulk-update-jobs:waiting
redis-cli DEL bull:bulk-update-jobs:active
```
