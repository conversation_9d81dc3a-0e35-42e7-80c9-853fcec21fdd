# Thread Completion & Handoff Rule

## TRIGGER
Execute when:
- Completing a conversation thread
- Before starting a new thread for next task
- When requested explicitly by user
- At end of implementation phase

## PROCESS
Perform comprehensive thread analysis and handoff preparation:

### 1. COMPREHENSIVE THREAD ANALYSIS
**Review ALL messages in conversation thread:**
- Analyze every agent message from start to finish
- Identify all tasks attempted and completed
- Track all files modified, created, or deleted
- Document all issues found and fixed
- Note all decisions made and rationale
- Capture all test results and metrics

### 2. FILE CHANGE ANALYSIS
**Examine all changed files:**
- Use `git status` or file system to identify changed files
- Review each file's modifications in detail
- Understand the purpose and impact of each change
- Document the relationship between changes
- Verify changes align with stated objectives

### 3. COMPREHENSIVE GIT COMMIT MESSAGE
**Generate detailed commit message following this format:**
```
<type>(<scope>): <brief summary>

<detailed description of what was accomplished>

## Files Changed:
- path/to/file1.ext: Description of changes and purpose
- path/to/file2.ext: Description of changes and purpose
- [continue for all changed files]

## Issues Fixed:
- Issue 1: Description and how it was resolved
- Issue 2: Description and how it was resolved
- [continue for all issues]

## Tests:
- X/Y tests passing (Z% coverage)
- New tests added: [list new test files/suites]
- Test improvements: [describe any test enhancements]

## Quality Metrics:
- TypeScript: X errors → 0 errors
- Security: [security improvements made]
- Performance: [performance optimizations]
- Architecture: [architectural improvements]

## Implementation Details:
- [Key technical decisions made]
- [Important patterns or approaches used]
- [Any trade-offs or considerations]

## Phase/Task Context:
- Current phase: [phase name/number]
- Task completed: [specific task from documentation]
- Next logical step: [what should come next]

Co-authored-by: Augment Code <https://www.augmentcode.com>
```

### 4. NEXT THREAD PROMPT GENERATION
**Create comprehensive handoff prompt:**
```
# CONTEXT HANDOFF: [Task/Phase Name]

## PREVIOUS THREAD SUMMARY
**What was accomplished:**
- [Detailed summary of all work completed]
- [All files modified and their purposes]
- [All issues resolved]
- [Current state of implementation]

**Key decisions made:**
- [Important technical decisions]
- [Architectural choices]
- [Implementation patterns established]

## CURRENT PROJECT STATE
**Phase:** [Current phase from documentation]
**Last completed task:** [Specific task just finished]
**Documentation status:** [Current state of docs]

**File structure context:**
- Key files: [list important files and their roles]
- Test coverage: [current test metrics]
- Quality status: [TypeScript, security, performance status]

## NEXT TASK OBJECTIVES
**Primary goal:** [Next task from documentation/plan]
**Specific requirements:**
- [Requirement 1 from docs]
- [Requirement 2 from docs]
- [Continue with all requirements]

**Files likely to be modified:**
- [Predicted files based on task requirements]
- [New files that may need creation]

**Testing requirements:**
- [Expected test coverage for next task]
- [Specific test scenarios to implement]

**Success criteria:**
- [How to measure completion of next task]
- [Quality gates that must be met]

## IMPLEMENTATION APPROACH
**Recommended strategy:** [Based on current architecture and patterns]
**Key considerations:** [Important factors to keep in mind]
**Potential challenges:** [Known issues or complexities ahead]

## REFERENCE DOCUMENTATION
**Primary docs to consult:**
- [List relevant MD files with brief descriptions]
- [Specific sections to focus on]

**Related previous work:**
- [Reference to patterns/approaches from this thread]
- [Reusable components or utilities available]

## QUALITY REQUIREMENTS
- Maintain 100% TypeScript compliance
- Ensure comprehensive test coverage
- Follow established security patterns
- Maintain performance standards
- Update documentation as needed

Execute this prompt in a fresh conversation thread to continue implementation with full context preservation.
```

### 5. VALIDATION CHECKLIST
Before providing output, verify:
- ✅ All conversation messages reviewed
- ✅ All file changes identified and understood
- ✅ Commit message captures complete scope of work
- ✅ Next prompt provides sufficient context
- ✅ Documentation references are accurate
- ✅ Implementation continuity is preserved

### 6. OUTPUT FORMAT
Provide both outputs clearly separated:
1. **GIT COMMIT MESSAGE** (ready to copy-paste)
2. **NEXT THREAD PROMPT** (ready to execute in new conversation)

## CONSTRAINTS
- ❌ Do NOT rely only on recent messages
- ❌ Do NOT miss any file changes
- ❌ Do NOT provide generic or incomplete commit messages
- ❌ Do NOT create handoff prompts without sufficient context
- ❌ Do NOT skip validation of documentation alignment

## SUCCESS CRITERIA
- Commit message accurately reflects ALL work done in thread
- Next prompt enables seamless continuation without context loss
- Both outputs demonstrate comprehensive understanding of project state
- Documentation and implementation remain aligned
