---
type: "manual"
---

# Inter-Phase Quality Gate Rule

## TRIG<PERSON><PERSON>
Execute this quality gate process:
- After completing any implementation phase/task
- Before moving to the next phase/stage
- When requested explicitly by user
- Before any major code changes

## PROCESS
Perform 10+ iterative quality checks with fixes:

### ITERATION LOOP (Minimum 10 cycles)
**ONE COMPLETE ITERATION = ALL 7 TASKS BELOW COMPLETED IN SEQUENCE:**

**For each iteration (1-10+), execute ALL of the following tasks:**

1. **CODE ANALYSIS**
   - Review all changed files for logic errors
   - Check for redundant/duplicate code
   - Verify implementation matches current phase requirements
   - Ensure no premature implementation of future phases

2. **DEPENDENCY VALIDATION**
   - Check all imports/exports are correct
   - Verify function signatures match usage
   - Validate type definitions consistency
   - Ensure proper error handling chains

3. **FULL TEST SUITE VERIFICATION**
   - Run FULL TEST suite: `npm test`, not individual tests.
   - Fix any failing tests immediately
   - Add missing test coverage for new code
   - Verify test logic matches implementation

4. **TYPESCRIPT COMPLIANCE**
   - Run diagnostics on all changed files
   - Fix all TypeScript errors/warnings
   - Ensure strict type safety
   - Validate interface consistency

5. **INTEGRATION CHECKS**
   - Verify component integration points
   - Check API contract compliance
   - Validate data flow between components
   - Ensure proper state management

6. **DOCUMENTATION SYNC**
   - Update relevant documentation
   - Sync code comments with implementation
   - Update API documentation if needed
   - Maintain phase/stage documentation

7. **CLEANUP VALIDATION**
   - Remove unnecessary files
   - Clean up unused imports/exports
   - Verify no dead code remains
   - Confirm all temporary files removed

**ITERATION COMPLETION:** Only when ALL 7 tasks above are completed, mark one iteration as done.

**REPEAT:** Execute the complete 7-task cycle 10+ times, finding and fixing issues in each iteration.

### COMPLETION CRITERIA
Only proceed to next phase when:
- ✅ All 10+ complete iterations (7 tasks × 10+ cycles) are finished
- ✅ Zero TypeScript errors across all iterations
- ✅ 100% test suite passing across all iterations. FULL TEST SUITE ONLY (npm test), not individual tests.
- ✅ No logic errors or redundancies found across all iterations
- ✅ Documentation fully updated and synchronized
- ✅ Implementation matches current phase scope
- ✅ All cleanups completed

### CONSTRAINTS
- ❌ NO git commits/pushes during quality gate
- ❌ NO implementation of future phase features
- ❌ NO skipping any of the 7 tasks within an iteration
- ❌ NO counting partial task completion as a full iteration
- ❌ NO moving forward with any failing tests

### ITERATION TRACKING
**Report format for each iteration:**