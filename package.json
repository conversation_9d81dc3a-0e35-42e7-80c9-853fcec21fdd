{"name": "bpe-remix", "private": true, "scripts": {"build": "remix vite:build", "dev": "npm run deps:check && shopify app dev", "dev:full": "npm run deps:start && npm run deps:check && shopify app dev", "config:link": "shopify app config link", "generate": "shopify app generate", "deploy": "shopify app deploy", "config:use": "shopify app config use", "env": "shopify app env", "start": "remix-serve ./build/server/index.js", "docker-start": "npm run setup && npm run start", "setup": "prisma generate && prisma migrate deploy", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "test": "TEST_MODE=all vitest run --coverage", "test:watch": "vitest", "test:cleanup": "npx tsx tests/cleanup.ts", "shopify": "shopify", "prisma": "prisma", "graphql-codegen": "graphql-codegen", "vite": "vite", "deps:start": "docker-compose up -d", "deps:stop": "docker-compose down", "deps:check": "node -e \"const net = require('net'); const client = net.createConnection(6379, 'localhost', () => { console.log('✅ Redis is running'); client.end(); }); client.on('error', () => { console.log('⚠️ Redis not running. Run: npm run deps:start'); process.exit(1); });\"", "redis:logs": "docker-compose logs -f redis", "redis:commander": "echo 'Redis Commander available at http://localhost:8081 (no login required)'", "worker": "npm run deps:check && tsx workers/worker.server.ts", "worker:dev": "npm run deps:start && npm run deps:check && tsx workers/worker.server.ts"}, "type": "module", "engines": {"node": "^18.20 || ^20.10 || >=21.0.0"}, "dependencies": {"@prisma/client": "^6.6.0", "@remix-run/dev": "^2.16.1", "@remix-run/fs-routes": "^2.16.1", "@remix-run/node": "^2.16.1", "@remix-run/react": "^2.16.1", "@remix-run/serve": "^2.16.1", "@shopify/app-bridge-react": "^4.1.6", "@shopify/polaris": "^v13.4.0", "@shopify/shopify-app-remix": "^3.7.0", "@shopify/shopify-app-session-storage-prisma": "^6.0.0", "bull": "^3.29.3", "dotenv": "^17.2.1", "ioredis": "^5.6.1", "isbot": "^5.1.0", "limiter": "^3.0.0", "prisma": "^6.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "redis": "^5.6.1", "vite-tsconfig-paths": "^5.0.1"}, "devDependencies": {"@remix-run/eslint-config": "^2.16.1", "@remix-run/route-config": "^2.16.1", "@shopify/api-codegen-preset": "^1.1.1", "@testing-library/react": "^16.3.0", "@types/bull": "^3.15.9", "@types/eslint": "^9.6.1", "@types/node": "^22.2.0", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "@types/supertest": "^6.0.3", "@vitest/coverage-v8": "^3.2.4", "eslint": "^8.42.0", "eslint-config-prettier": "^10.0.1", "prettier": "^3.2.4", "supertest": "^7.1.3", "tsx": "^4.20.3", "typescript": "^5.2.2", "vite": "^6.2.2", "vitest": "^3.2.4"}, "workspaces": ["extensions/*"], "trustedDependencies": ["@shopify/plugin-cloudflare"], "resolutions": {"@graphql-tools/url-loader": "8.0.16", "@graphql-codegen/client-preset": "4.7.0", "@graphql-codegen/typescript-operations": "4.5.0", "minimatch": "9.0.5"}, "overrides": {"@graphql-tools/url-loader": "8.0.16", "@graphql-codegen/client-preset": "4.7.0", "@graphql-codegen/typescript-operations": "4.5.0", "minimatch": "9.0.5"}, "author": "pm-dev"}