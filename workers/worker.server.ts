// Background job worker for Shopify Bulk Product Editor
import 'dotenv/config'
import <PERSON> from 'bull'
import Redis from 'ioredis'
import { processJob } from '../app/services/jobProcessor.server.js'

/**
 * Background Worker for Shopify Bulk Product Editor
 * 
 * This worker discovers existing shop queues and sets up job processors
 * to handle bulk product update jobs from the Bull/Redis queue system.
 */

console.log('🚀 Job Worker starting...')

// Test database connection
async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...')
  try {
    const db = await import('../app/db.server.js')
    const sessionCount = await db.default.session.count()
    console.log(`✅ Database connected successfully. Session count: ${sessionCount}`)
  } catch (error) {
    console.error('❌ Database connection failed:', (error as Error).message)
    console.error('Full error:', error)
  }
}

// Redis client for queue discovery
const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  enableReadyCheck: false,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  reconnectOnError: (err) => {
    const targetError = 'READONLY'
    return err.message.includes(targetError)
  },
})

// Add error handling for Redis connection
redis.on('error', (err) => {
  console.error('🔴 Redis connection error:', err.message)
})

redis.on('connect', () => {
  console.log('🟢 Redis connected successfully')
})

redis.on('ready', () => {
  console.log('🟢 Redis ready for commands')
})

redis.on('close', () => {
  console.log('🟡 Redis connection closed')
})

redis.on('reconnecting', () => {
  console.log('🟡 Redis reconnecting...')
})

// Store active queues for graceful shutdown
const activeQueues = new Map<string, Bull.Queue>()

/**
 * Discover existing shop queues from Redis
 */
export async function discoverShopQueues(): Promise<string[]> {
  try {
    const keys = await redis.keys('bull:bpe-*')
    const shopDomains = new Set<string>()

    // Ensure keys is iterable (handle cases where Redis returns null/undefined)
    if (!keys || !Array.isArray(keys)) {
      console.warn('⚠️ Redis keys returned non-array value:', keys)
      return []
    }

    console.log('🔍 Raw Redis keys found:', keys)

    for (const key of keys) {
      // Extract shop domain from key like 'bull:bpe-ranjanclentmyshopifycom:waiting'
      const match = key.match(/bull:bpe-([^:]+)/)
      if (match) {
        // Convert sanitized domain back to original format
        const sanitizedDomain = match[1]
        // For now, we'll need to reconstruct the original domain
        // This is a limitation of the simplified naming - we lose the original format
        console.log(`🔍 Discovered queue: bpe-${sanitizedDomain} -> sanitized: ${sanitizedDomain}`)
        shopDomains.add(sanitizedDomain)
      } else {
        console.warn(`⚠️ Could not parse queue key: ${key}`)
      }
    }

    return Array.from(shopDomains)
  } catch (error) {
    console.error('❌ Error discovering shop queues:', error)
    return []
  }
}

/**
 * Set up job processors for discovered shop queues
 */
export async function setupJobProcessors(shopDomains: string[]): Promise<void> {
  console.log(`🔧 Setting up processors for ${shopDomains.length} shop queues`)

  for (const sanitizedDomain of shopDomains) {
    try {
      // Create queue name (same format as in jobQueue.server.ts)
      const queueName = `bpe-${sanitizedDomain}`
      
      /**
       * BULL QUEUE CONFIGURATION - SIMPLIFIED FOR RELIABILITY
       *
       * WHY SIMPLIFIED CONFIGURATION:
       * - Bull 4.x had complex Redis options that prevented processor registration
       * - Bull 3.x with simple config works reliably and immediately processes jobs
       * - Removed: enableReadyCheck, maxRetriesPerRequest, lazyConnect, reconnectOnError
       * - Result: Processors register correctly and jobs process immediately
       *
       * 📚 **See**: docs/current-status-and-next-steps.md - "Bull Queue Configuration Guide"
       * ⚠️ **CRITICAL**: Do not add complex Redis options without thorough testing!
       */
      const queue = new Bull(queueName, {
        redis: {
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT || '6379'),
          // INTENTIONALLY SIMPLE - complex options caused processor registration failures
        },
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      })

      // Store queue for shutdown
      activeQueues.set(queueName, queue)

      // Add queue event listeners for debugging
      queue.on('waiting', (jobId) => {
        console.log(`⏳ Job ${jobId} is waiting in queue: ${queueName}`)
      })

      queue.on('active', (job) => {
        console.log(`🚀 Job ${job.id} started processing in queue: ${queueName}`)
      })

      queue.on('completed', (job) => {
        console.log(`✅ Job ${job.id} completed in queue: ${queueName}`)
      })

      queue.on('failed', (job, err) => {
        console.log(`❌ Job ${job?.id} failed in queue: ${queueName}:`, err.message)
      })

      // Debug: Check waiting jobs in this queue
      const waitingJobs = await queue.getWaiting()
      console.log(`🔍 Queue ${queueName} has ${waitingJobs.length} waiting jobs`)
      if (waitingJobs.length > 0) {
        console.log(`🔍 First waiting job:`, {
          id: waitingJobs[0].id,
          name: waitingJobs[0].name,
          data: waitingJobs[0].data
        })
      }

      // Set up job processor (1 concurrent job per shop queue for FIFO ordering)
      console.log(`🔧 Setting up processor for queue: ${queueName}`)



      /**
       * JOB PROCESSOR SETUP - SIMPLIFIED FOR BULL 3.x
       *
       * IMPORTANT CHANGES FROM BULL 4.x:
       * - Removed concurrency parameter (was: queue.process('bulk-update', 1, fn))
       * - Bull 3.x format: queue.process('bulk-update', fn)
       * - This change was CRITICAL for processor registration to work
       *
       * WHY NO CONCURRENCY PARAMETER:
       * - Bull 3.x handles concurrency differently than Bull 4.x
       * - Omitting the parameter uses Bull's default concurrency (1)
       * - Adding it back caused processor registration failures
       */
      queue.process('bulk-update', async (job) => {
        const { jobId, shopDomain, modifications, productIds, options } = job.data
        const startTime = Date.now()

        console.log(`🔄 Processing job ${jobId} for shop ${shopDomain}`, {
          productCount: productIds?.length || 0,
          modificationsCount: modifications?.length || 0,
          batchSize: options?.batchSize || 'default',
        })

        // Check if this is a test job
        if (jobId?.startsWith('test-job-')) {
          console.log(`🧪 Processing test job - returning success immediately`)
          return {
            success: true,
            jobId,
            processedProducts: 1,
            successfulUpdates: 1,
            failedUpdates: 0,
            retryableFailures: 0,
            permanentFailures: 0
          }
        }

        try {
          const result = await processJob(job.data)
          const duration = Date.now() - startTime
          const successRate = result.processedProducts > 0
            ? ((result.successfulUpdates / result.processedProducts) * 100).toFixed(1)
            : '0'

          console.log(`✅ Job ${jobId} completed successfully in ${duration}ms:`, {
            processedProducts: result.processedProducts,
            successfulUpdates: result.successfulUpdates,
            failedUpdates: result.failedUpdates,
            retryableFailures: result.retryableFailures || 0,
            permanentFailures: result.permanentFailures || 0,
            successRate: `${successRate}%`,
            avgTimePerProduct: result.processedProducts > 0
              ? `${(duration / result.processedProducts).toFixed(0)}ms`
              : 'N/A'
          })
          return result
        } catch (error) {
          const duration = Date.now() - startTime
          const attemptNumber = job.attemptsMade + 1

          console.error(`❌ Job ${jobId} failed after ${duration}ms (attempt ${attemptNumber}):`, (error as Error).message)

          // Import error handling functions
          const { shouldMoveToDeadLetter, handleDeadLetterJob, classifyError } = await import('../app/services/jobProcessor.server')

          // Check if this should be moved to dead letter queue
          if (shouldMoveToDeadLetter(error as Error, attemptNumber)) {
            console.warn(`🚨 Moving job ${jobId} to dead letter queue after ${attemptNumber} attempts`)
            await handleDeadLetterJob(jobId, error as Error, attemptNumber)
          } else {
            // Classify error for better logging
            const classification = classifyError(error as Error)
            console.warn(`🔄 Job ${jobId} will be retried (error category: ${classification.category}, retryable: ${classification.isRetryable})`)
          }

          throw error
        }

        /* COMMENTED OUT FOR TESTING
        const { jobId, shopDomain, modifications, productIds, options } = job.data
        const startTime = Date.now()

        console.log(`🔄 Processing job ${jobId} for shop ${shopDomain}`, {
          productCount: productIds?.length || 0,
          modificationsCount: modifications?.length || 0,
          batchSize: options?.batchSize || 'default',
        })

        // Check if this is a test job
        if (jobId?.startsWith('test-job-')) {
          console.log(`🧪 Processing test job - returning success immediately`)
          return {
            success: true,
            jobId,
            processedProducts: 1,
            successfulUpdates: 1,
            failedUpdates: 0,
            retryableFailures: 0,
            permanentFailures: 0
          }
        }

        try {
          const result = await processJob(job.data)
          const duration = Date.now() - startTime
          const successRate = result.processedProducts > 0
            ? ((result.successfulUpdates / result.processedProducts) * 100).toFixed(1)
            : '0'

          console.log(`✅ Job ${jobId} completed successfully in ${duration}ms:`, {
            processedProducts: result.processedProducts,
            successfulUpdates: result.successfulUpdates,
            failedUpdates: result.failedUpdates,
            retryableFailures: result.retryableFailures || 0,
            permanentFailures: result.permanentFailures || 0,
            successRate: `${successRate}%`,
            avgTimePerProduct: result.processedProducts > 0
              ? `${(duration / result.processedProducts).toFixed(0)}ms`
              : 'N/A'
          })
          return result
        } catch (error) {
          const duration = Date.now() - startTime
          const attemptNumber = job.attemptsMade + 1

          console.error(`❌ Job ${jobId} failed after ${duration}ms (attempt ${attemptNumber}):`, (error as Error).message)

          // Import error handling functions
          const { shouldMoveToDeadLetter, handleDeadLetterJob, classifyError } = await import('../app/services/jobProcessor.server')

          // Check if this should be moved to dead letter queue
          if (shouldMoveToDeadLetter(error as Error, attemptNumber)) {
            console.warn(`🚨 Moving job ${jobId} to dead letter queue after ${attemptNumber} attempts`)
            await handleDeadLetterJob(jobId, error as Error, attemptNumber)
          } else {
            // Classify error for better logging
            const classification = classifyError(error as Error)
            console.warn(`🔄 Job ${jobId} will be retried (error category: ${classification.category}, retryable: ${classification.isRetryable})`)
          }

          throw error
        }
        */
      })

      // Set up event listeners for monitoring
      queue.on('completed', (job) => {
        console.log(`✅ Job ${job.data.jobId} completed for shop ${job.data.shopDomain}`)
      })

      queue.on('failed', (job, err) => {
        console.error(`❌ Job ${job.data.jobId} failed for shop ${job.data.shopDomain}:`, err.message)
      })

      // Add Redis connection error handling for Bull queues
      queue.on('error', (err) => {
        console.error(`🔴 Queue ${queueName} error:`, err.message)
      })

      console.log(`✅ Processor set up for shop: ${sanitizedDomain}`)

    } catch (error) {
      console.error(`❌ Failed to set up processor for shop ${sanitizedDomain}:`, error)
    }
  }
}

/**
 * Graceful shutdown - close all queues
 */
export async function shutdown(): Promise<void> {
  console.log('🛑 Shutting down worker...')

  const closePromises = Array.from(activeQueues.values()).map(queue => queue.close())
  await Promise.all(closePromises)

  // Disconnect Redis client (automatically cleans up event listeners)
  redis.disconnect()
  console.log('👋 Worker shutdown complete')
}

// Main worker startup
async function startWorker(): Promise<void> {
  try {
    // Test database connection first
    await testDatabaseConnection()

    console.log('🔍 Discovering existing shop queues...')
    const shopDomains = await discoverShopQueues()
    
    if (shopDomains.length === 0) {
      console.log('📭 No existing shop queues found. Worker will wait for jobs...')
    } else {
      console.log(`📋 Found ${shopDomains.length} shop queues:`, shopDomains)
      await setupJobProcessors(shopDomains)
    }
    
    console.log('🚀 Worker is ready and listening for jobs!')
    console.log('🔗 Monitor jobs at: http://localhost:8081 (Redis Commander)')
  } catch (error) {
    console.error('💥 Worker startup failed:', error)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  await shutdown()
  process.exit(0)
})

process.on('SIGTERM', async () => {
  await shutdown()
  process.exit(0)
})

// Start the worker if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  startWorker()
}
