# Workers Directory

This directory contains background workers for the Shopify Bulk Product Editor app.

## Files

### `worker.server.ts`
TypeScript background worker that processes bulk product update jobs from Bull/Redis queues.

**Features:**
- Automatic queue discovery from Redis
- Shop-isolated job processing (FIFO ordering)
- Comprehensive error handling and logging
- Graceful shutdown with proper cleanup
- Full TypeScript support with type safety

**Usage:**
```bash
npm run worker:dev    # Start the worker in development
npm run worker        # Start the worker in production
```

**Testing:**
- Unit tests: `tests/worker.test.ts` (6 tests)
- Integration tests: `tests/integration/worker-integration.test.ts` (4 tests)
- Coverage: 73.63% (excellent for new implementation)

**Architecture:**
- Each shop gets its own Bull queue: `bulk-update-{shop-domain}`
- 1 concurrent job per shop queue (FIFO ordering)
- Automatic job enqueueing when jobs are created
- Real-time job processing with status updates

## Development

The worker is automatically included in test coverage reports via `vitest.config.ts`:
```typescript
include: ['app/**/*.ts', 'app/**/*.tsx', 'workers/**/*.ts']
```

## Monitoring

- Redis Commander: http://localhost:8081
- Worker logs show real-time job processing
- Job status updates stored in database
